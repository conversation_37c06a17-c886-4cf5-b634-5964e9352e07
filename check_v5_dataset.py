#!/usr/bin/env python3
"""
检查V5数据集的结构和内容
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[0]
sys.path.append(str(project_root))

import torch
import numpy as np
import pickle
import lmdb
from src.data.datasets import LMDBDataset
from src.utils.config_loader import load_config

def check_dataset_structure():
    """检查数据集结构"""
    print("=== 检查V5数据集结构 ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 检查数据集路径
    data_path = config.data_preprocessing.output_path
    print(f"数据集路径: {data_path}")
    
    # 检查训练集
    train_path = f"{data_path}/train"
    if os.path.exists(train_path):
        print(f"训练集路径存在: {train_path}")
        
        # 打开LMDB
        env = lmdb.open(train_path, readonly=True, lock=False)
        with env.begin() as txn:
            cursor = txn.cursor()
            sample_count = 0
            for key, _ in cursor:
                try:
                    key_int = int(key.decode())
                    sample_count += 1
                except ValueError:
                    continue
        
        print(f"训练集样本数: {sample_count}")
        env.close()
    else:
        print(f"训练集路径不存在: {train_path}")
    
    # 检查验证集
    val_path = f"{data_path}/val"
    if os.path.exists(val_path):
        print(f"验证集路径存在: {val_path}")
        
        # 打开LMDB
        env = lmdb.open(val_path, readonly=True, lock=False)
        with env.begin() as txn:
            cursor = txn.cursor()
            sample_count = 0
            for key, _ in cursor:
                try:
                    key_int = int(key.decode())
                    sample_count += 1
                except ValueError:
                    continue
        
        print(f"验证集样本数: {sample_count}")
        env.close()
    else:
        print(f"验证集路径不存在: {val_path}")

def check_sample_content():
    """检查样本内容"""
    print("\n=== 检查样本内容 ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    try:
        # 创建数据集
        dataset = LMDBDataset(config, lmdb_type='train', return_mask=True)
        print(f"数据集样本数: {len(dataset)}")
        
        if len(dataset) == 0:
            print("数据集为空")
            return
        
        # 检查第一个样本
        sample = dataset[0]
        print(f"\n第一个样本的键: {list(sample.keys())}")
        
        for key, value in sample.items():
            if isinstance(value, np.ndarray):
                print(f"{key}: shape={value.shape}, dtype={value.dtype}")
                if value.size > 0:
                    print(f"  范围: [{value.min():.4f}, {value.max():.4f}]")
                    print(f"  均值: {value.mean():.4f}")
                    print(f"  标准差: {value.std():.4f}")
            else:
                print(f"{key}: {type(value)} = {value}")
        
        # 检查历史特征维度
        history_features = sample['history_features']
        print(f"\n历史特征维度: {history_features.shape}")
        print(f"期望的输入维度: 9 (x, y, delta_t, vel_n, vel_e, cos_heading, sin_heading, acc_x, acc_y)")
        
        if history_features.shape[1] != 9:
            print(f"⚠️ 警告: 历史特征维度 {history_features.shape[1]} 与模型期望的 9 不匹配!")
        
        # 检查预测轨迹
        pred_trajectory = sample['ground_truth_trajectory']
        print(f"\n预测轨迹维度: {pred_trajectory.shape}")
        print(f"期望的预测点数: 120 (1200s ÷ 10s)")
        
        if pred_trajectory.shape[0] != 120:
            print(f"⚠️ 警告: 预测点数 {pred_trajectory.shape[0]} 与期望的 120 不匹配!")
        
        # 检查环境ROI
        env_roi = sample['environment_roi']
        print(f"\n环境ROI维度: {env_roi.shape}")
        
    except Exception as e:
        print(f"检查样本内容时出错: {e}")
        import traceback
        traceback.print_exc()

def check_normalization_stats():
    """检查归一化统计"""
    print("\n=== 检查归一化统计 ===")
    
    config = load_config('configs/main_config.yaml')
    stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    
    if os.path.exists(stats_path):
        with open(stats_path, 'rb') as f:
            stats = pickle.load(f)
        
        print("归一化统计数据的键:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
    else:
        print(f"归一化统计文件不存在: {stats_path}")

def main():
    """主函数"""
    check_dataset_structure()
    check_sample_content()
    check_normalization_stats()

if __name__ == "__main__":
    main() 