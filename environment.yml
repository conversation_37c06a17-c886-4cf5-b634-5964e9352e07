name: wargame
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - python=3.10
  - pip
  # --- PyTorch and CUDA ---
  # Installs PyTorch and a compatible CUDA toolkit inside the conda environment
  - pytorch::pytorch
  - pytorch::torchvision
  - pytorch::torchaudio
  - pytorch-cuda=11.8

  # --- Core Libraries ---
  - conda-forge::pyyaml          # For parsing .yaml config files
  - conda-forge::python-lmdb     # For reading the LMDB database
  - conda-forge::numpy           # Core scientific computing library
  - conda-forge::tqdm            # For progress bars
  - conda-forge::tensorboard     # For visualizing training runs
  
  # --- GIS Libraries (from dev manual) ---
  - conda-forge::rasterio        # For reading geospatial raster data
  - conda-forge::gdal            # Dependency for rasterio 