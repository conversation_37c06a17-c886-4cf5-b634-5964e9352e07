#!/usr/bin/env python3
"""
全面诊断数据预处理问题
"""
import sys
import os
sys.path.append('.')

import numpy as np
import pandas as pd
import pickle
import torch
from pathlib import Path

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset

def diagnose_normalization_stats():
    """诊断归一化统计数据"""
    print("=== 诊断归一化统计数据 ===")
    
    config = load_config('configs/main_config.yaml', 'configs/data_preprocessing.yaml')
    
    # 加载归一化统计数据
    normalization_stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    
    if not os.path.exists(normalization_stats_path):
        print(f"❌ 归一化统计文件不存在: {normalization_stats_path}")
        return None
    
    with open(normalization_stats_path, 'rb') as f:
        stats = pickle.load(f)
    
    print("归一化统计数据:")
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for subkey, subvalue in value.items():
                print(f"    {subkey}: {subvalue}")
        else:
            print(f"  {key}: {value}")
    
    # 检查统计数据是否合理
    print("\n合理性检查:")
    
    # 检查历史轨迹统计
    if 'history_mean' in stats and 'history_std' in stats:
        x_mean = stats['history_mean']['x']
        y_mean = stats['history_mean']['y']
        x_std = stats['history_std']['x']
        y_std = stats['history_std']['y']
        
        print(f"  历史轨迹X: 均值={x_mean:.2f}, 标准差={x_std:.2f}")
        print(f"  历史轨迹Y: 均值={y_mean:.2f}, 标准差={y_std:.2f}")
        
        # 检查是否异常
        if abs(x_mean) > 1000000 or abs(y_mean) > 1000000:
            print("  ⚠️  坐标均值异常大，可能是投影坐标系")
        if x_std > 100000 or y_std > 100000:
            print("  ⚠️  坐标标准差异常大")
        if x_std < 1 or y_std < 1:
            print("  ⚠️  坐标标准差异常小")
    
    # 检查真实轨迹统计
    if 'gt_trajectory_mean' in stats and 'gt_trajectory_std' in stats:
        gt_x_mean = stats['gt_trajectory_mean']['x']
        gt_y_mean = stats['gt_trajectory_mean']['y']
        gt_x_std = stats['gt_trajectory_std']['x']
        gt_y_std = stats['gt_trajectory_std']['y']
        
        print(f"  真实轨迹X: 均值={gt_x_mean:.2f}, 标准差={gt_x_std:.2f}")
        print(f"  真实轨迹Y: 均值={gt_y_mean:.2f}, 标准差={gt_y_std:.2f}")
    
    return stats

def check_raw_data_vs_processed():
    """检查原始数据vs处理后数据"""
    print("\n=== 检查原始数据vs处理后数据 ===")
    
    # 检查原始轨迹文件
    raw_traj_dir = "data/trajectories_with_env"
    if not os.path.exists(raw_traj_dir):
        print(f"❌ 原始轨迹目录不存在: {raw_traj_dir}")
        return
    
    # 随机选择一个轨迹文件
    traj_files = [f for f in os.listdir(raw_traj_dir) if f.endswith('.csv')]
    if not traj_files:
        print("❌ 没有找到轨迹文件")
        return
    
    sample_file = traj_files[0]
    print(f"分析样本文件: {sample_file}")
    
    # 读取原始数据
    raw_df = pd.read_csv(os.path.join(raw_traj_dir, sample_file))
    print(f"原始数据:")
    print(f"  点数: {len(raw_df)}")
    print(f"  X范围: [{raw_df['x'].min():.2f}, {raw_df['x'].max():.2f}]")
    print(f"  Y范围: [{raw_df['y'].min():.2f}, {raw_df['y'].max():.2f}]")
    # 检查是否有时间列
    time_cols = [col for col in raw_df.columns if 'time' in col.lower()]
    if time_cols:
        time_col = time_cols[0]
        print(f"  时间范围: [{raw_df[time_col].min():.2f}, {raw_df[time_col].max():.2f}]")
    else:
        print(f"  列名: {list(raw_df.columns)}")
    
    # 计算原始数据的统计
    raw_stats = {
        'x_mean': raw_df['x'].mean(),
        'x_std': raw_df['x'].std(),
        'y_mean': raw_df['y'].mean(),
        'y_std': raw_df['y'].std()
    }
    print(f"  原始统计: X均值={raw_stats['x_mean']:.2f}, X标准差={raw_stats['x_std']:.2f}")
    print(f"            Y均值={raw_stats['y_mean']:.2f}, Y标准差={raw_stats['y_std']:.2f}")
    
    return raw_stats, raw_df

def check_processed_data():
    """检查处理后的数据"""
    print("\n=== 检查处理后的数据 ===")
    
    config = load_config('configs/main_config.yaml', 'configs/models/v3_masked_autoregressive.yaml')
    
    # 创建数据集
    try:
        train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
        val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
        
        print(f"数据集大小: 训练={len(train_dataset)}, 验证={len(val_dataset)}")
        
        # 检查几个样本
        for i in range(min(3, len(val_dataset))):
            sample = val_dataset[i]
            if sample is not None:
                history = sample['history_features']
                gt_traj = sample['ground_truth_trajectory']
                mask = sample['history_mask']
                
                print(f"\n样本 {i}:")
                print(f"  历史特征形状: {history.shape}")
                print(f"  真实轨迹形状: {gt_traj.shape}")
                print(f"  掩码形状: {mask.shape}, 有效比例: {mask.float().mean():.3f}")
                
                # 检查数值范围
                if mask.sum() > 0:
                    valid_history = history[mask.bool()]
                    print(f"  有效历史数据:")
                    print(f"    X范围: [{valid_history[:, 0].min():.4f}, {valid_history[:, 0].max():.4f}]")
                    print(f"    Y范围: [{valid_history[:, 1].min():.4f}, {valid_history[:, 1].max():.4f}]")
                    if valid_history.shape[1] > 2:
                        print(f"    Delta_t范围: [{valid_history[:, 2].min():.4f}, {valid_history[:, 2].max():.4f}]")
                
                print(f"  真实轨迹:")
                print(f"    X范围: [{gt_traj[:, 0].min():.4f}, {gt_traj[:, 0].max():.4f}]")
                print(f"    Y范围: [{gt_traj[:, 1].min():.4f}, {gt_traj[:, 1].max():.4f}]")
                
    except Exception as e:
        print(f"❌ 加载处理后数据失败: {e}")
        import traceback
        traceback.print_exc()

def test_denormalization():
    """测试反归一化过程"""
    print("\n=== 测试反归一化过程 ===")
    
    config = load_config('configs/main_config.yaml', 'configs/models/v3_masked_autoregressive.yaml')
    
    # 加载归一化统计数据
    normalization_stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    with open(normalization_stats_path, 'rb') as f:
        stats = pickle.load(f)
    
    # 创建测试数据
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    sample = val_dataset[0]
    
    if sample is not None:
        gt_traj_norm = sample['ground_truth_trajectory']  # 归一化后的真实轨迹
        
        print(f"归一化后的轨迹:")
        print(f"  X范围: [{gt_traj_norm[:, 0].min():.4f}, {gt_traj_norm[:, 0].max():.4f}]")
        print(f"  Y范围: [{gt_traj_norm[:, 1].min():.4f}, {gt_traj_norm[:, 1].max():.4f}]")
        
        # 手动反归一化
        x_mean = stats['gt_trajectory_mean']['x']
        x_std = stats['gt_trajectory_std']['x']
        y_mean = stats['gt_trajectory_mean']['y']
        y_std = stats['gt_trajectory_std']['y']
        
        gt_traj_denorm = gt_traj_norm.clone()
        gt_traj_denorm[:, 0] = gt_traj_denorm[:, 0] * x_std + x_mean
        gt_traj_denorm[:, 1] = gt_traj_denorm[:, 1] * y_std + y_mean
        
        print(f"\n反归一化后的轨迹:")
        print(f"  X范围: [{gt_traj_denorm[:, 0].min():.2f}, {gt_traj_denorm[:, 0].max():.2f}]")
        print(f"  Y范围: [{gt_traj_denorm[:, 1].min():.2f}, {gt_traj_denorm[:, 1].max():.2f}]")
        
        # 计算轨迹长度
        diffs = torch.diff(gt_traj_denorm, dim=0)
        distances = torch.norm(diffs, dim=1)
        total_distance = distances.sum().item()
        avg_step = distances.mean().item()
        
        print(f"\n轨迹分析:")
        print(f"  总长度: {total_distance:.1f}m")
        print(f"  平均步长: {avg_step:.2f}m")
        print(f"  点数: {len(gt_traj_denorm)}")
        
        # 检查是否合理
        if total_distance > 50000:  # 50km
            print("  ⚠️  轨迹总长度异常大")
        if avg_step > 100:  # 100m/step
            print("  ⚠️  平均步长异常大")
        if avg_step < 0.1:  # 0.1m/step
            print("  ⚠️  平均步长异常小")

def main():
    print("=== 全面诊断数据预处理问题 ===")
    
    # 1. 检查归一化统计数据
    stats = diagnose_normalization_stats()
    
    # 2. 检查原始数据
    raw_stats, raw_df = check_raw_data_vs_processed()
    
    # 3. 检查处理后数据
    check_processed_data()
    
    # 4. 测试反归一化
    test_denormalization()
    
    print("\n=== 诊断完成 ===")

if __name__ == "__main__":
    main()
