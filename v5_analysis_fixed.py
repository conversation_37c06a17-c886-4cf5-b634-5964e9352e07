#!/usr/bin/env python3
"""
V5模型分析 - 修复版本，使用英文标签
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import numpy as np
import lmdb
import pickle
import os

from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.utils.config_loader import load_config

def main():
    print("🚀 V5 Model Analysis - Fixed Version")
    
    # 1. Load model
    config = load_config('configs/main_config.yaml')
    config.model.max_history_len = 360
    config.model.prediction_horizon = 120
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    model = TrajectoryPredictorV5(config).to(device)
    checkpoint = torch.load('models/trajectory_predictor_v5_best.pth', map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"✅ Model loaded successfully")
    print(f"  Epoch: {checkpoint.get('epoch', 'unknown')}")
    print(f"  Val Loss: {checkpoint.get('val_loss', 'unknown'):.4f}")
    print(f"  Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 2. Load data and make predictions
    data_path = config.data_preprocessing.output_path + '/val'
    env = lmdb.open(data_path, readonly=True, lock=False)
    
    predictions = []
    ground_truths = []
    losses = []
    
    criterion = nn.MSELoss()
    
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        
        sample_count = 0
        for key, value in cursor:
            if sample_count >= 15:  # Test 15 samples
                break
                
            try:
                sample_data = pickle.loads(value)
                
                # Prepare data
                def ensure_tensor(data):
                    if isinstance(data, torch.Tensor):
                        return data.float()
                    elif isinstance(data, np.ndarray):
                        return torch.from_numpy(data).float()
                    else:
                        return torch.tensor(data).float()
                
                history_features = ensure_tensor(sample_data['history_features']).unsqueeze(0).to(device)
                history_mask = torch.ones(1, history_features.shape[1], dtype=torch.bool).to(device)
                ground_truth_trajectory = ensure_tensor(sample_data['ground_truth_trajectory']).unsqueeze(0).to(device)
                ground_truth_destination = ensure_tensor(sample_data['ground_truth_destination']).unsqueeze(0).to(device)
                environment_roi = ensure_tensor(sample_data['environment_roi']).unsqueeze(0).to(device)
                
                # Model prediction
                with torch.no_grad():
                    predicted_trajectory = model(
                        history_features=history_features,
                        history_mask=history_mask,
                        ground_truth_destination=ground_truth_destination,
                        environment_roi=environment_roi
                    )
                
                # Calculate loss
                loss = criterion(predicted_trajectory, ground_truth_trajectory)
                losses.append(loss.item())
                
                # Save results
                pred_np = predicted_trajectory[0].cpu().numpy()
                gt_np = ground_truth_trajectory[0].cpu().numpy()
                
                predictions.append(pred_np)
                ground_truths.append(gt_np)
                
                sample_count += 1
                print(f"  Sample {sample_count}: Loss = {loss.item():.4f}")
                
            except Exception as e:
                print(f"Sample processing failed: {e}")
                continue
    
    env.close()
    
    # 3. Calculate performance metrics
    avg_loss = np.mean(losses)
    
    all_distances = []
    final_distances = []
    
    for pred, gt in zip(predictions, ground_truths):
        distances = np.sqrt(np.sum((pred - gt) ** 2, axis=1))
        all_distances.extend(distances)
        final_distances.append(distances[-1])
    
    ade = np.mean(all_distances)  # Average Displacement Error
    fde = np.mean(final_distances)  # Final Displacement Error
    
    print(f"\n📊 Performance Statistics:")
    print(f"  Test samples: {len(predictions)}")
    print(f"  Average MSE loss: {avg_loss:.4f}")
    print(f"  Average Displacement Error (ADE): {ade:.4f}")
    print(f"  Final Displacement Error (FDE): {fde:.4f}")
    print(f"  Loss std: {np.std(losses):.4f}")
    print(f"  Distance error std: {np.std(all_distances):.4f}")
    
    # 4. Create visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Loss distribution
    axes[0, 0].hist(losses, bins=8, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('Loss Distribution')
    axes[0, 0].set_xlabel('MSE Loss')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].axvline(avg_loss, color='red', linestyle='--', label=f'Mean: {avg_loss:.4f}')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Distance error distribution
    axes[0, 1].hist(all_distances, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0, 1].set_title(f'Distance Error Distribution (ADE: {ade:.4f})')
    axes[0, 1].set_xlabel('Distance Error')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].axvline(ade, color='red', linestyle='--', label=f'ADE: {ade:.4f}')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Final distance error
    axes[0, 2].hist(final_distances, bins=8, alpha=0.7, color='orange', edgecolor='black')
    axes[0, 2].set_title(f'Final Distance Error (FDE: {fde:.4f})')
    axes[0, 2].set_xlabel('Final Distance Error')
    axes[0, 2].set_ylabel('Frequency')
    axes[0, 2].axvline(fde, color='red', linestyle='--', label=f'FDE: {fde:.4f}')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # Trajectory visualization examples
    for i in range(3):
        ax = axes[1, i]
        if i < len(predictions):
            pred = predictions[i]
            gt = ground_truths[i]
            
            ax.plot(gt[:, 0], gt[:, 1], 'g-', linewidth=3, label='Ground Truth', alpha=0.8)
            ax.plot(pred[:, 0], pred[:, 1], 'r--', linewidth=3, label='Prediction', alpha=0.8)
            ax.scatter(gt[0, 0], gt[0, 1], c='green', s=150, marker='s', label='Start', zorder=5)
            ax.scatter(gt[-1, 0], gt[-1, 1], c='green', s=150, marker='*', label='GT End', zorder=5)
            ax.scatter(pred[-1, 0], pred[-1, 1], c='red', s=150, marker='*', label='Pred End', zorder=5)
            
            final_error = np.sqrt(np.sum((pred[-1] - gt[-1]) ** 2))
            ax.set_title(f'Trajectory Example {i+1} (FDE: {final_error:.3f})')
            ax.set_xlabel('X Coordinate')
            ax.set_ylabel('Y Coordinate')
            if i == 0:
                ax.legend(fontsize=10)
            ax.grid(True, alpha=0.3)
            ax.axis('equal')
    
    plt.tight_layout()
    plt.savefig('v5_analysis_results_fixed.png', dpi=300, bbox_inches='tight')
    print(f"\n✅ Visualization saved: v5_analysis_results_fixed.png")
    
    # 5. Create error over time plot
    fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
    
    # Calculate error over time for first few samples
    time_errors = []
    for pred, gt in zip(predictions[:5], ground_truths[:5]):
        errors = np.sqrt(np.sum((pred - gt) ** 2, axis=1))
        time_errors.append(errors)
    
    time_errors = np.array(time_errors)
    mean_errors = np.mean(time_errors, axis=0)
    std_errors = np.std(time_errors, axis=0)
    
    time_steps = np.arange(len(mean_errors))
    ax2.plot(time_steps, mean_errors, 'b-', linewidth=3, label='Mean Error')
    ax2.fill_between(time_steps, mean_errors - std_errors, mean_errors + std_errors, 
                     alpha=0.3, color='blue', label='±1 Std Dev')
    ax2.set_title('Prediction Error Over Time', fontsize=16)
    ax2.set_xlabel('Time Step', fontsize=14)
    ax2.set_ylabel('Position Error', fontsize=14)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('v5_error_over_time.png', dpi=300, bbox_inches='tight')
    print(f"✅ Error over time plot saved: v5_error_over_time.png")
    
    # 6. Save detailed results
    results_summary = f"""
V5 Model Test Results Summary
=============================

Model Information:
- Training Epoch: {checkpoint.get('epoch', 'unknown')}
- Validation Loss: {checkpoint.get('val_loss', 'unknown'):.4f}
- Model Parameters: {sum(p.numel() for p in model.parameters()):,}

Test Results:
- Test Samples: {len(predictions)}
- Average MSE Loss: {avg_loss:.4f}
- Average Displacement Error (ADE): {ade:.4f}
- Final Displacement Error (FDE): {fde:.4f}
- Loss Standard Deviation: {np.std(losses):.4f}
- Distance Error Standard Deviation: {np.std(all_distances):.4f}

Error Distribution:
- 50% errors < {np.percentile(all_distances, 50):.4f}
- 75% errors < {np.percentile(all_distances, 75):.4f}
- 90% errors < {np.percentile(all_distances, 90):.4f}
- 95% errors < {np.percentile(all_distances, 95):.4f}

Generated Files:
- v5_analysis_results_fixed.png: Main analysis visualization
- v5_error_over_time.png: Error progression over time
- v5_test_results_summary.txt: This summary file
"""
    
    with open('v5_test_results_summary.txt', 'w', encoding='utf-8') as f:
        f.write(results_summary)
    
    print(results_summary)
    print("✅ Results summary saved: v5_test_results_summary.txt")
    print("\n🎉 V5 Model Analysis Complete!")
    
    # Check file sizes
    files_to_check = ['v5_analysis_results_fixed.png', 'v5_error_over_time.png', 'v5_test_results_summary.txt']
    print("\n📁 Generated Files:")
    for filename in files_to_check:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"  ✅ {filename}: {size:,} bytes")
        else:
            print(f"  ❌ {filename}: Not found")

if __name__ == "__main__":
    main()
