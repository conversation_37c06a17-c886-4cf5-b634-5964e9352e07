import rasterio
from pathlib import Path

dem_path = Path('environment/dem.tif')

try:
    with rasterio.open(dem_path) as src:
        print(f'DEM 文件路径: {dem_path}')
        print(f'Bounds: {src.bounds}')
        print(f'Resolution: {src.res}')
        print(f'CRS: {src.crs}')
        print(f'Width: {src.width}')
        print(f'Height: {src.height}')
        print(f'Nodata Value: {src.nodata}')
except rasterio.errors.RasterioIOError as e:
    print(f"错误: 无法打开或读取DEM文件 {dem_path}: {e}")
except Exception as e:
    print(f"发生未知错误: {e}") 