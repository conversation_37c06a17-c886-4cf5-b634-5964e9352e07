# 多模态端到端轨迹预测与生成系统

## 1. 项目概述

### **项目背景与核心挑战**

本项目旨在解决**军事侦察与目标跟踪**中的关键问题。我们关注的目标是**全地形装甲车**，其在复杂战场环境中的运动模式具有高度不确定性。

核心挑战在于：
1.  **间歇性观测：** 观测数据来源于卫星平台，具有明显的**“有观测期”和“无观测间歇期”交替出现**的特点。这意味着我们无法获得连续、完整的轨迹信息。
2.  **复杂环境：** 战场地形复杂，包含山地、森林、城市、道路等多种地貌，这些环境因素直接影响装甲车的通行能力和战术选择。
3.  **动态意图：** 目标装甲车的运动并非随机，而是基于特定的**战术意图**，这些意图可能随战场态势动态变化。
4.  **资源约束：** 卫星观测资源有限，需要高效调配。

### **项目目标**

本项目的目标是开发一个**智能化的轨迹预测与意图推断系统**，能够：

1.  **实时意图推断：** 根据有限的、间歇性观测轨迹片段，实时推断目标装甲车的**最终目的地意图**。这一意图以**概率分布**形式输出，并包含**不确定性量化**，以反映模型对预测结果的置信度。
2.  **间歇期轨迹预测：** 在观测间歇期，能够**预测目标未来可能的运动轨迹**。这有助于系统在失去直接观测后，仍能大致掌握目标位置，辅助卫星接力观测和重捕获。
3.  **适应情报变化：**
    * 模型能够接受**外部备选终点集合**作为输入（例如，来自战场情报的关键区域或战略目标列表），并从中识别最可能的目标。
    * 同时，模型也必须能够在**没有任何外部备选终点输入**的情况下，完全依靠自身对轨迹和环境的理解，自主预测目标的最终目的地。
    * 该外部备选集合本身可能在任务进行中发生**动态变化**或附带**不同权重**。
4.  **支持卫星协同与调配：** 模型的预测输出将直接指导**多星协同观测平台**的调度决策，以优化观测条带的配置，提高目标捕获率和持续跟踪能力。

## 2. 核心设计理念：多模态端到端融合与两阶段预测

为实现上述目标，我们设计了一个**多模态端到端的深度学习系统**。该系统将**联合优化**意图推断和轨迹生成两个逻辑阶段，并充分利用不同粒度的环境信息。

1.  **多模态深度融合：**
    * 模型将同时处理并智能融合三种关键信息：**轨迹运动信息**（包括运动学特征和点级环境特征）、**区域级环境语义**（通过 CNN 从局部环境图块中提取）、以及**抽象的全局上下文**（提供宏观背景）。这种全面感知能力是理解复杂战场态势的基础。
    * 这解决了传统模型仅依赖单一模态信息或无法理解环境宏观战术意义的局限性。

2.  **端到端学习：**
    * 整个系统从原始输入到最终输出（未来轨迹序列）都由一个大型神经网络来处理。所有模块的参数将通过一个统一的总损失函数进行联合优化。
    * 这使得模型各部分能够协同工作，共同提升整体预测性能，避免了多模型串联可能导致的误差累积和优化不一致问题。

3.  **两阶段逻辑预测：**
    * 模型在逻辑上将复杂的预测任务分解为两个明确的、有依赖关系的阶段：
        * **阶段一：目的地意图预测 (意图推断)。** 模型首先根据所有可用信息，预测目标最可能的**最终目的地坐标**及其**概率分布**（包含不确定性）。这是对“目标要去哪儿”的判断。
        * **阶段二：未来轨迹生成 (轨迹预测)。** 然后，模型将第一阶段预测出的目的地（均值）作为**明确的条件**，来生成从当前位置到该目的地的详细**未来轨迹序列**。这解决了“目标会怎么去”的问题。
    * 这种分阶段设计符合人类决策直觉，并使得轨迹生成有了清晰的引导，显著降低了长时预测的难度。

## 3. 数据准备与预处理

高效且精细的数据准备是模型成功的基石，特别是要处理复杂的多模态和间歇性数据。

1.  **轨迹片段数据：**
    * 从卫星观测数据中提取**不固定长度的轨迹片段序列**。每个轨迹点包含：
        * **平面坐标 (X, Y)**：从原始经纬度转换，并标准化。
        * **运动学特征**：瞬时速度 (vx, vy)、加速度 (ax, ay)、方向（$\sin/\cos$ 编码）、曲率。
        * **点级环境特征**：查询该轨迹点所在位置的坡度、坡向、沿路径坡度、垂直路径坡度，以及地表覆盖物的 **One-Hot 编码**。
    * **时间窗口聚合：** 将 1 秒级的原始轨迹数据按**10 秒时间窗口**进行聚合，以降低序列长度、去除噪声并捕获宏观运动模式。

2.  **环境栅格数据（多模态地理信息）：**
    * 包括 **DEM、地表覆盖物、坡度、坡向**等高分辨率栅格图。
    * **道路信息整合：** 额外引入**道路矢量数据**，将其栅格化为**“道路存在/类型”图层**，作为新的通道加入环境栅格数据。
    * **局部 ROI 裁剪：** 在训练和推理时，根据轨迹的当前位置，动态裁剪固定大小的**局部感兴趣区域 (ROI)** 栅格图块，作为 CNN 的输入。

3.  **抽象全局信息：**
    * 通过离线预处理，对整个作战区域进行**粗粒度网格划分**。预计算每个网格的宏观特征（如平均海拔、主要地表类型、战略 POI 密度等）。
    * 在模型中，根据轨迹所在的大网格区域，输入相应的抽象全局特征。

4.  **数据存储与加载：**
    * 为确保训练效率，所有处理好的数据（轨迹序列特征、ROI 像素数据、抽象全局信息、标签）将打包存储在 **LMDB (Lightning Memory-Mapped Database)** 中。
    * LMDB 利用**内存映射和零拷贝**特性，能实现极高效的数据加载，避免训练过程中的 I/O 瓶颈。

## 4. 核心模型模块设计与实现

我们的模型由多个专门的深度学习模块组成，它们协同工作以实现多模态信息的处理和预测任务。

1.  **轨迹序列编码器 (Transformer Encoder)**
    * **职责：** 从输入的轨迹片段序列中，学习并提取装甲车的**运动意图和模式**，并整合每个轨迹点所处的**点级环境特征**。
    * **架构：** 采用 **Transformer Encoder**。它通过**多头自注意力机制** 高效捕捉序列中长距离的时序依赖和复杂运动模式。
    * **输入：** 经过预处理的轨迹片段序列，每个点包含运动学特征（如坐标、速度、加速度、朝向、曲率）和点级环境特征（如坡度、地表覆盖 One-Hot 编码）。
    * **输出：** **轨迹意图嵌入向量 $E_{\text{trajectory}}$**。

2.  **环境特征提取器 (CNN)**
    * **职责：** 从局部环境 ROI 栅格图中，提取**区域性的、隐式的环境语义**，理解地形的战术价值。
    * **架构：** 基于**轻量级 ResNet 风格的 CNN 骨架**。它通过卷积核的感受野 学习和抽象出区域级的空间模式。
    * **输入：** 包含 DEM、坡度、坡向、地表覆盖和**道路信息**等多通道的局部环境 ROI。
    * **输出：** **区域环境语义嵌入向量 $E_{\text{environment}}$**。

3.  **全局信息编码器 (Global Information Encoder)**
    * **职责：** 从**抽象的全局信息**中提取**全局上下文**，为模型提供宏观的视野和战略背景。
    * **架构：** 可根据全局信息的具体形式，采用 **nn.Embedding 层**（如果输入是 ID）或 **MLP**（如果输入是特征向量）。
    * **输入：** 目标当前位置关联的抽象全局信息，如粗粒度网格 ID 或战略 POI 特征。
    * **输出：** **全局上下文嵌入向量 $E_{\text{global}}$**。

4.  **特征融合层 (Feature Fusion Layer)**
    * **职责：** 智能地整合来自不同编码器的三种关键嵌入：**轨迹意图、区域环境语义和全局上下文**。
    * **架构：** 采用直接**拼接 (Concatenation)** 的方式，将三种嵌入向量连接起来，然后通过一个或多个**多层感知机 (MLP)** 进行非线性变换和信息混合。
    * **输出：** 统一的**融合特征向量 $E_{\text{fused}}$**。

5.  **目的地预测层 (MDN)**
    * **职责：** 执行**阶段一：意图推断**。它根据融合特征，预测目标最终目的地坐标的**概率分布**，并量化不确定性。
    * **架构：** 采用**混合密度网络 (MDN)**，它包含 MLP 层，最终输出定义高斯分布的参数。
    * **关键要求：** **严格设定为 $K=3$ 个高斯分量**，以捕捉目标意图的多种可能性和复杂性。
    * **输出：** 3 个二维高斯分量的参数（包括均值、标准差、相关系数和混合权重），维度为 `(Batch_size, 18)`。

6.  **轨迹生成器 (Transformer Decoder)**
    * **职责：** 执行**阶段二：轨迹生成**。它以融合特征为上下文，并以预测的目的地为条件，生成未来轨迹序列。
    * **架构：** 采用 **Transformer Decoder**。
    * **输入：**
        * **上下文输入：** 融合特征 $E_{\text{fused}}$。
        * **条件输入：** 来自 MDN 预测的**目的地均值** ($\hat{\mu}_x, \hat{\mu}_y$)。
        * **自回归输入：** 上一步预测点。
    * **输出：** 预测的未来轨迹序列，包含一系列坐标点。

7.  **V1网格分类器模型 (GridClassifierV1)**
    * **职责：** `GridClassifierV1` 模型是一个早期版本的轨迹预测模型，它结合了意图分类和轨迹回归。
        其主要功能是根据历史轨迹和环境ROI，预测目标的最终目的地所属的网格，并生成未来的轨迹。
    * **架构：** 该模型主要由以下组件构成：
        * **CNN 编码器：** 处理环境ROI，提取空间特征。
        * **RNN 编码器 (LSTM)：** 处理历史轨迹序列，捕获时序依赖。
        * **特征融合层：** 整合CNN和RNN提取的特征。
        * **意图分类头：** 将融合特征映射到预定义网格的分类 logits，用于目的地意图推断。
        * **轨迹回归头 (GMM)：** 输出未来轨迹的高斯混合模型参数，用于多模态轨迹预测。
    * **输入：** 历史轨迹数据 (`history_data`) 和环境ROI数据 (`environment_roi_data`)。
        在训练时，也会接收真实的目的地 (`ground_truth_destination`) 和未来轨迹 (`ground_truth_trajectory`) 作为监督信号。
    * **输出：** 意图分类的 logits (`intent_logits`) 和轨迹预测的 GMM 参数 (`gmm_params_flat`)。
    * **位置：** 模型定义位于 `src/models/v1_grid_classifier/end_to_end_model.py`。

## 5. 训练策略与优化

我们的模型将采用端到端联合训练策略，以确保各模块协同优化，达到最佳整体性能。

1.  **总损失函数：**
    * 模型的训练目标是最小化一个由多个损失项组成的**总损失函数**：$L_{total} = L_{dest} + \lambda_1 \cdot L_{traj} + \lambda_2 \cdot L_{contrastive}$。
    * **$L_{dest}$ (目的地预测损失)：** 对 MDN 输出的 3 个高斯分量参数和真实目的地坐标计算**负对数似然 (NLL) 损失**。这不仅确保目的地预测准确，更重要的是促使模型**准确量化预测的不确定性**。
    * **$L_{traj}$ (轨迹生成损失)：** 对轨迹生成器预测的未来轨迹序列和真实未来轨迹序列计算 **MSE (均方误差)** 或 **Huber Loss**。
    * **$L_{contrastive}$ (对比学习损失 - 可选)：** 如果有备选终点集合，可利用 **InfoNCE Loss** 或 **Triplet Loss** 来增强模型在众多候选目标中辨别真实目标的“眼力”。
    * **超参数 $\lambda_1, \lambda_2$：** 用于平衡不同损失项的贡献，需通过实验调整。

2.  **联合优化与反向传播：**
    * 整个模型是**端到端可微分的**。梯度会从两个主要损失项（MDN 输出的 $L_{dest}$ 和轨迹生成器输出的 $L_{traj}$）反向传播。
    * 关键在于，来自 $L_{traj}$ 的梯度会通过预测的目的地均值**反向传播回 MDN**，甚至进一步影响到共享的特征融合层和编码器。这使得所有模块协同优化，确保生成的轨迹与预测的目的地之间逻辑一致。

3.  **优化器与学习率调度：**
    * 选用 **AdamW** 或 **RAdam** 优化器，它们在处理 Transformer 和 RNN 结构时表现良好。
    * 采用**学习率调度策略**（如 ReduceLROnPlateau 或余弦退火），以实现更稳定的训练。

4.  **训练技巧：**
    * **教师强制 (Teacher Forcing)：** 在训练轨迹生成器时使用教师强制，加速模型收敛并提高稳定性。
    * **梯度累积：** 可选使用梯度累积，以在显存有限的情况下，有效增大逻辑批次大小。

## 6. 在线推理与持续学习机制

我们的模型设计专门针对卫星观测下**间歇性数据流**的挑战，确保在部署后能实时响应并持续学习。

1.  **在线推理：实时预测与“意图连续性”**
    * **核心：** 即使在观测间歇期后，模型也能**无缝地接续之前的认知**，并提供实时预测。
    * **实时数据流与预处理：** 持续接收卫星发送的轨迹片段 ($T_{current}$)，并立即进行预处理（特征提取、ROI 裁剪）。
    * **【关键】轨迹编码器状态传递：应对观测间歇期。**
        * 轨迹序列编码器利用**上下文 Token 嵌入传递机制**，将上一个已观测片段的最终上下文作为当前片段的**初始上下文输入**。
        * **作用：** 这使得模型能够“记忆”目标在**历史所有已观测片段中的运动趋势和意图**，即便中间有长时间的观测空白。模型能理解目标的**意图连续性**。
    * **实时预测更新：** 基于当前输入和传递的状态，模型会**实时更新**对目标最终目的地概率分布和未来轨迹的预测。
    * **结果处理与输出：** 预测结果可选经过平滑滤波处理。
    * **动态备选目标处理：** 模型核心能力是**自由预测**。如果外部提供了动态备选目标，系统将进行**预测后匹配或优化**，并重新条件化轨迹生成器，以指导卫星调度，协助重捕获。

2.  **持续学习：模型参数的“适应性进化”**
    * **核心：** 确保模型参数能够**长期适应变化**（新战术、新地形），防止“灾难性遗忘”，提升在新环境下的泛化能力。
    * **已完成轨迹数据收集与标注：** 持续收集带有真实标签的已完成轨迹数据。
    * **经验回放缓冲区 (Experience Replay Buffer)：** 存储近期已完成轨迹样本的“历史知识库”。这是**对抗灾难性遗忘 (Catastrophic Forgetting)** 的关键机制。
    * **小批量微调：** 定期（例如每 N 条新轨迹或每隔一段时间）进行。从经验回放缓冲区和最新数据中**混合采样**，以**非常低的学习率**对模型主体参数进行梯度更新。
    * **模型更新与部署：** 验证新模型性能后，平滑部署到实时推理系统。

## 7. 评估与验证

为了全面、科学地评估我们新模型的实际效能，我们将采用一套多维度、任务导向型的评估体系，而非仅仅依赖传统指标。

1.  **核心评估理念：任务导向型评估**
    * **目的：** 超越传统轨迹预测指标（如 ADE/FDE）的局限，直接衡量模型输出在**多星协同观测军事任务**中的实际有效性。
    * **方法：** 在一个模拟环境中，模型的预测将直接指导卫星观测调度，从而计算其对实际任务目标的贡献。

2.  **实验设置：卫星观测场景模拟**
    * **环境构建：** 搭建一个虚拟的战场模拟环境，基于真实地理环境数据，模拟装甲车的运动轨迹（包含间歇和战术行为）以及卫星星座的观测能力（条带、扫描频率等）。
    * **模型集成与调度：** 将训练好的模型集成到模拟器中，其预测输出（目的地概率分布、未来轨迹）将作为模拟中**卫星调度模块**的输入，指导观测条带的生成和部署。

3.  **关键任务指标 (Task-Oriented Metrics)：**
    * **命中率 (Coverage Rate)：** 模型预测指导生成的观测条带能够**实际覆盖到目标真实位置**的比例。这是系统“发现”和“锁定”目标能力的核心指标。
    * **观测精度 (Target Centricity)：** 当观测条带成功命中目标时，目标的真实位置距离该观测条带**几何中心**（或模型预测目的地均值）的平均距离。这衡量了命中质量，区分是“精准命中”还是“大范围覆盖”命中。
    * **持续观测丢失率 (Sustained Observation Loss Rate)：** 在长时间跟踪中，由于预测失误导致观测条带未能覆盖目标真实位置的**总时间百分比**。反映系统持续跟踪的鲁棒性。
    * **最大/平均持续观测时长 (Maximum/Average Sustained Observation Duration)：** 系统能够**连续不间断地追踪目标的最长时间或平均时间**。这是丢失率的积极反面，更直观体现了连续跟踪的成功效能。
    * **条带资源利用率 (Strip Utilization Efficiency)：** 平均每个观测条带实际“有效观测时间”或“有效覆盖面积”与条带总时间和总面积的比例。评估资源调配的效率和成本效益。

4.  **预测模型质量指标 (Model Prediction Quality Metrics)：**
    * **负对数似然 (NLL)：** 衡量真实目标落在模型预测概率分布上的“概率密度有多高”。直接反映模型不确定性估计的质量。
    * **覆盖概率校准 (Coverage Probability Calibration)：** 检验模型预测的置信度是否与实际覆盖率相符。衡量模型对其自身不确定性判断的可靠性。

5.  **传统几何误差指标 (Traditional Geometric Error Metrics)：**
    * **平均位移误差 (ADE)：** 预测轨迹与真实轨迹所有时间步之间 $L_2$ 距离的平均值。
    * **最终位移误差 (FDE)：** 预测轨迹的最后一个点与真实轨迹的最后一个点之间 $L_2$ 距离。
    * 这些指标作为底层预测精度的基线，为模型本身的数据匹配能力提供参考。

## 8. 技术栈与开发规范

**技术栈建议：**
* **编程语言：** Python
* **深度学习框架：** PyTorch (推荐，因其灵活性和研究友好性)
* **科学计算库：** NumPy, SciPy
* **数据处理：** Pandas, `rasterio`/`GDAL` (用于地理栅格数据处理), **LMDB 库**
* **模型训练与管理：** TensorBoard 或 Weights & Biases (W&B)

**开发规范：**
* **模块化设计：** 各模型组件（编码器、融合层、预测层、生成器）应作为独立的 PyTorch `nn.Module` 进行设计和实现，职责明确。
* **代码可读性与可维护性：** 遵循 PEP 8 规范，代码注释清晰，易于理解和后续迭代。
* **性能优化：** 关注数据加载、模型推理的速度，利用 GPU 并行计算和 PyTorch 的性能优化工具（如 AMP）。
* **版本控制：** 使用 Git 进行版本管理。 

## 9. 项目结构

一个清晰且有逻辑的项目结构对于深度学习项目的可维护性、可扩展性和团队协作至关重要。以下是我们推荐的理想项目结构，它旨在清晰地分离数据、代码、配置和输出，并促进模块化开发。

```
Destination_Intent_Predictor__Trajectory_Generator/
├── .gitignore               # Git版本控制忽略文件列表，指定不应被版本控制追踪的文件和目录
├── README.md                # 项目主说明文件，提供项目概览、安装指南和快速开始
├── environment.yml          # Conda环境配置文件，用于管理项目依赖
├── run.py                   # 项目主入口脚本，用于启动训练、评估、预测等主要任务
├── train.py                 # 通用训练脚本，负责加载配置并调用训练器进行模型训练
├── evaluate.py              # 通用评估脚本，用于在指定数据集上评估模型性能
├── predict_and_visualize.py # 通用预测与可视化脚本，用于模型推理和结果展示
├── configs/                 # **配置目录**：集中管理所有YAML格式的配置
│   ├── default.yaml         # 所有参数的全局默认值，提供各项参数的基准设定
│   ├── main_config.yaml     # 项目的主配置文件，用于覆盖default.yaml中的特定参数
│   ├── data_preprocessing.yaml # (新增) 数据预处理专用配置
│   └── models/              # **模型特定配置**：存放各模型专属的YAML配置
│       ├── cnn_rnn_fusion_model.yaml # (新增) CNN-RNN Fusion 模型的配置
│       ├── end_to_end_model.yaml   # 端到端模型的具体配置
│       ├── v1_grid_classifier_model.yaml # V1网格分类器模型的配置 (由Python文件重构而来)
│       └── v4_mdn_model.yaml       # V4 MDN模型的配置
├── data/                    # **数据目录**：分层存储不同处理阶段的数据
│   ├── raw/                 # **原始数据**：未经过任何处理的原始输入数据
│   │   ├── trajectories/    # 原始轨迹CSV文件
│   │   └── environment/     # 原始环境GIS文件 (如dem.tif, landcover.tif)
│   ├── interim/             # **中间数据**：经过初步处理（如环境特征集成）的数据
│   │   └── trajectories_with_env/ # 带有环境特征的轨迹CSV文件
│   └── processed/           # **处理后的数据**：可直接用于模型训练的最终数据集（LMDB格式）
│       └── processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/ # 示例LMDB数据集
│           ├── train/              # 训练集LMDB文件 (data.mdb, lock.mdb)
│           ├── val/                # 验证集LMDB文件 (data.mdb, lock.mdb)
│           └── normalization_stats.pkl # 归一化统计文件 (由预处理脚本生成)
├── environment/             # **环境文件目录**：GIS环境相关文件和辅助可视化脚本
│   ├── dem_aligned.tif      # 对齐后的DEM文件
│   ├── landcover_aligned.tif # 对齐后的地表覆盖文件
│   └── visualize_gis_data.py # GIS数据可视化工具脚本
├── scripts/                 # **辅助脚本目录**：存放顶层执行脚本和各种辅助工具脚本
│   ├── data_check/          # **数据检查和验证脚本**
│   │   ├── check_crs.py            # 检查坐标参考系一致性
│   │   ├── check_landcover.py      # 检查地表覆盖数据
│   │   ├── check_lmdb.py           # 检查LMDB数据库内容
│   │   ├── check_lmdb_sample.py    # (新增) 检查LMDB单个样本内容
│   │   ├── check_stats_file.py     # (新增) 检查统计文件内容
│   │   └── check_trajectory_duration.py # (新增) 检查轨迹时长
│   ├── data_prep/           # **数据预处理流水线脚本**
│   │   ├── add_environment_data_to_trajectories.py # 将环境数据添加到轨迹CSV
│   │   ├── preprocess_main.py      # 主预处理脚本，协调整个数据生成流程
│   │   └── sample_generator.py     # 轨迹样本生成器
│   ├── debug/               # **调试和数据探索脚本**
│   │   ├── debug_dataloader.py     # (新增) 调试数据加载器
│   │   ├── debug_model_and_data.py # (新增) 调试模型和数据
│   │   ├── final_diagnosis.py      # (新增) 最终诊断脚本
│   │   ├── fix_all_issues.py       # (新增) 修复所有问题脚本
│   │   ├── inspect_data.py         # 检查数据样本内容
│   │   ├── inspect_data_gap.py     # (新增) 检查数据空隙
│   │   ├── inspect_data_pipeline.py # 检查数据管道流程
│   │   ├── inspect_generated_dataset.py # (新增) 检查生成数据集
│   │   ├── inspect_landcover_categories.py # (新增) 检查地表覆盖类别
│   │   ├── inspect_lmdb_keys.py    # (新增) 检查LMDB键
│   │   ├── inspect_lmdb_samples.py # (新增) 检查LMDB样本
│   │   ├── inspect_normalization_history_keys.py # (新增) 检查归一化历史键
│   │   └── quick_check.py          # 快速检查预处理结果
│   ├── training/            # **特定训练辅助脚本** (如果train.py不能完全覆盖所有训练场景)
│       └── v1_model_train.py # (移动) V1模型的训练脚本
│   └── visualization/       # **可视化辅助脚本**
│       ├── visualize_history_and_roi.py # (新增) 可视化历史和ROI
│       └── visualize_trajectory_animation.py # (新增) 可视化轨迹动画
├── src/                     # **核心源代码目录**：项目的主要业务逻辑和可复用模块
│   ├── __init__.py          # 将此目录标识为Python包
│   ├── data/                # **数据处理模块**
│   │   ├── __init__.py
│   │   ├── datasets.py      # PyTorch Dataset定义 (如LMDBDataset)
│   │   ├── processors/      # **数据处理器**：负责数据转换和特征提取
│   │   │   ├── environment_processor.py # 环境特征提取器
│   │   │   └── trajectory_processor.py  # 轨迹运动学特征处理器
│   │   └── io/              # **数据输入/输出**：负责数据读写操作
│   │       └── lmdb_writer.py # LMDB数据库写入器
│   ├── models/              # **模型定义**：存放所有模型的架构定义
│   │   ├── __init__.py
│   │   ├── cnn_rnn_fusion_model.py # (新增) CNN-RNN Fusion 模型的定义
│   │   ├── end_to_end_model.py # (移动) EndToEndModel的主要定义
│   │   ├── v1_grid_classifier/ # V1模型目录
│   │   │   └── end_to_end_model.py # V1网格分类器模型的定义
│   │   ├── v4_mdn_model.py     # (移动) V4 MDN模型的定义
│   │   └── components/         # **共享模型组件**：可复用的神经网络层或模块
│   │       ├── attention.py    # (新增) 通用注意力模块
│   │       └── mlp_blocks.py   # (新增) 通用MLP块
│   ├── engine/              # **训练引擎模块**
│   │   ├── __init__.py
│   │   ├── trainer.py       # 核心训练器类，管理训练和验证循环
│   │   └── loss.py          # 自定义损失函数定义
│   ├── evaluation/          # **评估模块**
│   │   ├── __init__.py
│   │   └── metrics.py       # (新增) 评估指标计算函数
│   ├── utils/               # **通用实用工具**
│   │   ├── __init__.py
│   │   ├── config_loader.py # 配置加载与合并工具
│   │   ├── normalization.py # 数据归一化和反归一化工具
│   │   ├── welford_stats.py # Welford算法实现 (在线均值/标准差计算)
│   │   └── logger.py        # (新增) 项目日志管理工具
│   └── visualization/       # **可视化模块**
│       ├── __init__.py
│       └── plotter.py       # (新增) 通用绘图工具
├── checkpoints/             # 存放训练过程中保存的模型检查点
├── logs/                    # 存放训练日志（如TensorBoard日志、训练过程中的文本日志）
├── outputs/                 # 存放模型推理结果、生成文件、实验报告等
├── notebooks/               # 存放Jupyter Notebooks，用于数据探索、模型原型设计和结果展示
```

## 10. 文件夹和脚本作用说明

*   **`项目根目录/`**:
    *   `.gitignore`: 配置Git忽略的文件和目录，保持仓库清洁。
    *   `README.md`: 项目的入口点，提供项目概览、安装指南和快速开始。
    *   `environment.yml`: Conda环境定义文件，确保开发环境的一致性。
    *   `run.py`: **主执行入口。** 负责解析命令行参数，加载主配置文件，并根据传入的参数（如 `--mode train`、`--mode evaluate`）调度执行 `train.py`、`evaluate.py` 或 `predict_and_visualize.py`。
    *   `train.py`: **通用模型训练启动脚本。** 从 `configs/` 加载训练配置和模型特定配置，然后调用 `src/engine/trainer.py` 中的 `Trainer` 类开始训练过程。
    *   `evaluate.py`: **通用模型评估启动脚本。** 加载训练好的模型和测试数据，调用 `src/evaluation/metrics.py` 计算并输出评估指标。
    *   `predict_and_visualize.py`: **通用预测与可视化启动脚本。** 加载模型，进行轨迹预测，并使用 `src/visualization/plotter.py` 将结果（如预测轨迹和环境地图）可视化。

*   **`configs/`**:
    *   `default.yaml`: 定义项目中所有配置参数的默认值。
    *   `main_config.yaml`: 允许用户覆盖 `default.yaml` 中的特定参数，用于当前运行的主要配置。
    *   `data_preprocessing.yaml`: 专门用于配置数据预处理管道的参数。
    *   `models/`:
        *   `cnn_rnn_fusion_model.yaml`: CNN-RNN Fusion 模型的具体架构参数和超参数。
        *   `end_to_end_model.yaml`: 端到端模型的具体架构参数和超参数。
        *   `v1_grid_classifier_model.yaml`: V1版本网格分类器模型的配置（从Python文件重构而来）。
        *   `v4_mdn_model.yaml`: V4 MDN模型的具体架构参数和超参数。

*   **`data/`**:
    *   `raw/`: 存放项目最原始的数据，未经任何修改。
    *   `interim/`: 存放原始数据经过初步处理后的中间结果。
    *   `processed/`: 存放经过完整预处理，可直接用于模型训练/评估的最终数据集（LMDB）。

*   **`environment/`**:
    *   `dem_aligned.tif`, `landcover_aligned.tif`: 存储对齐和处理后的GIS栅格数据。
    *   `visualize_gis_data.py`: 用于加载和显示GIS环境数据的辅助脚本。

*   **`scripts/`**: 用于存放那些不属于核心库，但在项目流程中扮演重要角色，或作为独立工具的脚本。
    *   `data_check/`: 包含用于验证数据质量和格式的脚本。
    *   `data_prep/`: 包含构建数据预处理流水线的各个阶段脚本。
    *   `debug/`: 包含用于调试特定模块或数据流的临时/辅助脚本。
    *   `training/`: 如果有非常特定或实验性的训练流程，可以放在这里，与通用 `train.py` 区分。
    *   `visualization/`: 包含用于生成图表和可视化的函数。

*   **`src/`**: 存放项目的所有核心Python模块，这些模块是可复用的库代码。
    *   `data/`:
        *   `datasets.py`: 定义如何从数据源（如LMDB）加载数据并准备成PyTorch `Dataset`。
        *   `processors/`: 包含处理不同数据类型（轨迹、环境）的逻辑。
        *   `io/`: 包含数据读写（如LMDB写入）的逻辑。
    *   `models/`: 包含所有模型的 `nn.Module` 定义，是模型架构的蓝图。
        *   `cnn_rnn_fusion_model.py`: (新增) 定义 CNN-RNN Fusion 模型的 PyTorch `nn.Module`。
        *   `end_to_end_model.py`: 定义端到端预测模型的PyTorch `nn.Module`。
        *   `v1_grid_classifier/`: 存放 V1 网格分类器模型的特定定义文件。
            *   `end_to_end_model.py`: 定义 `GridClassifierV1` 模型的PyTorch `nn.Module`。
        *   `v4_mdn_model.py`: 定义V4 MDN预测模型的PyTorch `nn.Module`。
        *   `components/`: 存放可在不同模型中共享的通用神经网络层或构建块（如注意力机制、MLP）。
    *   `engine/`: 包含训练和优化的核心逻辑。
    *   `evaluation/`: 包含计算各种评估指标的函数。
    *   `utils/`: 存放通用工具函数，如配置加载、数据归一化、日志记录等。
    *   `visualization/`: 包含用于生成图表和可视化的函数。

*   **`checkpoints/`**: 存放训练过程中模型保存的状态字典（权重）。
*   **`logs/`**: 存放训练日志（如TensorBoard日志、训练过程中的文本日志）。
*   **`outputs/`**: 存放模型推理的输出、生成文件、实验报告等。
*   **`notebooks/`**: 存放用于探索性数据分析 (EDA)、模型原型设计或特定结果展示的Jupyter Notebooks。

## 11. 核心工作流程与脚本运行顺序

一个典型的项目工作流程会遵循以下步骤，并涉及相应的脚本：

1.  **环境设置与依赖安装**
    *   **脚本/文件：** `environment.yml`
    *   **描述：** 使用 `conda env create -f environment.yml` 创建并激活项目所需的Python环境和依赖。

2.  **数据准备 (Data Preparation)**
    *   **目的：** 将原始轨迹数据与环境GIS数据结合，并预处理成模型可直接使用的LMDB格式数据集。
    *   **脚本：**
        *   `scripts/data_prep/add_environment_data_to_trajectories.py`: 将 `data/raw/trajectories/` 中的轨迹数据与 `environment/` 中的GIS数据融合，生成 `data/interim/trajectories_with_env/`。
        *   `scripts/data_prep/preprocess_main.py`: 作为主预处理脚本，它会：
            *   从 `data/interim/trajectories_with_env/` 读取数据。
            *   利用 `src/data/processors/trajectory_processor.py` 进行运动学特征计算和轨迹采样。
            *   利用 `src/data/processors/environment_processor.py` 提取环境ROI。
            *   利用 `src/utils/welford_stats.py` 在线计算特征的归一化统计量，并保存到 `data/processed/.../normalization_stats.pkl`。
            *   利用 `src/data/io/lmdb_writer.py` 将处理好的样本写入到 `data/processed/` 目录下的LMDB数据库中。
    *   **运行顺序示例：**
        1.  `conda activate wargame` (激活环境)
        2.  `python scripts/data_prep/add_environment_data_to_trajectories.py --config configs/data_preprocessing.yaml` (首次运行或数据更新时)
        3.  `python scripts/data_prep/preprocess_main.py --config configs/data_preprocessing.yaml`

3.  **模型训练 (Model Training)**
    *   **目的：** 使用处理好的LMDB数据集训练深度学习模型。
    *   **脚本：** `run.py` (通过参数调用 `train.py`)，`train.py`，`scripts/training/v1_model_train.py` (特定于V1模型)。
    *   **运行顺序示例：**
        1.  `conda activate wargame`
        2.  `python run.py --mode train --config configs/main_config.yaml --model_config configs/models/end_to_end_model.yaml` (训练端到端模型)
        3.  `python run.py --mode train --config configs/main_config.yaml --model_config configs/models/v1_grid_classifier_model.yaml` (训练V1网格分类器模型)
            *   (`run.py` 内部会调用 `train.py` 或 `scripts/training/v1_model_train.py`，它们会加载模型定义，`src/engine/trainer.py` 进行训练。)

4.  **模型评估 (Model Evaluation)**
    *   **目的：** 评估训练好的模型在验证集或测试集上的性能。
    *   **脚本：** `run.py` (通过参数调用 `evaluate.py`)，`evaluate.py`
    *   **运行顺序示例：**
        1.  `conda activate wargame`
        2.  `python run.py --mode evaluate --config configs/main_config.yaml --model_config configs/models/end_to_end_model.yaml --checkpoint checkpoints/best_model.pth`
            *   (`run.py` 内部会调用 `evaluate.py`，`evaluate.py` 会加载模型和 `src/evaluation/metrics.py`。)

5.  **预测与可视化 (Prediction & Visualization)**
    *   **目的：** 使用训练好的模型进行轨迹预测，并将结果可视化展示。
    *   **脚本：** `run.py` (通过参数调用 `predict_and_visualize.py`)，`predict_and_visualize.py`
    *   **运行顺序示例：**
        1.  `conda activate wargame`
        2.  `python run.py --mode predict_and_visualize --config configs/main_config.yaml --model_config configs/models/end_to_end_model.yaml --checkpoint checkpoints/best_model.pth --input_trajectory data/raw/trajectories/example.csv`
            *   (`run.py` 内部会调用 `predict_and_visualize.py`，它会使用 `src/visualization/plotter.py` 进行绘图。)

## 12. 配置管理

项目采用分层配置策略，通过 `src/utils/config_loader.py` 实现多层YAML文件的合并加载，确保配置的灵活性和可维护性：

1.  **加载顺序：**
    *   首先加载 `configs/default.yaml` 作为所有参数的基准。
    *   然后加载 `configs/main_config.yaml`，其参数会覆盖 `default.yaml` 中同名的参数。
    *   最后加载模型特定的配置文件（如 `configs/models/end_to_end_model.yaml` 或 `configs/models/v1_grid_classifier_model.yaml`），其参数会进一步覆盖之前的设定。
2.  **优势：**
    *   **易于管理：** 默认值集中管理，实验特定参数通过覆盖机制方便修改。
    *   **可复用性：** 可以在不同实验中复用大部分默认配置，只需修改少量关键参数。
    *   **可追踪性：** 每次运行都基于明确的配置文件，便于复现实验结果。

## 13. 数据管理

为了高效处理和管理大规模轨迹数据和环境信息，项目采取以下数据管理策略：

1.  **分层存储：** `data/` 目录下分为 `raw/` (原始数据), `interim/` (中间处理数据), `processed/` (模型直接可用的处理后数据)。
2.  **LMDB：** 处理后的数据集（包括轨迹序列、ROI图像、标签等）统一存储为LMDB格式，以充分利用内存映射特性，提高数据加载速度。
3.  **归一化：** 所有数值型特征在预处理阶段进行归一化，其统计参数（均值、标准差）随处理后的数据集一起保存，确保模型训练和推理时使用一致的归一化方式。

## 14. 模型开发与管理

1.  **模块化设计：** 所有神经网络模型及其核心组件都定义在 `src/models/` 目录下，作为独立的 `nn.Module` 类。这促进了代码的复用性和清晰度。
2.  **训练器封装：** `src/engine/trainer.py` 封装了通用的训练逻辑，与具体模型解耦，便于切换和管理不同模型。
3.  **检查点：** 训练过程中，模型的权重（state_dict）会定期或在验证损失改善时保存到 `checkpoints/` 目录下，便于中断续训或加载模型进行推理。
4.  **版本迭代：** 不同的模型版本（如 V1 Grid Classifier, V4 MDN, End-to-End, CNN-RNN Fusion）通过独立的配置文件 (`configs/models/`) 和模型定义文件 (`src/models/`) 进行管理，便于并行开发和比较。特别是 `GridClassifierV1` 模型，我们已经为其重建了骨架并配置了独立的YAML文件。 