# Python
*.pyc
__pycache__/

# Logs
*.log
logs/

# Data and Models
data/processed_lmdb/
data/processed_lmdb_final/
data/processed_lmdb_old/
data/processed_lmdb_test/
data/processed_lmdb_v_aggregated/
data/processed_lmdb_v_batch/
data/processed_lmdb_v2/
checkpoints/
models/
outputs/
inference_results/
data/normalization_stats.pkl
data/v2_normalization_stats.pkl
data/stats.pkl
data/aggregated_features/
data/lmdb_v2/
data/preprocessed/
data/trajectories/*.png
wget-log
wget-log.1

# Virtual Environment
wargame/
bin/micromamba

# IDE and Editor files
.cursor/
*.swp
.idea/
.vscode/
*.bak
*.mdc

# Operating System files
.DS_Store
Thumbs.db 
# Data files
data/processed_lmdb*
data/trajectories_with_env/
data/trajectories/

# Model checkpoints
checkpoints/

# Training runs and logs
runs/
logs/

# Outputs and results
outputs/
inference_results/

# Other
pecnet_optuna_study.db
*.pyc
__pycache__/

# Ignore entire data and environment folders
/data/
/environment/

