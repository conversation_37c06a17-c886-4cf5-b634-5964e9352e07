#!/usr/bin/env python3
"""
可视化当前模型的预测效果
"""
import sys
import os
sys.path.append('.')

import torch
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import pickle
import glob
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.v3_masked_autoregressive.model import MaskedAutoregressiveModel

def load_best_model():
    """加载最佳模型"""
    print("=== 加载最佳模型 ===")
    
    # 查找最新的检查点
    checkpoint_dir = Path("checkpoints/v3_masked_autoregressive")
    if not checkpoint_dir.exists():
        print("❌ 检查点目录不存在")
        return None, None, None
    
    checkpoint_files = list(checkpoint_dir.glob("best_model_*.pth"))
    if not checkpoint_files:
        print("❌ 没有找到最佳模型检查点")
        return None, None, None
    
    # 选择最新的检查点
    latest_checkpoint = max(checkpoint_files, key=lambda x: x.stat().st_mtime)
    print(f"✅ 找到最佳模型: {latest_checkpoint}")
    
    # 加载配置
    config = load_config('configs/main_config.yaml', 'configs/models/v3_masked_autoregressive.yaml')
    
    # 加载归一化统计
    normalization_stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    with open(normalization_stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = MaskedAutoregressiveModel(config.model, normalization_stats)
    
    # 加载权重
    try:
        checkpoint = torch.load(latest_checkpoint, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device)
        model.eval()
        
        epoch = checkpoint.get('epoch', 'unknown')
        val_loss = checkpoint.get('val_loss', 'unknown')
        print(f"✅ 模型加载成功")
        print(f"  训练轮次: {epoch}")
        print(f"  验证损失: {val_loss}")
        
        return model, config, normalization_stats
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None, None

def denormalize_trajectory(normalized_traj, normalization_stats, use_target_stats=True):
    """反归一化轨迹"""
    if use_target_stats and 'target_mean' in normalization_stats:
        mean_x = normalization_stats['target_mean']['x']
        std_x = normalization_stats['target_std']['x']
        mean_y = normalization_stats['target_mean']['y']
        std_y = normalization_stats['target_std']['y']
    else:
        mean_x = normalization_stats['history_mean']['x']
        std_x = normalization_stats['history_std']['x']
        mean_y = normalization_stats['history_mean']['y']
        std_y = normalization_stats['history_std']['y']
    
    denorm_traj = normalized_traj.clone()
    denorm_traj[..., 0] = denorm_traj[..., 0] * std_x + mean_x
    denorm_traj[..., 1] = denorm_traj[..., 1] * std_y + mean_y
    
    return denorm_traj

def predict_and_visualize(model, config, normalization_stats, num_samples=6):
    """预测并可视化结果"""
    print(f"\n=== 预测并可视化 ===")
    
    device = next(model.parameters()).device
    
    # 创建验证数据集
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=True, 
                           collate_fn=LMDBDataset.collate_fn)
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    predictions = []
    ground_truths = []
    histories = []
    errors = []
    
    sample_count = 0
    with torch.no_grad():
        for batch in val_loader:
            if sample_count >= num_samples or batch is None:
                break
            
            # 提取数据
            history_features = batch['history_features'].to(device)  # [1, seq_len, 2]
            ground_truth = batch['ground_truth_trajectory'].to(device)  # [1, pred_len, 2]
            history_mask = batch['history_mask'].to(device)  # [1, seq_len]
            environment_roi = batch['environment_roi'].to(device)  # [1, seq_len, 13, 9, 9]
            
            try:
                # 模型预测
                predicted_trajectory = model(history_features, environment_roi, history_mask)
                
                # 转换为CPU并去掉batch维度
                history_cpu = history_features[0].cpu()
                pred_cpu = predicted_trajectory[0].cpu()
                gt_cpu = ground_truth[0].cpu()
                mask_cpu = history_mask[0].cpu()
                
                # 获取有效历史数据
                valid_indices = torch.where(mask_cpu)[0]
                valid_history = history_cpu[valid_indices]
                
                if len(valid_history) == 0:
                    continue
                
                # 反归一化
                history_physical = denormalize_trajectory(valid_history, normalization_stats, use_target_stats=False)
                pred_physical = denormalize_trajectory(pred_cpu, normalization_stats, use_target_stats=True)
                gt_physical = denormalize_trajectory(gt_cpu, normalization_stats, use_target_stats=True)
                
                # 计算误差
                error = torch.norm(pred_physical - gt_physical, dim=1).mean().item()
                errors.append(error)
                
                # 存储结果
                predictions.append(pred_physical)
                ground_truths.append(gt_physical)
                histories.append(history_physical)
                
                # 可视化
                ax = axes[sample_count]
                
                # 绘制历史轨迹
                ax.plot(history_physical[:, 0].numpy(), history_physical[:, 1].numpy(), 
                       'b-o', markersize=2, linewidth=2, label='历史轨迹', alpha=0.7)
                
                # 绘制真实轨迹
                ax.plot(gt_physical[:, 0].numpy(), gt_physical[:, 1].numpy(), 
                       'g-', linewidth=3, label='真实轨迹', alpha=0.8)
                
                # 绘制预测轨迹
                ax.plot(pred_physical[:, 0].numpy(), pred_physical[:, 1].numpy(), 
                       'r--', linewidth=2, label='预测轨迹', alpha=0.8)
                
                # 标记关键点
                ax.plot(history_physical[0, 0].numpy(), history_physical[0, 1].numpy(), 
                       'go', markersize=8, label='起点')
                ax.plot(history_physical[-1, 0].numpy(), history_physical[-1, 1].numpy(), 
                       'bo', markersize=8, label='观测终点')
                ax.plot(gt_physical[-1, 0].numpy(), gt_physical[-1, 1].numpy(), 
                       'g*', markersize=12, label='真实终点')
                ax.plot(pred_physical[-1, 0].numpy(), pred_physical[-1, 1].numpy(), 
                       'r*', markersize=12, label='预测终点')
                
                # 计算统计信息
                hist_len = len(history_physical)
                pred_len = len(pred_physical)
                
                if len(history_physical) > 1:
                    hist_distance = torch.norm(torch.diff(history_physical, dim=0), dim=1).sum().item()
                else:
                    hist_distance = 0
                    
                if len(pred_physical) > 1:
                    pred_distance = torch.norm(torch.diff(pred_physical, dim=0), dim=1).sum().item()
                    gt_distance = torch.norm(torch.diff(gt_physical, dim=0), dim=1).sum().item()
                else:
                    pred_distance = 0
                    gt_distance = 0
                
                # 终点误差
                endpoint_error = torch.norm(pred_physical[-1] - gt_physical[-1]).item()
                
                ax.set_title(f'样本 {sample_count + 1}\n'
                           f'历史: {hist_len}点, {hist_distance:.0f}m\n'
                           f'预测: {pred_len}点, {pred_distance:.0f}m (真实: {gt_distance:.0f}m)\n'
                           f'平均误差: {error:.0f}m, 终点误差: {endpoint_error:.0f}m', 
                           fontsize=10)
                ax.set_xlabel('X坐标 (m)')
                ax.set_ylabel('Y坐标 (m)')
                ax.legend(fontsize=8)
                ax.grid(True, alpha=0.3)
                ax.axis('equal')
                
                sample_count += 1
                
            except Exception as e:
                print(f"预测样本 {sample_count} 失败: {e}")
                continue
    
    # 隐藏多余的子图
    for j in range(sample_count, len(axes)):
        axes[j].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('model_predictions_visualization.png', dpi=300, bbox_inches='tight')
    print(f"✅ 可视化结果已保存到: model_predictions_visualization.png")
    
    # 统计分析
    if errors:
        print(f"\n=== 预测性能分析 ===")
        print(f"样本数量: {len(errors)}")
        print(f"平均误差: {np.mean(errors):.0f}米")
        print(f"误差标准差: {np.std(errors):.0f}米")
        print(f"误差范围: [{np.min(errors):.0f}, {np.max(errors):.0f}]米")
        
        # 与训练损失对比
        current_val_error = np.sqrt(400000000)  # 当前验证损失对应的RMS误差
        print(f"\n与训练损失对比:")
        print(f"  当前验证损失对应误差: {current_val_error:.0f}米")
        print(f"  实际预测平均误差: {np.mean(errors):.0f}米")
        
        if np.mean(errors) < current_val_error:
            print(f"  ✅ 实际误差小于损失函数显示的误差")
        else:
            print(f"  ⚠️  实际误差与损失函数一致")
    
    return predictions, ground_truths, histories, errors

def analyze_prediction_patterns(predictions, ground_truths, histories):
    """分析预测模式"""
    print(f"\n=== 预测模式分析 ===")
    
    if not predictions:
        print("❌ 没有预测结果可分析")
        return
    
    # 分析预测趋势
    straight_line_count = 0
    curved_count = 0
    
    for i, (pred, gt, hist) in enumerate(zip(predictions, ground_truths, histories)):
        # 分析预测轨迹的曲率
        if len(pred) > 2:
            pred_diffs = torch.diff(pred, dim=0)
            pred_angles = torch.atan2(pred_diffs[:, 1], pred_diffs[:, 0])
            
            if len(pred_angles) > 1:
                angle_changes = torch.diff(pred_angles)
                # 处理角度跳跃
                angle_changes = torch.where(angle_changes > np.pi, angle_changes - 2*np.pi, angle_changes)
                angle_changes = torch.where(angle_changes < -np.pi, angle_changes + 2*np.pi, angle_changes)
                
                avg_curvature = torch.abs(angle_changes).mean().item()
                
                if avg_curvature < 0.1:  # 基本直线
                    straight_line_count += 1
                else:
                    curved_count += 1
    
    print(f"预测模式统计:")
    print(f"  直线型预测: {straight_line_count}/{len(predictions)} ({straight_line_count/len(predictions)*100:.1f}%)")
    print(f"  曲线型预测: {curved_count}/{len(predictions)} ({curved_count/len(predictions)*100:.1f}%)")
    
    if straight_line_count > curved_count:
        print(f"  ⚠️  模型倾向于预测直线，可能缺乏复杂轨迹建模能力")
    else:
        print(f"  ✅ 模型能够预测复杂轨迹")

def main():
    print("=== 可视化当前模型预测效果 ===")
    
    # 1. 加载最佳模型
    model, config, normalization_stats = load_best_model()
    
    if model is None:
        print("❌ 无法加载模型，退出")
        return
    
    # 2. 预测并可视化
    predictions, ground_truths, histories, errors = predict_and_visualize(
        model, config, normalization_stats, num_samples=6
    )
    
    # 3. 分析预测模式
    analyze_prediction_patterns(predictions, ground_truths, histories)
    
    # 4. 给出改进建议
    print(f"\n=== 改进建议 ===")
    if errors and np.mean(errors) > 10000:  # 平均误差超过10公里
        print("🚨 当前自回归模型效果很差，建议:")
        print("1. 考虑一次性预测模型 (Non-autoregressive)")
        print("2. 实现目标引导机制")
        print("3. 增强注意力机制")
        print("4. 考虑分层预测架构")
    elif errors and np.mean(errors) > 5000:  # 平均误差5-10公里
        print("⚠️  当前模型有改进空间，建议:")
        print("1. 优化损失函数 (多尺度损失)")
        print("2. 增加目标引导机制")
        print("3. 改进注意力机制")
    else:
        print("✅ 当前模型表现尚可，可以进行微调优化")
    
    print(f"\n🎯 可视化完成！请查看 model_predictions_visualization.png")

if __name__ == "__main__":
    main()
