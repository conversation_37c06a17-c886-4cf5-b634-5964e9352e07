# @Time    : 2024/07/29
# <AUTHOR> AI Assistant
# @File    : data_preprocessing.yaml
# @Description : 项目数据预处理阶段的专用配置文件。
# @用途 :
#   本配置文件集中定义了数据预处理流水线的各项参数，包括：
#   - 数据路径配置：原始轨迹、环境数据和处理后LMDB数据的存储路径。
#   - 数据集划分：训练集和验证集的划分比例。
#   - 轨迹处理参数：轨迹重采样率、聚合时间间隔、观测和预测时间长度。
#   - 环境ROI提取：环境感兴趣区域的像素尺寸、地表覆盖类别和映射。
#   - LMDB数据库设置：LMDB数据库的容量。
#   - 特征定义：历史轨迹特征、运动学特征和环境地图特征。
# @原理 :
#   通过YAML格式定义结构化的数据预处理参数，使得数据管道的配置独立于模型和训练配置。
#   这提高了配置的模块化和可维护性，便于对数据预处理流程进行独立的调整和实验。
# @输入 :
#   无直接输入，本文件作为数据预处理脚本（如 `scripts/data_prep/preprocess_main.py`）的参数源。
# @输出 :
#   为数据预处理流程提供详细的配置参数，指导数据的读取、转换、特征工程和存储。

data_preprocessing:
  # 数据路径配置
  output_path: "data/processed_lmdb_obs_5min_pred_40min_v3_with_mask"
  clear_existing_data: true
  val_split_ratio: 0.2
  num_processing_workers: 8

  # --- 轨迹处理配置 ---
  trajectory:
    path: "data/trajectories_with_env"
    freq_hz: 1 # 轨迹数据的频率

  # --- 环境数据配置 ---
  environment:
    path: "environment"
    super_window_margin_m: 200
    roi_size_m: 270 # (9 pixels * 30 m/pixel)
    grid_resolution_m: 30
    num_features: 13 # DEM, Slope, Aspect(sin/cos), LandCover(9 classes) -> 1+1+2+9=13
    env_paths:
      dem: "environment/dem_aligned.tif"
      landcover: "environment/landcover_aligned.tif"
      slope: "environment/slope_aligned.tif"
      aspect: "environment/aspect_aligned.tif"
    landcover_classes:
      - 10
      - 20
      - 30
      - 40
      - 50
      - 60
      - 80
      - 90
      - 255
    landcover_mapping:
      10: 'water'
      20: 'wetland'
      30: 'grassland'
      40: 'shrub'
      50: 'urban'
      60: 'cropland'
      80: 'forest'
      90: 'barren'
      255: 'unclassified'

  # --- 事件驱动掩码配置 ---
  masking:
    max_history_len_s: 3600      # 模型能处理的最大历史长度 (秒), e.g., 60分钟
    min_duration_min: 3          # 观测窗口最小持续时间 (分钟)
    max_duration_min: 5          # 观测窗口最大持续时间 (分钟)
    min_gap_duration_min: 3      # 间歇窗口最小持续时间 (分钟)
    max_gap_duration_min: 5      # 间歇窗口最大持续时间 (分钟)

  prediction_horizon_s: 1200   # 预测时长 (秒), e.g., 20分钟
  lmdb_map_size_gb: 100        # LMDB数据库的最大容量 (GB)

  # 定义历史轨迹的基础特征，这些将在聚合后变为单点特征
  history_features: ['x', 'y', 'velocity_x', 'velocity_y', 'acceleration_x', 'acceleration_y', 'heading_sin', 'heading_cos', 'curvature', 'dem_agg', 'slope_agg', 'aspect_sin_agg', 'aspect_cos_agg', 'lc_class_10', 'lc_class_20', 'lc_class_30', 'lc_class_40', 'lc_class_50', 'lc_class_60', 'lc_class_80', 'lc_class_90', 'lc_class_255']

  # Feature and Map Configuration
  kinematic_cols: ['velocity_x', 'velocity_y', 'acceleration_x', 'acceleration_y', 'heading', 'curvature']
  env_maps:
    dem: "dem_aligned.tif"
    landcover: "landcover_aligned.tif"
    slope: "slope_aligned.tif"
    aspect: "aspect_aligned.tif" 