# '''
# @Time    : 2024/07/29
# <AUTHOR> AI Assistant
# @File    : config.yaml
# @Description : 项目的主配置文件，集中管理数据预处理、模型和训练过程的各项参数。
# @用途 :
#   本配置文件定义了整个轨迹预测项目的各项关键参数，包括：
#   - 调试选项，如是否使用小型数据集进行快速测试。
#   - 数据预处理阶段的配置，如轨迹和环境数据的路径、输出路径、数据集划分比例、时间窗口设置、环境ROI尺寸、LMDB数据库大小以及特征列定义。
#   - 模型构建的相关参数（待完善）。
#   - 训练过程中的超参数，如批量大小、学习率和训练轮数。
# @原理 :
#   通过YAML格式定义结构化的配置参数，使得项目的各项设置易于管理和修改。
#   不同的配置节（如 `debugging`, `data_preprocessing`, `modeling`, `training`）逻辑分离，提高了配置的清晰度和可维护性。
# @输入 :
#   无直接输入，本文件作为程序启动时的配置参数源。
# @输出 :
#   为项目中的不同模块提供配置参数，指导数据加载、模型构建、训练和评估流程。
# @处理方法 :
#   通过配置加载器（例如 `src/utils/config_loader.py`）解析本文件，将参数载入到程序中供各模块使用。
# '''
# =================================================================
#                 Debugging Configuration
# =================================================================
debugging:
  use_small_dataset: false # 设置为true时，只使用15个轨迹文件进行快速测试
  num_files_to_use: 100 # 新增：指定用于生成数据集的CSV文件数量，设置为-1则使用全部文件

# =================================================================
#                 Data Preprocessing Configuration
# =================================================================

data_preprocessing:
  # 数据路径配置
  trajectory_path: "data/trajectories_with_env" # 修改为包含环境数据的轨迹目录
  environment_path: "environment"
  output_path: "data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq"
  clear_existing_data: true
  val_split_ratio: 0.2 # 新增：训练集和验证集划分比例
  super_window_margin_m: 200 # 新增：环境超级窗口的额外边距（米）
  trajectory_resample_rate_hz: 1 # 新增：轨迹原始采样率 (Hz)
  
  # Resampling and Kinematics
  agg_interval_s: 10
  observation_horizon_s: 1800 # 修改为 1800 秒 (30分钟)
  prediction_horizon_s: 1200 # 修改为 1200 秒 (20分钟)
  env_roi_size_pixels: 9 # 新增：环境ROI的像素尺寸 (例如 9x9 像素)
  lmdb_map_size_gb: 5 # 增加LMDB数据库的容量，暂时降低用于调试
  num_processing_workers: 8 # 新增：数据处理工作者数量

  # 定义历史轨迹的基础特征，这些将在聚合后变为单点特征
  history_features: ['x', 'y', 'velocity_x', 'velocity_y', 'acceleration_x', 'acceleration_y', 'heading_sin', 'heading_cos', 'curvature', 'dem_agg', 'slope_agg', 'aspect_sin_agg', 'aspect_cos_agg', 'lc_class_10', 'lc_class_20', 'lc_class_30', 'lc_class_40', 'lc_class_50', 'lc_class_60', 'lc_class_80', 'lc_class_90', 'lc_class_255']
  
  # Columns
  # --- Environment ROI Configuration ---
  # The pixel dimension of the square Region of Interest (ROI) extracted around the agent.
  # E.g., 64 means a 64x64 pixel map will be extracted.
  # roi_size: 64 # This line is removed as per the edit hint

  # --- Feature and Map Configuration ---
  # List of kinematic features to be calculated and normalized.
  # These names must match the columns created in the `calculate_kinematics_and_point_env` function.
  kinematic_cols: ['velocity_x', 'velocity_y', 'acceleration_x', 'acceleration_y', 'heading', 'curvature']
  # Environment Map Paths (相对于 environment/ 目录)
  env_maps:
    dem: "dem_aligned.tif" # 更新为正确的文件名
    landcover: "landcover_aligned.tif" # 更新为正确的文件名
    slope: "slope_aligned.tif" # 更新为正确的文件名
    aspect: "aspect_aligned.tif" # 更新为正确的文件名

  # 核心修复：使用从数据中扫描出的真实地表覆盖类别值
  landcover_classes:
    - 10
    - 20
    - 30
    - 40
    - 50
    - 60
    - 80
    - 90
    - 255

  landcover_mapping:
    1: 'forest'
    2: 'shrub'
    3: 'grassland'
    4: 'barren'
    5: 'urban'
    6: 'water'
    7: 'wetland'
    8: 'cropland'

  # 轨迹数据配置
  trajectory:
    freq_hz: 1  # 轨迹采样频率 (Hz)
    min_trajectory_length: 10  # 最小轨迹长度 (秒)
    max_trajectory_length: 7200  # 最大轨迹长度 (秒)

  # 掩码配置
  masking:
    max_history_len_s: 120  # 最大历史长度 (秒)
    min_history_len_s: 30   # 最小历史长度 (秒)
    min_duration_min: 1     # 最小持续时间 (分钟)
    max_duration_min: 60    # 最大持续时间 (分钟)
    min_gap_duration_min: 0.5 # 最小间歇时间 (分钟)
    max_gap_duration_min: 5   # 最大间歇时间 (分钟)

# =================================================================
#                 Model Configuration
# =================================================================

modeling:
  lmdb_path: "data/processed_lmdb/data.lmdb" # 添加LMDB数据库路径
  obs_len: 20
  pred_len: 10
  rnn_hidden_size: 256 # 增加RNN隐藏层大小
  num_rnn_layers: 3 # 增加RNN层数
  # num_mixture: 3 # 移除此行，以v1_grid_classifier_model.yaml为准
  goal_embedding_dim: 64 # 新增：目标嵌入维度
  dropout_prob: 0.1 # 新增：Dropout概率
  trajectory_features: ['velocity_x', 'velocity_y', 'acceleration_x', 'acceleration_y', 'heading', 'curvature']

# =================================================================
#                 V5 Model Configuration
# =================================================================

model:
  # 基础参数
  d_model: 256  # 模型维度
  n_heads: 8    # 注意力头数
  n_layers: 4   # 编码器层数
  prediction_horizon: 120  # 预测点数（聚合后）：1200s ÷ 10s = 120
  max_history_len: 360     # 最大历史长度（聚合后）：3600s ÷ 10s = 360

  # 环境特征
  env_channels: 13  # 环境ROI通道数

  # 训练参数
  dropout: 0.1
  learning_rate: 0.001
  weight_decay: 1e-4

# =================================================================
#                 Training Configuration
# =================================================================

training:
  batch_size: 512 # 批次大小设为512 (原64，增大8倍)
  learning_rate: 0.0001 # 学习率从0.00008提高到0.0001
  epochs: 10 # 这里的epochs只是一个参考值，实际由num_epochs控制
  num_epochs: 100 # 总训练epoch数
  log_dir: "runs/track_model_v2"
  checkpoints_root_dir: "checkpoints"
  output_root_dir: "outputs"
  early_stopping_patience: 15 # 早停耐心值从10增加到15
  optimizer: AdamW # 明确使用AdamW
  weight_decay: 0.0005 # 新增：L2正则化参数，用于AdamW优化器，帮助防止过拟合
  scheduler:
    enabled: true
    mode: min
    factor: 0.5
    patience: 10
    verbose: true
  scheduler_params:
    mode: min
    factor: 0.5
    patience: 10
    verbose: true
  num_workers: 8
  gradient_clipping:
    enabled: true
    max_norm: 1.0
  checkpoint_dir: "checkpoints" # 新增：模型检查点保存目录
  val_interval: 1 # 新增：验证间隔，每1个epoch验证一次
  save_interval: 10 # 新增：模型保存间隔，每10个epoch保存一次
  reconstruction_loss_weight: 1.0 # 这个现在是总的重建损失权重，将被拆分
  kl_loss_weight: 0.05 # 从 0.01 提高到 0.05
  trajectory_reconstruction_weight: 1.0 # 新增：轨迹重建损失权重
  endpoint_reconstruction_weight: 1.0 # 将终点重建损失权重从 2.0 降低到 1.0，使其与轨迹损失保持一致，进一步平衡
  # V2 意图引导模型的损失权重
  LAMBDA_INTENT: 1.0
  LAMBDA_TRAJ: 1.0 