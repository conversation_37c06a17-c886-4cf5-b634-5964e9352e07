# '''
# @Time    : 2024/07/29
# <AUTHOR> AI Assistant
# @File    : default.yaml
# @Description : 项目的默认配置文件，为数据预处理、模型和训练过程提供标准参数。
# @用途 :
#   本默认配置文件提供了轨迹预测项目的各项标准参数，作为 `config.yaml` 的补充或基础配置。
#   它包含了：
#   - 调试选项，例如是否使用小型数据集进行快速测试。
#   - 数据预处理阶段的详细配置，包括数据路径、输出路径、数据集划分、时间窗口、环境ROI尺寸、LMDB容量、地表覆盖类别和特征定义。
#   - 模型架构和MDN（混合密度网络）参数，如隐藏层大小、预测维度、混合高斯数量、目标嵌入维度和Dropout概率。
#   - 训练过程的超参数，例如批量大小、学习率、训练轮数、DataLoader的worker数量、梯度裁剪、早停机制和教师强制比率设置。
# @原理 :
#   通过YAML格式定义结构化的默认配置参数，当主配置文件 `config.yaml` 中未指定某项参数时，将使用 `default.yaml` 中的对应值。
#   这种分层配置机制使得配置管理更加灵活，可以方便地覆盖默认值或扩展配置。
# @输入 :
#   无直接输入，本文件作为程序启动时的默认配置参数源。
# @输出 :
#   为项目中的不同模块提供默认配置参数，确保即使没有完整的自定义配置也能正常运行。
# @处理方法 :
#   配置加载器（例如 `src/utils/config_loader.py`）会先加载此默认配置文件，然后加载 `config.yaml`，并用后者中的值覆盖前者中重复的参数。
# '''
# =================================================================
#                 Debugging Configuration
# =================================================================
debugging:
  use_small_dataset: false # 设置为false时，使用所有轨迹文件

# =================================================================
#                 Model Configuration
# =================================================================

modeling:
  # lmdb_path: "data/processed_lmdb/data.lmdb" # 添加LMDB数据库路径
  # obs_len: 20 # 已在 data_preprocessing 中定义
  # pred_len: 10 # 已在 data_preprocessing 中定义
  rnn_hidden_size: 128 # 添加 RNN 隐藏层大小
  prediction_dim: 2 # 模型预测的维度，通常是 (x, y)
  num_rnn_layers: 2 # RNN层数
  num_mixture: 3 # MDN混合高斯的数量
  goal_embedding_dim: 8 # 相对目标嵌入维度
  dropout_prob: 0.2 # Dropout概率
  # trajectory_features: ['velocity_x', 'velocity_y', 'acceleration_x', 'acceleration_y', 'heading', 'curvature'] # 已在 data_preprocessing 中定义
  # Model Architecture
  # ...

# =================================================================
#                 Training Configuration
# =================================================================

training:
  output_root_dir: "outputs" # 训练日志和TensorBoard的根目录
  checkpoints_root_dir: "checkpoints" # 模型检查点保存的根目录
  batch_size: 64
  learning_rate: 0.001
  epochs: 10 # 添加训练轮数 (旧的，用 num_epochs 替代)
  num_epochs: 50 # 实际训练轮数
  num_workers: 8 # DataLoader的worker数量
  grad_clip_norm: 1.0 # 梯度裁剪的范数阈值 (0为不裁剪)
  early_stopping_patience: 10 # 早停机制的耐心值
  teacher_forcing_ratio_start: 1.0 # 教师强制起始比率
  teacher_forcing_ratio_decay: 0.01 # 教师强制每epoch衰减值
  # ... 
