#!/usr/bin/env python3
"""
V5模型分步分析 - 更稳定的版本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import lmdb
import pickle

from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def step1_load_model():
    """步骤1: 加载V5模型"""
    print("🤖 步骤1: 加载V5模型")
    
    config = load_config('configs/main_config.yaml')
    
    # 修正配置以匹配检查点
    config.model.max_history_len = 360
    config.model.prediction_horizon = 120
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    model = TrajectoryPredictorV5(config).to(device)
    
    # 加载检查点
    checkpoint_path = "models/trajectory_predictor_v5_best.pth"
    
    if os.path.exists(checkpoint_path):
        print(f"加载检查点: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        
        print(f"✅ 模型加载成功!")
        print(f"  训练轮次: {checkpoint.get('epoch', 'unknown')}")
        print(f"  验证损失: {checkpoint.get('val_loss', 'unknown'):.4f}")
        print(f"  模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        return model, device, config, checkpoint
    else:
        print("❌ 未找到模型检查点")
        return None, None, None, None

def step2_test_single_prediction(model, device, config):
    """步骤2: 测试单个样本预测"""
    print("\n📊 步骤2: 测试单个样本预测")
    
    model.eval()
    
    # 加载数据
    data_path = config.data_preprocessing.output_path + '/val'
    if not os.path.exists(data_path):
        print(f"❌ 数据路径不存在: {data_path}")
        return None
    
    env = lmdb.open(data_path, readonly=True, lock=False)
    
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        key, value = cursor.item()
        
        try:
            sample_data = pickle.loads(value)
            print(f"✅ 成功加载样本")
            print(f"  样本键: {list(sample_data.keys())}")
            
            # 检查数据形状
            for k, v in sample_data.items():
                if hasattr(v, 'shape'):
                    print(f"  {k}: {v.shape}")
            
            # 准备数据
            def ensure_tensor(data):
                if isinstance(data, torch.Tensor):
                    return data.float()
                elif isinstance(data, np.ndarray):
                    return torch.from_numpy(data).float()
                else:
                    return torch.tensor(data).float()
            
            history_features = ensure_tensor(sample_data['history_features']).unsqueeze(0).to(device)
            history_mask = torch.ones(1, history_features.shape[1], dtype=torch.bool).to(device)
            ground_truth_trajectory = ensure_tensor(sample_data['ground_truth_trajectory']).unsqueeze(0).to(device)
            ground_truth_destination = ensure_tensor(sample_data['ground_truth_destination']).unsqueeze(0).to(device)
            environment_roi = ensure_tensor(sample_data['environment_roi']).unsqueeze(0).to(device)
            
            print(f"  输入形状:")
            print(f"    history_features: {history_features.shape}")
            print(f"    history_mask: {history_mask.shape}")
            print(f"    ground_truth_trajectory: {ground_truth_trajectory.shape}")
            print(f"    environment_roi: {environment_roi.shape}")
            
            # 模型预测
            with torch.no_grad():
                predicted_trajectory = model(
                    history_features=history_features,
                    history_mask=history_mask,
                    ground_truth_destination=ground_truth_destination,
                    environment_roi=environment_roi
                )
            
            print(f"  预测输出形状: {predicted_trajectory.shape}")
            
            # 计算误差
            criterion = nn.MSELoss()
            loss = criterion(predicted_trajectory, ground_truth_trajectory)
            
            pred_np = predicted_trajectory[0].cpu().numpy()
            gt_np = ground_truth_trajectory[0].cpu().numpy()
            
            # 计算距离误差
            distances = np.sqrt(np.sum((pred_np - gt_np) ** 2, axis=1))
            final_distance = distances[-1]
            avg_distance = np.mean(distances)
            
            print(f"✅ 单样本预测成功!")
            print(f"  MSE损失: {loss.item():.4f}")
            print(f"  平均距离误差: {avg_distance:.4f}")
            print(f"  最终距离误差: {final_distance:.4f}")
            
            env.close()
            return {
                'prediction': pred_np,
                'ground_truth': gt_np,
                'loss': loss.item(),
                'avg_distance': avg_distance,
                'final_distance': final_distance
            }
            
        except Exception as e:
            print(f"❌ 样本处理失败: {e}")
            env.close()
            return None

def step3_create_simple_visualization(result):
    """步骤3: 创建简单可视化"""
    print("\n🎨 步骤3: 创建简单可视化")
    
    if result is None:
        print("❌ 没有结果可视化")
        return
    
    pred = result['prediction']
    gt = result['ground_truth']
    
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # 轨迹对比图
    axes[0].plot(gt[:, 0], gt[:, 1], 'g-', linewidth=3, label='真实轨迹', alpha=0.8)
    axes[0].plot(pred[:, 0], pred[:, 1], 'r--', linewidth=3, label='预测轨迹', alpha=0.8)
    axes[0].scatter(gt[0, 0], gt[0, 1], c='green', s=150, marker='s', label='起点', zorder=5)
    axes[0].scatter(gt[-1, 0], gt[-1, 1], c='green', s=150, marker='*', label='真实终点', zorder=5)
    axes[0].scatter(pred[-1, 0], pred[-1, 1], c='red', s=150, marker='*', label='预测终点', zorder=5)
    
    axes[0].set_title(f'轨迹预测对比 (FDE: {result["final_distance"]:.3f})')
    axes[0].set_xlabel('X坐标')
    axes[0].set_ylabel('Y坐标')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    axes[0].axis('equal')
    
    # 误差随时间变化
    distances = np.sqrt(np.sum((pred - gt) ** 2, axis=1))
    time_steps = np.arange(len(distances))
    
    axes[1].plot(time_steps, distances, 'b-', linewidth=2, label='位置误差')
    axes[1].axhline(result['avg_distance'], color='red', linestyle='--', 
                   label=f'平均误差: {result["avg_distance"]:.3f}')
    axes[1].set_title('误差随时间变化')
    axes[1].set_xlabel('时间步')
    axes[1].set_ylabel('位置误差')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('v5_simple_test.png', dpi=300, bbox_inches='tight')
    print("✅ 可视化已保存: v5_simple_test.png")
    plt.show()

def step4_batch_evaluation(model, device, config, max_samples=20):
    """步骤4: 批量评估"""
    print(f"\n📈 步骤4: 批量评估 (最多{max_samples}个样本)")
    
    model.eval()
    
    # 加载数据
    data_path = config.data_preprocessing.output_path + '/val'
    env = lmdb.open(data_path, readonly=True, lock=False)
    
    all_losses = []
    all_distances = []
    all_final_distances = []
    
    criterion = nn.MSELoss()
    
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        
        sample_count = 0
        for key, value in cursor:
            if sample_count >= max_samples:
                break
                
            try:
                sample_data = pickle.loads(value)
                
                # 准备数据
                def ensure_tensor(data):
                    if isinstance(data, torch.Tensor):
                        return data.float()
                    elif isinstance(data, np.ndarray):
                        return torch.from_numpy(data).float()
                    else:
                        return torch.tensor(data).float()
                
                history_features = ensure_tensor(sample_data['history_features']).unsqueeze(0).to(device)
                history_mask = torch.ones(1, history_features.shape[1], dtype=torch.bool).to(device)
                ground_truth_trajectory = ensure_tensor(sample_data['ground_truth_trajectory']).unsqueeze(0).to(device)
                ground_truth_destination = ensure_tensor(sample_data['ground_truth_destination']).unsqueeze(0).to(device)
                environment_roi = ensure_tensor(sample_data['environment_roi']).unsqueeze(0).to(device)
                
                # 模型预测
                with torch.no_grad():
                    predicted_trajectory = model(
                        history_features=history_features,
                        history_mask=history_mask,
                        ground_truth_destination=ground_truth_destination,
                        environment_roi=environment_roi
                    )
                
                # 计算损失
                loss = criterion(predicted_trajectory, ground_truth_trajectory)
                all_losses.append(loss.item())
                
                # 计算距离误差
                pred_np = predicted_trajectory[0].cpu().numpy()
                gt_np = ground_truth_trajectory[0].cpu().numpy()
                
                distances = np.sqrt(np.sum((pred_np - gt_np) ** 2, axis=1))
                all_distances.extend(distances)
                all_final_distances.append(distances[-1])
                
                sample_count += 1
                
                if sample_count % 5 == 0:
                    print(f"  处理了 {sample_count} 个样本...")
                    
            except Exception as e:
                print(f"样本处理失败: {e}")
                continue
    
    env.close()
    
    # 计算统计指标
    avg_loss = np.mean(all_losses)
    ade = np.mean(all_distances)  # Average Displacement Error
    fde = np.mean(all_final_distances)  # Final Displacement Error
    
    print(f"✅ 批量评估完成!")
    print(f"  评估样本数: {sample_count}")
    print(f"  平均MSE损失: {avg_loss:.4f}")
    print(f"  平均距离误差(ADE): {ade:.4f}")
    print(f"  最终距离误差(FDE): {fde:.4f}")
    print(f"  损失标准差: {np.std(all_losses):.4f}")
    print(f"  距离误差标准差: {np.std(all_distances):.4f}")
    
    # 百分位数分析
    print(f"\n📊 误差分布:")
    print(f"  50%误差 < {np.percentile(all_distances, 50):.4f}")
    print(f"  75%误差 < {np.percentile(all_distances, 75):.4f}")
    print(f"  90%误差 < {np.percentile(all_distances, 90):.4f}")
    print(f"  95%误差 < {np.percentile(all_distances, 95):.4f}")
    
    return {
        'avg_loss': avg_loss,
        'ade': ade,
        'fde': fde,
        'all_losses': all_losses,
        'all_distances': all_distances,
        'all_final_distances': all_final_distances,
        'sample_count': sample_count
    }

def main():
    """主函数"""
    print("🚀 V5模型分步分析开始")
    
    # 步骤1: 加载模型
    model, device, config, checkpoint = step1_load_model()
    if model is None:
        return
    
    # 步骤2: 测试单个样本
    single_result = step2_test_single_prediction(model, device, config)
    if single_result is None:
        return
    
    # 步骤3: 创建简单可视化
    step3_create_simple_visualization(single_result)
    
    # 步骤4: 批量评估
    batch_results = step4_batch_evaluation(model, device, config, max_samples=30)
    
    print("\n🎉 V5模型分析完成!")
    print("生成的文件:")
    print("  - v5_simple_test.png: 单样本预测可视化")

if __name__ == "__main__":
    main()
