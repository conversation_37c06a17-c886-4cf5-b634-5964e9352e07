'''
@Time    : 2025/07/15
<AUTHOR> AI Assistant
@File    : src/models/trajectory_encoder.py
@Description : 包含用于轨迹序列编码的Transformer编码器及其位置编码模块。
@用途 :
  本模块定义了`TrajectoryEncoder`类，它使用Transformer Encoder来处理历史轨迹序列，
  从输入的轨迹片段中学习并提取装甲车的运动意图和模式。它还包含`PositionalEncoding`用于注入序列的位置信息。
@原理 :
  - `input_embed`: 将原始输入特征投影到模型维度`d_model`。
  - `PositionalEncoding`: 通过正弦函数向输入中添加位置信息，帮助模型理解序列中元素的位置。
  - `TransformerEncoder`: 使用多头自注意力机制高效捕捉序列中长距离的时序依赖和复杂运动模式。
@输入数据 :
  - `src`: 原始轨迹序列张量，形状为`(batch_size, seq_len, input_dim)`，包含轨迹点的各种特征。
  - `src_key_padding_mask`: 用于指示填充位置的布尔掩码，形状为`(batch_size, seq_len)`，其中`True`表示需要忽略的填充位置。
@输出数据 :
  - `output`: 经过Transformer编码器处理后的轨迹序列嵌入，形状为`(batch_size, seq_len, d_model)`。
@处理方法 :
  1. 将输入的轨迹特征通过线性层投影到Transformer所需的维度。
  2. 添加正弦位置编码，为序列中的每个位置提供独特的信号。
  3. 将带有位置编码的序列输入到Transformer Encoder中进行编码，捕捉序列内部的依赖关系。
'''
import torch
import torch.nn as nn

class TrajectoryEncoder(nn.Module):
    def __init__(self, input_dim, d_model=256, nhead=8, num_encoder_layers=6, dim_feedforward=2048, dropout=0.1, layer_norm_eps=1e-5):
        super().__init__()
        self.d_model = d_model
        
        # 1. Linear embedding layer
        self.input_embed = nn.Linear(input_dim, d_model)
        
        # 2. Positional Encoding
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # 3. Transformer Encoder Blocks
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, 
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True,
            norm_first=True  # Use Pre-LN Transformer for stability
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_encoder_layers)

    def forward(self, src: torch.Tensor, src_key_padding_mask: torch.Tensor) -> torch.Tensor:
        """
        Args:
            src: Tensor, shape [batch_size, seq_len, input_dim]
            src_key_padding_mask: Tensor, shape [batch_size, seq_len]

        Returns:
            output: Tensor, shape [batch_size, seq_len, d_model]
        """
        # Project input features to d_model
        src_projected = self.input_embed(src)

        # Add positional encoding
        src_pos_encoded = self.pos_encoder(src_projected)
        
        output = self.transformer_encoder(src_pos_encoded, src_key_padding_mask=src_key_padding_mask)
        return output

class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)

        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-torch.log(torch.tensor(10000.0)) / d_model))
        pe = torch.zeros(max_len, d_model)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe)

    def forward(self, x):
        """
        Args:
            x: Tensor, shape [batch_size, seq_len, d_model]
        """
        # x is not directly added with pe, but pe is sliced and added
        x = x + self.pe[:x.size(1), :].unsqueeze(0)
        return self.dropout(x)

if __name__ == '__main__':
    # Example usage
    input_dim = 22 # Matching our latest data preprocessor
    encoder = TrajectoryEncoder(input_dim=input_dim, layer_norm_eps=1e-4) # Pass the robust eps
    
    # Create a dummy batch
    src = torch.rand(32, 10, input_dim) # (batch_size, seq_len, input_dim)
    mask = torch.zeros(32, 10, dtype=torch.bool) # No padding
    
    output = encoder(src, mask)
    
    print("Trajectory Encoder initialized successfully.")
    print(f"Input shape: {src.shape}")
    print(f"Output shape: {output.shape}")
    assert output.shape == (32, 10, 256) # (batch_size, seq_len, d_model)
    print("Test passed.") 