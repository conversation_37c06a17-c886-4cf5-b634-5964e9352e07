'''
@Time    : 2025/07/15
<AUTHOR> AI Assistant
@File    : src/models/environment_extractor.py
@Description : 包含用于从环境ROI中提取特征图的轻量级CNN模型。
@用途 :
  本模块定义了`EnvironmentExtractor`类，它是一个基于ResNet风格的CNN，
  用于从输入的局部环境感兴趣区域（ROI）栅格图中提取区域性的、隐式的环境语义特征图。
@原理 :
  - `BasicBlock`: 定义了ResNet风格的基础残差块，用于构建深度网络，有助于缓解梯度消失问题。
  - `EnvironmentExtractor`: 采用一系列卷积层、批量归一化层、激活函数和池化层来逐步提取多尺度特征。
  - 使用残差连接（通过`BasicBlock`实现）来允许网络更深层次的学习。
  - 不包含最终的全局平均池化和全连接层，直接输出多通道的特征图，以便后续的特征采样或其他处理。
@输入数据 :
  - `x`: 输入的环境ROI图像张量，形状为`(batch_size, input_channels, H, W)`，其中`input_channels`通常包含DEM、坡度、坡向、地表覆盖等环境信息。
@输出数据 :
  - `feature_map`: 提取到的环境特征图，形状为`(batch_size, D_model, H', W')`，
    其中`D_model`是模型最后一层卷积的输出通道数，`H'`和`W'`是经过一系列下采样操作后的特征图尺寸。
@处理方法 :
  1. 初始卷积层和最大池化层对输入ROI进行初步特征提取和下采样。
  2. 接着通过一系列包含残差连接的卷积块（`_make_layer`构建）进一步提取更高级别的空间特征。
  3. 最终输出的是一个包含丰富环境语义信息的特征图，而不是单一的嵌入向量。
'''
import torch
import torch.nn as nn
import torch.nn.functional as F

class BasicBlock(nn.Module):
    """A basic residual block for ResNet-style architectures."""
    def __init__(self, in_channels, out_channels, stride=1):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)

        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += self.shortcut(x)
        out = F.relu(out)
        return out

class EnvironmentExtractor(nn.Module):
    """
    A lightweight ResNet-style CNN to extract a feature map from the environmental ROI.
    """
    def __init__(self, input_channels=6): # d_model is no longer an init argument
        super().__init__()
        self.in_channels = 64

        # The output dimension of the final feature map will be the channel count of the last layer
        self.d_model = 512

        # Initial convolution
        self.conv1 = nn.Conv2d(input_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm2d(64)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)

        # Residual blocks
        self.layer1 = self._make_layer(64, 2, stride=1)
        self.layer2 = self._make_layer(128, 2, stride=2)
        self.layer3 = self._make_layer(256, 2, stride=2)
        self.layer4 = self._make_layer(512, 2, stride=2)

        # We remove the final avgpool and fc layers to output a feature map

    def _make_layer(self, out_channels, num_blocks, stride):
        strides = [stride] + [1] * (num_blocks - 1)
        layers = []
        for s in strides:
            layers.append(BasicBlock(self.in_channels, out_channels, s))
            self.in_channels = out_channels
        return nn.Sequential(*layers)

    def forward(self, x):
        """
        Args:
            x (Tensor): The input environment ROI, shape [batch_size, input_channels, H, W]
        
        Returns:
            Tensor: The environment feature map, shape [batch_size, 512, H/32, W/32]
        """
        out = self.maxpool(F.relu(self.bn1(self.conv1(x))))
        out = self.layer1(out)
        out = self.layer2(out)
        out = self.layer3(out)
        feature_map = self.layer4(out)
        return feature_map

if __name__ == '__main__':
    # Example usage
    input_channels = 6 # Matching our data preprocessor
    
    model = EnvironmentExtractor(input_channels=input_channels)
    
    # Create a dummy batch
    roi = torch.rand(4, input_channels, 256, 256) # (batch_size, channels, H, W)
    
    output_map = model(roi)
    
    print("Environment Extractor initialized successfully.")
    print(f"Input shape: {roi.shape}")
    print(f"Output feature map shape: {output_map.shape}")
    # For a 256x256 input, strides are 2 (conv1), 2 (maxpool), 1, 2, 2, 2. Total stride = 32.
    # Output size should be 256/32 = 8
    assert output_map.shape == (4, model.d_model, 8, 8)
    print("Test passed.") 