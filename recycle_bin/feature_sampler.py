'''
@Time    : 2025/07/15
<AUTHOR> AI Assistant
@File    : feature_sampler.py
@Description : 根据归一化坐标从特征图中采样特征。
@用途 :
  本模块定义了`FeatureSampler`类，用于从环境特征图（如CNN编码器输出）中，根据给定的归一化坐标，通过双线性插值采样出对应的特征向量。
@原理 :
  - 利用PyTorch的`F.grid_sample`函数实现高效的采样操作。
  - `F.grid_sample`要求输入坐标在`[-1, 1]`范围内，其中`-1`代表图像的左/上边界，`1`代表右/下边界，因此需要对输入坐标进行预归一化。
  - 支持批处理，能够同时对多个样本和每个样本内的多个点进行采样。
@输入数据 :
  - `env_feature_map`: 环境特征图，通常是CNN编码器输出的多通道特征图，形状为`(B, C, H, W)`。
  - `normalized_coords`: 归一化后的采样点坐标，形状为`(B, N, 2)`，其中`N`是采样点数，坐标值在`[-1, 1]`范围内（`x, y`）。
@输出数据 :
  - `sampled_features`: 采样得到的特征，形状为`(B, N, C)`。
@处理方法 :
  1. 将输入的`normalized_coords`形状调整为`F.grid_sample`所需的`(B, N, 1, 2)`。
  2. 调用`F.grid_sample`进行双线性插值采样。
  3. 将采样结果的形状从`(B, C, N, 1)`调整回`(B, N, C)`，以便后续模型处理。
'''
import torch
import torch.nn as nn
import torch.nn.functional as F

class FeatureSampler(nn.Module):
    """
    通过双线性插值，根据归一化坐标从特征图中采样。
    """
    def __init__(self):
        super().__init__()

    def forward(self, env_feature_map: torch.Tensor, normalized_coords: torch.Tensor) -> torch.Tensor:
        """
        Args:
            env_feature_map (torch.Tensor): 环境特征图, 形状为 (B, C, H, W).
            normalized_coords (torch.Tensor): 归一化的采样点坐标, 形状为 (B, N, 2).
                                              坐标值应在 [-1, 1] 范围内.
                                              格式为 (x, y).

        Returns:
            torch.Tensor: 采样得到的特征, 形状为 (B, N, C).
        """
        # grid_sample 要求 grid 的形状是 (B, N, 1, 2)
        # B: batch size, N: number of points to sample, H_out=1, W_out=1
        # 但我们的输入是 (B, N, 2), 所以需要增加一个维度
        grid = normalized_coords.unsqueeze(2)  # 形状变为 (B, N, 1, 2)

        # 使用 F.grid_sample 进行采样
        # align_corners=False 是现代用法，将输入图像的像素视为网格线之间的方块
        sampled_features = F.grid_sample(
            env_feature_map, 
            grid, 
            mode='bilinear', 
            padding_mode='border', 
            align_corners=False
        )
        
        # sampled_features 的形状是 (B, C, N, 1)
        # 我们需要将其转换为 (B, N, C) 以便后续处理
        # 1. 移除最后一个维度: .squeeze(-1) -> (B, C, N)
        # 2. 交换 C 和 N 维度: .permute(0, 2, 1) -> (B, N, C)
        return sampled_features.squeeze(-1).permute(0, 2, 1)

if __name__ == '__main__':
    # === 测试 FeatureSampler ===
    batch_size = 4
    num_channels = 12  # 例如 DEM, landcover one-hot 等
    height, width = 256, 256
    num_points = 50  # 每个样本采样50个点

    # 1. 创建一个虚拟的环境特征图
    # (B, C, H, W)
    dummy_env_map = torch.randn(batch_size, num_channels, height, width)
    print(f"输入特征图形状: {dummy_env_map.shape}")

    # 2. 创建一批归一化的坐标
    # (B, N, 2)
    # 随机生成在 [-1, 1] 区间的坐标
    dummy_coords = torch.rand(batch_size, num_points, 2) * 2 - 1
    print(f"输入坐标形状: {dummy_coords.shape}")

    # 3. 初始化并使用采样器
    sampler = FeatureSampler()
    sampled_output = sampler(dummy_env_map, dummy_coords)
    
    # 4. 验证输出形状
    print(f"采样后输出形状: {sampled_output.shape}")
    expected_shape = (batch_size, num_points, num_channels)
    assert sampled_output.shape == expected_shape, \
        f"形状不匹配! 期望: {expected_shape}, 得到: {sampled_output.shape}"

    print("\n✅ FeatureSampler 测试通过!")

    # === 边界情况测试 ===
    # 坐标正好在角落
    corner_coords = torch.tensor([
        [[-1.0, -1.0]], # 左上
        [[1.0, -1.0]],  # 右上
        [[-1.0, 1.0]],  # 左下
        [[1.0, 1.0]],   # 右下
    ], dtype=torch.float32)

    # 为了方便验证，我们创建一个可预测的特征图
    test_map = torch.zeros(4, 1, 2, 2)
    test_map[0, 0, 0, 0] = 10 # 左上
    test_map[1, 0, 0, 1] = 20 # 右上
    test_map[2, 0, 1, 0] = 30 # 左下
    test_map[3, 0, 1, 1] = 40 # 右下
    
    # corner_coords 的 batch size 是 4，匹配 test_map
    corner_output = sampler(test_map, corner_coords)
    print(f"\n角点采样输出:\n{corner_output.squeeze(-1)}")
    # 期望输出接近 [[10], [20], [30], [40]]
    assert torch.allclose(corner_output.squeeze(-1), torch.tensor([[10.], [20.], [30.], [40.]]))
    print("✅ 边界情况测试通过!") 
 
 
 