from src.models_v2.encoders import TrajectoryTransformerEncoder, EnvironmentCNNEncoder
from src.models_v2.decoder import TrajectoryTransformerDecoder
from src.models_v2.heads import GMMHead
from src.models_v2.cross_attention import CrossAttentionFusion
from src.models_v2.components import PositionalEncoding, FeatureSampler


EPS = 1e-6

class TrackModelV2(nn.Module):
// ... existing code ... 
 
 
from src.models_v2.decoder import TrajectoryTransformerDecoder
from src.models_v2.heads import GMMHead
from src.models_v2.cross_attention import CrossAttentionFusion
from src.models_v2.components import PositionalEncoding, FeatureSampler


EPS = 1e-6

class TrackModelV2(nn.Module):
// ... existing code ... 
 
 