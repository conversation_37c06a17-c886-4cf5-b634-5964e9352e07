"""
创建时间: 2025-07-29
功能: 加载训练好的V3模型，随机从验证集中抽取样本，进行预测，并将历史轨迹、真实未来轨迹和预测轨迹进行可视化。
输入: 
  - 训练好的V3模型检查点 (`checkpoints/MaskedAutoregressiveModel/best_model.pth`)
  - V3验证数据集 (`data/processed_lmdb_obs_5min_pred_40min_v3_with_mask/val`)
  - 归一化统计文件
  - DEM地形图
输出:
  - 一张包含历史、真实和预测轨迹的可视化图片。
原理及处理方法:
  1. 加载配置、模型和数据。
  2. 随机抽取一个验证样本。
  3. 将模型置于评估模式，并进行前向传播以获得预测轨迹。
  4. 反归一化历史、真实和预测轨迹。
  5. 使用matplotlib和rasterio，将三条轨迹绘制在DEM地形图上。
  6. 对历史轨迹中的真实观测段和空窗期使用不同样式进行区分。
"""
import os
import sys
import torch
import random
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import rasterio
from rasterio.plot import show as rio_show

# 将项目根目录添加到Python路径
sys.path.append(str(Path(__file__).resolve().parents[2]))

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.v3_masked_autoregressive.masked_autoregressive_model import MaskedAutoregressiveModel
from src.utils.normalization import load_normalization_stats, denormalize_trajectory

def main():
    # --- 1. 配置加载 ---
    project_root = Path(__file__).resolve().parents[2]
    default_config_path = project_root / 'configs' / 'default.yaml'
    main_config_path = project_root / 'configs' / 'main_config.yaml'
    data_preprocessing_config_path = project_root / 'configs' / 'data_preprocessing.yaml'
    model_config_path = project_root / 'configs' / 'models' / 'v3_masked_autoregressive_model.yaml'
    config = load_config(
        str(default_config_path),
        str(main_config_path),
        str(data_preprocessing_config_path),
        str(model_config_path)
    )

    # 设置matplotlib字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 'SimHei' 是黑体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # --- 2. 设备设置 ---
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # --- 3. 加载数据和模型 ---
    data_path = Path(config.data.path)
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    
    stats_path = data_path / 'normalization_stats.pkl'
    normalization_stats = load_normalization_stats(str(stats_path))
    
    model = MaskedAutoregressiveModel(config).to(device)
    checkpoint_path = project_root / 'checkpoints' / config.model.name / 'best_model.pth'
    
    if not checkpoint_path.exists():
        print(f"错误: 找不到模型检查点: {checkpoint_path}")
        return
        
    model.load_state_dict(torch.load(checkpoint_path, map_location=device))
    model.eval()
    print(f"从 {checkpoint_path} 加载模型成功。")

    # --- 4. 随机抽样与预测 ---
    with torch.no_grad():
        # 随机选择一个样本
        sample_index = random.randint(0, len(val_dataset) - 1)
        sample = val_dataset[sample_index]
        
        # 将样本数据放到一个批次中，并移动到设备
        history_features = sample['history'].unsqueeze(0).to(device)
        history_mask = sample['history_mask'].unsqueeze(0).to(device)
        environment_roi = sample['environment_roi'].unsqueeze(0).to(device)
        ground_truth_trajectory = sample['ground_truth_trajectory'].unsqueeze(0).to(device)

        # 进行预测 (在eval模式下，模型内部不使用teacher forcing)
        predicted_trajectory_norm = model(history_features, history_mask, environment_roi)

    # --- 5. 反归一化与数据准备 ---
    # 直接从样本中获取未经归一化的原始历史轨迹点
    history_xy = sample['original_history_points'].reshape(-1, 2)
    
    # 反归一化真实未来轨迹
    gt_trajectory_xy = denormalize_trajectory(
        ground_truth_trajectory[0].cpu().numpy(),
        normalization_stats['target_x_mean'], normalization_stats['target_x_std'],
        normalization_stats['target_y_mean'], normalization_stats['target_y_std']
    )

    # 反归一化预测轨迹
    pred_trajectory_xy = denormalize_trajectory(
        predicted_trajectory_norm[0].cpu().numpy(),
        normalization_stats['target_x_mean'], normalization_stats['target_x_std'],
        normalization_stats['target_y_mean'], normalization_stats['target_y_std']
    )

    history_mask_np = history_mask[0].cpu().numpy().flatten()

    # --- 6. 可视化 ---
    dem_path = project_root / 'environment' / 'dem_aligned.tif'
    with rasterio.open(dem_path) as src:
        fig, ax = plt.subplots(figsize=(12, 12))
        
        # 绘制DEM地形图
        rio_show(src, ax=ax, cmap='terrain')
        
        # --- 彻底改造历史轨迹的绘制逻辑 ---
        # 找到真实观测段 (mask=1) 和模拟间歇段 (mask=0)
        observed_indices = np.where(history_mask_np == 1)[0]
        masked_indices = np.where(history_mask_np == 0)[0]

        # 绘制真实观测段为蓝色实线
        ax.plot(history_xy[observed_indices, 0], history_xy[observed_indices, 1], 'o-', label='历史观测轨迹', color='blue', markersize=3, linewidth=1.5, zorder=3)
        
        # 绘制模拟间歇段为灰色虚线
        # 为了视觉效果，我们只绘制点，不连接成线
        if len(masked_indices) > 0:
            ax.plot(history_xy[masked_indices, 0], history_xy[masked_indices, 1], 'o', label='观测间歇(模拟)', color='grey', markersize=2, alpha=0.5, zorder=2)

        # 绘制历史轨迹的起点
        ax.plot(history_xy[0, 0], history_xy[0, 1], 'o', color='cyan', markersize=10, label='历史起点', zorder=4)

        # 绘制真实未来轨迹
        ax.plot(gt_trajectory_xy[:, 0], gt_trajectory_xy[:, 1], 'o-', label='真实未来轨迹', color='green', markersize=3, zorder=3)

        # 绘制预测轨迹
        ax.plot(pred_trajectory_xy[:, 0], pred_trajectory_xy[:, 1], 'o--', label='模型预测轨迹', color='red', markersize=3, zorder=3)
        
        # 设置图表
        ax.set_title('V3模型预测结果可视化 (含观测间歇)', fontsize=22)
        ax.set_xlabel('X坐标 (米)', fontsize=20)
        ax.set_ylabel('Y坐标 (米)', fontsize=20)
        ax.tick_params(axis='both', which='major', labelsize=18)
        ax.legend(fontsize=16)
        ax.grid(True, linestyle='--', alpha=0.6)
        ax.set_aspect('equal', adjustable='box')
        
        plt.tight_layout()
        output_path = project_root / 'inference_results' / 'v3_prediction_visualization.png'
        output_path.parent.mkdir(exist_ok=True)
        plt.savefig(output_path, dpi=300)
        plt.show()
        print(f"可视化结果已保存到: {output_path}")

if __name__ == '__main__':
    main() 