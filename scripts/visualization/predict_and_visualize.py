# -*- coding: utf-8 -*-
"""
创建时间: 2025/07/19
功能: 本脚本用于加载已训练的轨迹预测模型，对LMDB数据集中的单个样本进行轨迹预测，并将原始观测轨迹、真实未来轨迹、模型预测的未来轨迹以及预测目的地叠加在背景DEM地形图上进行可视化，以直观地评估模型性能和理解预测结果。
输入:
  - `main_config_path`: 字符串，主配置文件的YAML文件路径（例如：`configs/main_config.yaml`）。此文件包含了通用配置和指向模型特定配置的路径。
  - `sample_idx`: 整数，可选参数，指定要从LMDB中加载的样本索引。如果为 `None`，脚本将随机选择一个样本进行可视化。
  - LMDB验证数据集：通过配置文件中 `data.val_lmdb_path` 指定，包含预处理后的轨迹和环境数据。
  - 归一化统计文件：通过配置文件中 `data.stats_path` 指定，用于轨迹数据的归一化和反归一化。
  - 模型权重文件：通过配置文件中 `model.checkpoint_path` 指定，加载预训练的模型参数。
  - DEM地形数据文件：通过配置文件中 `data_preprocessing.env_maps.dem` 指定，作为可视化背景。
输出:
  - 控制台输出加载进度、模型预测结果的数值范围（例如：预测轨迹的X/Y坐标范围）。
  - 生成一张PNG格式的可视化图像，保存到 `outputs/inference_results/` 目录下，文件名包含样本索引。
原理:
  - **配置和设备设置:** 脚本会加载并合并主配置文件和模型特定配置文件，并根据可用性自动选择使用GPU或CPU作为计算设备。
  - **归一化处理:** 加载预计算的归一化统计数据。历史轨迹在输入模型前会进行归一化，而模型的预测输出（归一化轨迹和目的地）在可视化前会反归一化到实际的地理坐标系。
  - **模型加载与推理:** 实例化 `src.models.end_to_end_model.EndToEndModel` 模型，并加载指定路径的预训练权重。将模型设置为评估模式 (`model.eval()`) 后，将归一化后的历史轨迹和环境ROI输入模型进行前向传播，获取预测的未来轨迹和目的地分布参数。
  - **数据转换与范围检查:** 将模型的归一化输出（预测轨迹和目的地）使用加载的归一化统计数据反归一化为绝对坐标。同时，打印观测轨迹、真实未来轨迹、预测未来轨迹和预测目的地的坐标范围，便于调试和验证。
  - **可视化:** 使用 `matplotlib.pyplot` 进行绘图。背景图通过 `rasterio` 加载 `data/environment` 目录下的GeoTIFF地形数据，并使用 `cmap='terrain'` 进行配色。观测轨迹、真实未来轨迹、预测未来轨迹和预测目的地分别用不同颜色和标记绘制在同一图上。图表标题和轴标签的字体大小统一为22号和20号，图例字体大小也设置为20号，并支持中文显示。
处理方法:
  - 脚本通过动态修改 `sys.path` 来确保可以正确导入项目内部的模块。
  - `predict_and_visualize` 函数被设计为直接接收一个合并后的配置对象，使其更加模块化和可配置。
  - 在加载模型、数据以及绘图过程中，包含异常处理，以提高脚本的健壮性。
  - 在绘制图片之前，会检查并创建输出目录。
  - 使用 `plt.close()` 确保绘图资源在图片保存后被正确释放。
"""
import sys
import os
import torch
import numpy as np
import pickle
import random
import matplotlib.pyplot as plt
import rasterio
import matplotlib
import argparse
import torch.nn.functional as F
import importlib # 导入importlib用于动态加载模块
from pathlib import Path # 导入Path用于路径操作
from matplotlib.patches import Ellipse # 导入Ellipse用于绘制椭圆
from scipy.stats import chi2 # 导入chi2用于计算置信区间

# 将项目根目录添加到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# from src.models.end_to_end_model import EndToEndModel # 移除硬编码导入
from src.utils.normalization import load_normalization_stats, denormalize_trajectory
from src.utils.config_loader import load_config, update_config
from src.data.datasets import LMDBDataset # 需要导入LMDBDataset以便获取样本
from src.models.v1_grid_classifier.end_to_end_model import GridClassifierV1 # V1模型
from src.models.end_to_end_model import EndToEndModel # EndToEnd通用模型
from src.models.pecnet_model.pecnet_model import PECNet # PECNet模型
from src.models.mdn_predictor_v5 import MDN_Predictor_V5 # MDN_Predictor_V5模型
from src.models.v2_intent_guided.intent_guided_model import IntentGuidedModel # IntentGuidedModel模型
from src.models.cnn_rnn_fusion_model import CNN_RNN_Fusion_Model # CNN_RNN_Fusion_Model模型

def get_model_class(model_name: str):
    """
    根据模型名称动态加载模型类。
    """
    if model_name == "EndToEndModel": # 假设config.model.name是EndToEndModel
        module_name = "src.models.end_to_end_model"
        class_name = "EndToEndModel"
    elif model_name == "v1_grid_classifier_model": # 假设config.model.name是v1_grid_classifier_model
        module_name = "src.models.v1_grid_classifier.end_to_end_model"
        class_name = "GridClassifierV1"
    elif model_name == "pecnet_model": # 新增 PECNet 模型
        module_name = "src.models.pecnet_model.pecnet_model"
        class_name = "PECNet"
    elif model_name == "v4_mdn_model": # 新增 MDN_Predictor_V5 模型
        module_name = "src.models.mdn_predictor_v5"
        class_name = "MDN_Predictor_V5"
    elif model_name == "v2_intent_guided_model": # 新增 IntentGuidedModel 模型
        module_name = "src.models.v2_intent_guided.intent_guided_model"
        class_name = "IntentGuidedModel"
    elif model_name == "cnn_rnn_fusion_model": # 新增 CNN_RNN_Fusion_Model 模型
        module_name = "src.models.cnn_rnn_fusion_model"
        class_name = "CNN_RNN_Fusion_Model"
    elif model_name == "end_to_end_model": # 新增 EndToEndModel 模型
        module_name = "src.models.end_to_end_model"
        class_name = "EndToEndModel"
    # TODO: 添加其他模型的映射，例如 cnn_rnn_fusion
    # elif model_name == "cnn_rnn_fusion":
    #    module_name = "src.models.cnn_rnn_fusion_model" # 假设的模块路径
    #    class_name = "CNNRNNFusionModel" # 假设的类名
    else:
        raise ValueError(f"未知模型名称: {model_name}")

    module = importlib.import_module(module_name)
    model_class = getattr(module, class_name)
    return model_class

def predict_and_visualize(config, sample_idx: int = None, checkpoint_path: str = None): # 直接接收合并后的config对象
    """
    主函数：加载模型，预测并可视化
    Args:
        config (omegaconf.dictconfig.DictConfig): 合并后的配置对象。
        sample_idx (int, optional): 要可视化的样本索引。默认为None，随机选择。
        checkpoint_path (str, optional): 模型检查点文件路径。默认为None，将从config中获取。
    """
    print("配置加载成功。")

    # 2. 设置设备
    device = torch.device(config.device if hasattr(config, 'device') else ('cuda' if torch.cuda.is_available() else 'cpu'))
    print(f"使用设备: {device}")

    # 3. 加载归一化统计数据
    normalization_stats_path = Path(config.data_preprocessing.output_path) / 'normalization_stats.pkl' # 使用Path对象
    if not normalization_stats_path.exists():
        raise FileNotFoundError(f"错误: 归一化统计文件未找到: {normalization_stats_path}")

    normalization_stats = load_normalization_stats(str(normalization_stats_path)) # load_normalization_stats 期望字符串路径

    # --- 关键修复：使用新的嵌套结构访问统计数据 ---
    stats = normalization_stats # 为简洁起见，创建一个短别名
    mean_x = stats['target']['x']['mean']
    std_x = stats['target']['x']['std']
    mean_y = stats['target']['y']['mean']
    std_y = stats['target']['y']['std']

    print("归一化统计数据加载成功。")

    # 4. 加载模型
    model_load_path = checkpoint_path # 使用传入的checkpoint_path
    if not model_load_path:
        # 如果没有通过参数传入，则尝试从config中获取默认路径
        # 由于我们总是通过命令行传入checkpoint，此处逻辑可以简化，或者确保 config.model 中有默认值
        # 暂不修改，保持其健壮性
        raise ValueError("未提供模型检查点路径。") # 直接报错，因为checkpoint是required参数

    # 动态加载模型类
    # 从 args.model_config_name 中获取模型名称，不依赖 config.model.name
    model_name_from_arg = Path(args.model_config_name).stem # 使用args.model_config_name的stem
    
    # --- 新增: 动态加载V3模型 ---
    if model_name_from_arg == 'v3_masked_autoregressive':
        module = importlib.import_module("src.models.v3_masked_autoregressive.model")
        ModelClass = getattr(module, "MaskedAutoregressiveModel")
    else:
    ModelClass = get_model_class(model_name_from_arg)
    
    # 检查是否是V1模型，V1模型需要额外的dest_mean和dest_std参数
    if ModelClass == GridClassifierV1:
        dest_mean_for_model = torch.tensor([mean_x, mean_y], dtype=torch.float32).to(device)
        dest_std_for_model = torch.tensor([std_x, std_y], dtype=torch.float32).to(device)
        model = ModelClass(config, dest_mean_for_model, dest_std_for_model).to(device)
    elif ModelClass == PECNet: # 为PECNet添加独立的初始化逻辑
        # 从 config 中提取 PECNet 模型初始化所需的参数
        history_feature_dim = len(config.data_preprocessing.history_features)
        endpoint_dim = config.model.endpoint_dim
        output_len = config.model.output_len
        hidden_dim = config.model.hidden_dim
        dropout_prob = config.model.dropout_prob if hasattr(config.model, 'dropout_prob') else 0.3
        num_layers = config.model.num_layers if hasattr(config.model, 'num_layers') else 1
        env_feature_dim = config.model.get('env_feature_dim', 13) # 从配置获取，或默认13
        
        model = ModelClass(
            history_feature_dim=history_feature_dim,
            endpoint_dim=endpoint_dim,
            env_feature_dim=env_feature_dim,
            output_len=output_len,
            hidden_dim=hidden_dim,
            dropout_prob=dropout_prob,
            num_layers=num_layers
        ).to(device)
    elif ModelClass == MDN_Predictor_V5: # 新增 MDN_Predictor_V5 模型
        history_feature_dim = config.model.trajectory_encoder.input_dim
        rnn_hidden_size = config.model.trajectory_encoder.d_model
        num_layers_mdn = config.model.trajectory_encoder.num_layers # MDN_Predictor_V5 使用 num_rnn_layers
        num_mixture = config.model.num_gmm_components
        goal_embedding_dim = config.model.relative_pos_encoder.hidden_dim # 对应 RelativeGoalEncoderMLP 的 embedding_dim
        dropout_prob_mdn = config.model.trajectory_encoder.dropout # 使用 trajectory_encoder 的 dropout
        
        model = ModelClass(
            history_feature_dim=history_feature_dim,
            rnn_hidden_size=rnn_hidden_size,
            num_layers=num_layers_mdn,
            num_mixture=num_mixture,
            goal_embedding_dim=goal_embedding_dim,
            dropout_prob=dropout_prob_mdn,
            normalization_stats=normalization_stats # 需要传递 normalization_stats
        ).to(device)
    elif ModelClass == IntentGuidedModel: # 新增 IntentGuidedModel 模型
        # IntentGuidedModel 需要 dest_mean 和 dest_std
        dest_mean_for_model = torch.tensor([mean_x, mean_y], dtype=torch.float32).to(device)
        dest_std_for_model = torch.tensor([std_x, std_y], dtype=torch.float32).to(device)
        model = ModelClass(config, dest_mean=dest_mean_for_model, dest_std=dest_std_for_model).to(device)
    elif ModelClass == CNN_RNN_Fusion_Model: # 新增 CNN_RNN_Fusion_Model 模型
        history_feature_dim = len(config.data_preprocessing.history_features)
        # environment_roi_data 的通道数 C_env
        env_roi_channels = len(config.data_preprocessing.env_maps) + len(config.data_preprocessing.landcover_classes) # DEM, Slope, Aspect_sin, Aspect_cos + One-Hot Land Cover
        env_roi_size = config.data_preprocessing.env_roi_size_pixels
        rnn_hidden_size = config.model.trajectory_encoder.d_model # 假设在 config.model 中有 rnn_hidden_size
        pred_len_agg = int(config.data_preprocessing.prediction_horizon_s / config.data_preprocessing.agg_interval_s)

        model = ModelClass(
            history_feature_dim=history_feature_dim,
            env_roi_channels=env_roi_channels,
            env_roi_size=env_roi_size,
            rnn_hidden_size=rnn_hidden_size,
            pred_len_agg=pred_len_agg
        ).to(device)
    elif ModelClass == EndToEndModel: # 新增 EndToEndModel 模型
        # EndToEndModel 的 __init__ 期望 config 中包含子模块的配置字典
        # 例如：config['trajectory_encoder'] 而不是 config.model.trajectory_encoder
        # 我们可以构建一个临时的 config 字典，只包含 model 下面的子配置
        end_to_end_model_config = {
            "trajectory_encoder": config.model.trajectory_encoder,
            "environment_encoder": config.model.environment_encoder,
            "fusion_layer": config.model.fusion_layer,
            "destination_predictor": config.model.destination_predictor,
            "trajectory_generator": config.model.trajectory_generator,
        }
        model = ModelClass(end_to_end_model_config).to(device)
    else: # 其他通用模型，如EndToEndModel
        model = ModelClass(config).to(device) # EndToEndModel 的初始化接收整个 config

    checkpoint = torch.load(model_load_path, map_location=device)
    # 过滤掉 state_dict 中不需要的键 (例如 dest_mean, dest_std)
    model_state_dict = model.state_dict()
    pretrained_state_dict = {k: v for k, v in checkpoint.items() if k in model_state_dict}
    model.load_state_dict(pretrained_state_dict, strict=False) # 使用 strict=False 忽略不匹配的键
    
    model.eval()
    print(f"模型已从 {model_load_path} 加载。")
    
    # 5. 加载单个数据样本
    import lmdb
    lmdb_path_val = Path(config.data_preprocessing.output_path) / 'val' # 使用Path对象
    env = lmdb.open(str(lmdb_path_val), readonly=True, lock=False, readahead=False, meminit=False) # 转换回字符串
    with env.begin(write=False) as txn:
        # 使用txn.cursor()更稳健地获取所有键
        all_keys = [key.decode('utf-8') for key, _ in txn.cursor()]
        num_total_samples = len(all_keys) # 更改变量名，避免与num_samples参数混淆

        if num_total_samples == 0:
            print(f"错误: LMDB数据库 {lmdb_path_val} 中没有样本。请检查数据预处理。")
            env.close()
            return
        
        if sample_idx is None or sample_idx == -1: # 如果为None或-1，则随机选择
            sample_idx = random.randint(0, num_total_samples - 1)

        if sample_idx >= len(all_keys):
            print(f"错误: 样本索引 {sample_idx} 超出范围。LMDB中只有 {len(all_keys)} 个样本。")
            env.close()
            return

        key = all_keys[sample_idx].encode('utf-8') # Re-encode for txn.get()
        value = txn.get(key)
        sample = pickle.loads(value)
    env.close()
    print(f"已从LMDB加载样本 {sample_idx} (Key: {key.decode()})。")

    # 6. 准备模型输入
    history_data = torch.from_numpy(sample['history_features']).float().unsqueeze(0).to(device) # 键名改为 'history_features'
    environment_roi_data = torch.from_numpy(sample['environment_roi']).float().unsqueeze(0).to(device)
    # 获取ground truth以传递给模型forward方法，即使在推理时也可能需要
    ground_truth_destination_normalized = torch.from_numpy(sample['ground_truth_destination']).float().unsqueeze(0).to(device) # 重命名变量
    ground_truth_trajectory_normalized = torch.from_numpy(sample['ground_truth_trajectory']).float().unsqueeze(0).to(device) # 重命名变量

    # 提前进行数据反归一化和坐标转换，确保在所有模型分支中都可用
    # 原始观测轨迹是从LMDB样本中直接获取的，通常是聚合后的，这里假设是绝对坐标
    # original_history_points 包含原始（非聚合）的历史点，用于更平滑的轨迹显示
    obs_traj_abs = sample['original_history_points'].reshape(-1, 2)

    # 真实未来轨迹是LMDB样本中预先归一化过的，需要反归一化
    gt_future_traj_abs = denormalize_trajectory(sample['ground_truth_trajectory'], mean_x, std_x, mean_y, std_y)

    # 真实目的地是LMDB样本中预先归一化过的，需要反归一化
    gt_dest_abs = denormalize_trajectory(sample['ground_truth_destination'], mean_x, std_x, mean_y, std_y).squeeze()

    # 临时处理 environment_roi 维度以适配模型接口：对每个历史时间步的ROI进行平均池化，然后展平
    # environment_roi_data 原始形状 (batch_size, obs_len, C_env, H, W)
    # 我们需要将其聚合为 (batch_size, C_env)
    environment_features_pooled_hw = environment_roi_data.mean(dim=(-1, -2))
    environment_features_aggregated = environment_features_pooled_hw.mean(dim=1)

    # 7. 模型预测
    predicted_endpoint_means_sampled = None # 新增：用于存储所有采样的目的地均值
    predicted_endpoint_logvars_sampled = None # 新增：用于存储所有采样的目的地logvar

    with torch.no_grad():
        predicted_trajectories_sampled = None
        predicted_endpoints_sampled = None

        # --- 新增: V3模型的预测逻辑 ---
        if model_name_from_arg == 'v3_masked_autoregressive':
            # V3模型实例化
            model = ModelClass(config.model).to(device)
            model.load_state_dict(torch.load(model_load_path, map_location=device))
            model.eval()
            
            # 准备V3模型输入 (注意：V3模型可能需要history_mask)
            history_mask = torch.from_numpy(sample['history_mask']).float().unsqueeze(0).to(device)
            
            # 调用V3模型的predict方法
            predictions = model.predict(history_data, history_mask, environment_roi_data)
            
            # 提取并反归一化预测轨迹和目的地
            pred_traj_normalized = predictions['predicted_trajectory'].squeeze(0).cpu().numpy()
            pred_dest_normalized = predictions['predicted_destination'].squeeze(0).cpu().numpy()
            
            pred_traj_abs = denormalize_trajectory(pred_traj_normalized, mean_x, std_x, mean_y, std_y)
            pred_dest_abs = denormalize_trajectory(pred_dest_normalized, mean_x, std_x, mean_y, std_y).squeeze()

        # 检查模型类型以调用正确的 forward 方法
        if ModelClass == GridClassifierV1:
            # GridClassifierV1 的 forward 方法需要所有四个输入参数
            model_output = model(history_data, environment_roi_data, ground_truth_destination_normalized, ground_truth_trajectory_normalized)
            # 对于 GridClassifierV1，predicted_goal_coords 是一个辅助输出，直接使用它作为预测目的地
            pred_dest_abs = model_output['predicted_goal_coords'].squeeze(0).cpu().numpy()
            # predicted_trajectory 由 get_best_mode_trajectory 方法生成，需要额外调用
            pred_traj_abs = model.get_best_mode_trajectory(model_output['gmm_params_flat']).squeeze(0).cpu().numpy()

        elif ModelClass == PECNet: # 为PECNet添加独立的预测逻辑
            # PECNet forward 方法的参数: history_data, environment_features, ground_truth_destination, ground_truth_trajectory, is_train=True, num_samples=1
            # 在可视化时，is_train=False，并传入 num_samples 参数
            num_prediction_samples = args.num_samples # 从命令行参数获取num_samples
            model_output = model(history_data,
                                 environment_features=environment_features_aggregated, # 传入聚合后的环境特征
                                 is_train=True, # 修改: 训练模式，以获得多样化采样
                                 num_samples=num_prediction_samples) # 从命令行参数获取

            # PECNet 返回的 predicted_trajectories 是 (num_samples, batch_size, output_len, 2)
            # 选取第一个样本进行可视化，或者根据需要选取最佳样本
            predicted_trajectories_sampled = model_output['predicted_trajectories_sampled'][:, 0, :, :].cpu().numpy() # 针对单个样本进行可视化
            predicted_endpoints_sampled = model_output['predicted_endpoints'][:, 0, :].cpu().numpy() # 针对单个样本进行可视化
            predicted_endpoint_means_sampled = model_output['predicted_endpoint_mean'].squeeze(0).cpu().numpy() # (batch_size, 2) -> (2,)
            predicted_endpoint_logvars_sampled = model_output['predicted_endpoint_logvar'].squeeze(0).cpu().numpy() # (batch_size, 2) -> (2,)

            # 获取历史轨迹的最后一个绝对坐标点
            last_obs_point = obs_traj_abs[-1] # obs_traj_abs 是绝对坐标

            # 反归一化预测轨迹 (这时还是相对于全局原点)
            denormalized_predicted_trajectories_sampled = np.array([denormalize_trajectory(t, mean_x, std_x, mean_y, std_y) for t in predicted_trajectories_sampled])
            
            # 计算每个预测轨迹的起始点（反归一化后的绝对坐标）
            # predicted_trajectories_sampled 的形状是 (num_samples, output_len, 2)
            first_predicted_point_abs = denormalized_predicted_trajectories_sampled[:, 0, :]

            # 计算每个采样轨迹的偏移量
            # 形状: (num_samples, 2)
            offset = last_obs_point - first_predicted_point_abs 
            
            # 将偏移量广播到所有采样轨迹上，使其起始点与历史轨迹末端对齐
            # 扩展 offset 的维度以匹配 denormalized_predicted_trajectories_sampled (num_samples, output_len, 2)
            # 形状: (num_samples, 1, 2)
            pred_traj_abs = denormalized_predicted_trajectories_sampled + offset[:, np.newaxis, :] # 修正：这里直接赋值给 pred_traj_abs

            # 同时调整采样的预测目的地，使其与对应的轨迹保持一致的偏移
            denormalized_predicted_endpoints_sampled = np.array([denormalize_trajectory(e, mean_x, std_x, mean_y, std_y) for e in predicted_endpoints_sampled])
            pred_dest_abs_sampled = denormalized_predicted_endpoints_sampled + offset # (num_samples, 2)

            # 调整预测目的地均值
            pred_dest_mean_abs = denormalize_trajectory(predicted_endpoint_means_sampled, mean_x, std_x, mean_y, std_y).squeeze() # 预测目的地均值 (绝对坐标)
            # 预测目的地均值也需要应用同样的偏移量
            # 由于 pred_dest_mean_abs 是一个点，我们用批次中第一个轨迹的偏移量来修正它
            pred_dest_mean_abs_offsetted = pred_dest_mean_abs + offset[0] # 使用第一个采样的偏移量

            # 为了绘图，我们仍然使用 pred_traj_abs 和 pred_dest_mean_abs_offsetted
            # 这里 pred_traj_abs 现在已经是包含所有偏移校正后的采样轨迹的 NumPy 数组

        elif ModelClass == MDN_Predictor_V5: # 新增 MDN_Predictor_V5 模型
            # MDN_Predictor_V5 的 forward 方法签名是:
            # forward(self, history_data, ground_truth_trajectory, ground_truth_destination, teacher_forcing_ratio=0.5)
            # 不使用 environment_roi_data
            model_output = model(history_data,
                                 ground_truth_trajectory_normalized, # 真实未来轨迹
                                 ground_truth_destination_normalized, # 真实目的地
                                 teacher_forcing_ratio=0.0) # 推理时设置为0
            
            # MDN_Predictor_V5 返回 mdn_params_sequence, destination_mdn_params
            mdn_params_sequence = model_output[0] # 轨迹MDN参数 (B, T_pred_agg, 6*num_mixture)
            destination_mdn_params = model_output[1] # 目的地MDN参数 (B, 6*num_mixture)

            # 从 MDN_Predictor_V5 的 mdn_params_sequence 中提取预测轨迹
            pi_traj, mu_x_traj, mu_y_traj, sigma_x_traj, sigma_y_traj, rho_traj = model.get_mixture_params(mdn_params_sequence)
            # 选择概率最高的混合成分的均值
            _, best_k_traj = torch.max(pi_traj, dim=-1) # (B, T_pred_agg)
            
            # 展平以便索引
            batch_size, pred_steps, _ = mdn_params_sequence.shape
            best_k_traj_flat = best_k_traj.view(-1) # (B * T_pred_agg)
            mu_x_traj_flat = mu_x_traj.view(-1, model.num_mixture) # (B * T_pred_agg, num_mixture)
            mu_y_traj_flat = mu_y_traj.view(-1, model.num_mixture) # (B * T_pred_agg, num_mixture)

            predicted_offsets_normalized_flat = torch.stack([
                mu_x_traj_flat[torch.arange(batch_size * pred_steps), best_k_traj_flat],
                mu_y_traj_flat[torch.arange(batch_size * pred_steps), best_k_traj_flat]
            ], dim=1) # (B * T_pred_agg, 2)

            predicted_offsets_normalized = predicted_offsets_normalized_flat.view(batch_size, pred_steps, 2) # (B, T_pred_agg, 2)

            # 从历史轨迹最后一个点开始累加偏移量
            current_pos_normalized = history_data[:, -1, :2] # (B, 2)
            predicted_trajectory_normalized = []
            for t in range(pred_steps):
                current_pos_normalized = current_pos_normalized + predicted_offsets_normalized[:, t, :]
                predicted_trajectory_normalized.append(current_pos_normalized.clone()) # clone() to avoid in-place modification issues
            predicted_trajectory_normalized = torch.stack(predicted_trajectory_normalized, dim=1) # (B, T_pred_agg, 2)

            pred_traj_abs = denormalize_trajectory(predicted_trajectory_normalized.squeeze(0).cpu().numpy(), mean_x, std_x, mean_y, std_y)

            # 从 destination_mdn_params 中提取预测目的地
            pi_dest, mu_x_dest, mu_y_dest, sigma_x_dest, sigma_y_dest, rho_dest = model.get_mixture_params(destination_mdn_params, is_destination_mdn=True)
            _, best_k_dest = torch.max(pi_dest, dim=1)
            
            pred_dest_normalized_tensor = torch.stack([
                mu_x_dest[torch.arange(batch_size), best_k_dest],
                mu_y_dest[torch.arange(batch_size), best_k_dest]
            ], dim=1) # (B, 2)
            
            pred_dest_abs = denormalize_trajectory(pred_dest_normalized_tensor.squeeze(0).cpu().numpy(), mean_x, std_x, mean_y, std_y).squeeze()

            # 为了 PECNet 绘制椭圆所需参数，这里也计算并设置
            predicted_endpoint_means_sampled = pred_dest_normalized_tensor.squeeze(0).cpu().numpy() # 目的地均值 (归一化)
            
            # 从目的地MDN参数中获取方差，用于绘制椭圆 (针对最佳混合成分)
            best_k_dest_val = best_k_dest[0].item() # 假设 batch_size = 1
            log_sigma_x_best_dest = torch.log(sigma_x_dest[0, best_k_dest_val]).item() # 转换为 logvar
            log_sigma_y_best_dest = torch.log(sigma_y_dest[0, best_k_dest_val]).item()
            # 考虑到rho，更准确的协方差矩阵
            sigma_x_val = sigma_x_dest[0, best_k_dest_val].item()
            sigma_y_val = sigma_y_dest[0, best_k_dest_val].item()
            rho_val = rho_dest[0, best_k_dest_val].item()
            
            # 转换为 NumPy 数组
            predicted_endpoint_logvars_sampled = np.array([log_sigma_x_best_dest, log_sigma_y_best_dest])

        elif ModelClass == IntentGuidedModel: # 修改 IntentGuidedModel 预测和反归一化逻辑
            model_output = model(history_data, environment_roi_data, ground_truth_destination_normalized, ground_truth_trajectory_normalized)
            gmm_params_flat = model_output['gmm_params_flat']
            
            # 模型现在返回归一化后的轨迹
            pred_traj_normalized = model.get_best_mode_trajectory(gmm_params_flat).squeeze(0).cpu()
            # 同样，目的地也是归一化的
            pred_dest_normalized = model_output['predicted_goal_normalized'].squeeze(0).cpu()
            
            # 在脚本中进行反归一化
            pred_traj_abs = denormalize_trajectory(pred_traj_normalized.numpy(), mean_x, std_x, mean_y, std_y)
            pred_dest_abs = denormalize_trajectory(pred_dest_normalized.numpy(), mean_x, std_x, mean_y, std_y).squeeze()
            
            # 关键修正：检查轨迹是否是偏移量。如果是，则需要将其起点对齐到历史轨迹的终点
            # 假设模型输出的是相对于历史轨迹最后一个点的偏移量累加形成的轨迹
            # 清洗 obs_traj_abs，确保其为 float32 类型的 numpy 数组
            obs_traj_abs = np.vstack(obs_traj_abs).astype(np.float32)
            last_obs_point_abs = obs_traj_abs[-1] # 历史轨迹最后一个点的绝对坐标
            first_pred_point_abs = pred_traj_abs[0] # 预测轨迹第一个点的绝对坐标
            
            # 计算偏移量，将预测轨迹的起点平移到历史轨迹的终点
            offset = last_obs_point_abs - first_pred_point_abs
            pred_traj_abs += offset
            pred_dest_abs += offset # 目的地也需要同样的平移

        elif ModelClass == CNN_RNN_Fusion_Model: # 新增 CNN_RNN_Fusion_Model 预测逻辑
            # CNN_RNN_Fusion_Model 的 forward 方法签名是: forward(self, history_data, environment_roi_data)
            # 它返回 predicted_trajectory, predicted_destination
            predicted_trajectory_normalized, predicted_destination_normalized = model(history_data, environment_roi_data)
            
            # 反归一化预测轨迹和目的地
            pred_traj_abs = denormalize_trajectory(predicted_trajectory_normalized.squeeze(0).cpu().numpy(), mean_x, std_x, mean_y, std_y)
            pred_dest_abs = denormalize_trajectory(predicted_destination_normalized.squeeze(0).cpu().numpy(), mean_x, std_x, mean_y, std_y).squeeze()

        else: # 对于其他通用模型，如EndToEndModel
            # EndToEndModel 的 forward 方法期望 ground_truth_trajectory 为绝对坐标
            model_output = model(history_data, environment_roi_data, ground_truth_trajectory=ground_truth_trajectory_normalized)
            pred_traj_abs = model_output['predicted_trajectory'].squeeze(0).cpu().numpy()
            destination_params = model_output['destination_params'].squeeze(0).cpu()

            # 从destination_params中提取预测目的地（最可能的均值），并反归一化
            num_components = config.model.destination_predictor.num_components
            output_dimension = config.model.destination_predictor.output_dim
            params_per_component = 2 * output_dimension + 2 # mu_x, mu_y, sigma_x, sigma_y, rho_xy, pi
            params_reshaped = destination_params.view(num_components, params_per_component)
            
            mixture_weights_logits = params_reshaped[:, -1] # EndToEndModel 的 pi 是最后一个参数
            mixture_weights = F.softmax(mixture_weights_logits, dim=-1)
            best_component_idx = torch.argmax(mixture_weights)
            
            mu_start_idx = 0 # EndToEndModel 的 mu 是前两个参数
            pred_dest_normalized_tensor = params_reshaped[best_component_idx, mu_start_idx : mu_start_idx + output_dimension] # (D,)
            pred_dest_abs = denormalize_trajectory(pred_dest_normalized_tensor.numpy(), mean_x, std_x, mean_y, std_y).squeeze() # 反归一化目的地
    
    print("模型预测完成。")

    # 8. 数据反归一化和坐标转换 (提前到这里，确保所有模型分支都能使用)
    # 原始观测轨迹是从LMDB样本中直接获取的，通常是聚合后的，这里假设是绝对坐标
    # original_history_points 包含原始（非聚合）的历史点，用于更平滑的轨迹显示
    # obs_traj_abs = sample['original_history_points'].reshape(-1, 2) # 移至模型预测前

    # 真实未来轨迹是LMDB样本中预先归一化过的，需要反归一化
    # gt_future_traj_abs = denormalize_trajectory(sample['ground_truth_trajectory'], mean_x, std_x, mean_y, std_y) # 移至模型预测前

    # 真实目的地是LMDB样本中预先归一化过的，需要反归一化
    # gt_dest_abs = denormalize_trajectory(sample['ground_truth_destination'], mean_x, std_x, mean_y, std_y).squeeze() # 移至模型预测前

    # 预测轨迹已由模型输出为绝对坐标 (pred_traj_abs)
    
    # Debug: Print coordinate ranges
    print("\n--- 轨迹坐标范围 (绝对值) ---")
    print(f"观测轨迹 (obs_traj_abs) 形状: {obs_traj_abs.shape}, X范围: [{np.min(obs_traj_abs[:, 0]):.2f}, {np.max(obs_traj_abs[:, 0]):.2f}], Y范围: [{np.min(obs_traj_abs[:, 1]):.2f}, {np.max(obs_traj_abs[:, 1]):.2f}]")
    print(f"真实未来轨迹 (gt_future_traj_abs) 形状: {gt_future_traj_abs.shape}, X范围: [{np.min(gt_future_traj_abs[:, 0]):.2f}, {np.max(gt_future_traj_abs[:, 0]):.2f}], Y范围: [{np.min(gt_future_traj_abs[:, 1]):.2f}, {np.max(gt_future_traj_abs[:, 1]):.2f}]")

    # For PECNet, print ranges of sampled trajectories and sampled endpoints
    if ModelClass == PECNet: # Explicitly check for PECNet
        for i, traj in enumerate(predicted_trajectories_sampled):
            denorm_traj = denormalize_trajectory(traj, mean_x, std_x, mean_y, std_y)
            print(f"预测未来轨迹 (采样 {i+1}) 形状: {denorm_traj.shape}, X范围: [{np.min(denorm_traj[:, 0]):.2f}, {np.max(denorm_traj[:, 0]):.2f}], Y范围: [{np.min(denorm_traj[:, 1]):.2f}, {np.max(denorm_traj[:, 1]):.2f}]")
        
        for i, dest in enumerate(predicted_endpoints_sampled):
            denorm_dest = denormalize_trajectory(dest, mean_x, std_x, mean_y, std_y).squeeze()
            print(f"预测目的地 (采样 {i+1}) 形状: {denorm_dest.shape}, X范围: [{denorm_dest[0]:.2f}], Y范围: [{denorm_dest[1]:.2f}]")
        
        pred_dest_mean_abs = denormalize_trajectory(predicted_endpoint_means_sampled, mean_x, std_x, mean_y, std_y).squeeze()
        print(f"预测目的地均值 (pred_dest_mean_abs) 形状: {pred_dest_mean_abs.shape}, X范围: [{pred_dest_mean_abs[0]:.2f}], Y范围: [{pred_dest_mean_abs[1]:.2f}]")
    else: # For other models
        print(f"预测未来轨迹 (pred_traj_abs) 形状: {pred_traj_abs.shape}, X范围: [{np.min(pred_traj_abs[:, 0]):.2f}, {np.max(pred_traj_abs[:, 0]):.2f}], Y范围: [{np.min(pred_traj_abs[:, 1]):.2f}, {np.max(pred_traj_abs[:, 1]):.2f}]")
        print(f"预测目的地 (pred_dest_abs) 形状: {pred_dest_abs.shape}, X范围: [{pred_dest_abs[0]:.2f}], Y范围: [{pred_dest_abs[1]:.2f}]")

    print(f"真实目的地 (gt_dest_abs) 形状: {gt_dest_abs.shape}, X范围: [{gt_dest_abs[0]:.2f}], Y范围: [{gt_dest_abs[1]:.2f}]")
    print("----------------------------")

    # 9. 可视化
    print("开始绘图...")
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 20

    # 绘制背景DEM地形图
    dem_path = Path(config.data_preprocessing.environment_path) / config.data_preprocessing.env_maps.dem
    try:
        with rasterio.open(str(dem_path)) as src:
            dem_data = src.read(1)
            dem_transform = src.transform
            dem_crs = src.crs

            bounds = src.bounds
            window_data = src.read(1)

            ax.imshow(window_data, cmap='terrain', extent=[bounds.left, bounds.right, bounds.bottom, bounds.top], origin='upper')
            ax.set_aspect('equal', adjustable='box')

    except Exception as e:
        print(f"加载或绘制DEM背景图失败: {e}")
        pass

    # 绘制轨迹
    ax.plot(obs_traj_abs[:, 0], obs_traj_abs[:, 1], 'b-o', label='观测轨迹', markersize=2, linewidth=2)
    ax.plot(gt_future_traj_abs[:, 0], gt_future_traj_abs[:, 1], 'g-o', label='真实未来轨迹', markersize=2, linewidth=2)

    # PECNet specific plotting
    if ModelClass == PECNet:
        # Plot all sampled trajectories
        for i, traj in enumerate(predicted_trajectories_sampled):
            denorm_traj = denormalize_trajectory(traj, mean_x, std_x, mean_y, std_y)
            ax.plot(denorm_traj[:, 0], denorm_traj[:, 1], 'r--', label=f'预测轨迹 {i+1}' if i == 0 else "", markersize=1, linewidth=1, alpha=0.5)

        # Plot the predicted destination mean
        pred_dest_mean_abs = denormalize_trajectory(predicted_endpoint_means_sampled, mean_x, std_x, mean_y, std_y).squeeze()
        ax.plot(pred_dest_mean_abs[0], pred_dest_mean_abs[1], 'm*', label='预测目的地均值', markersize=8) 

        # Plot the confidence ellipse around the predicted destination mean
        current_logvar = predicted_endpoint_logvars_sampled # (2,)
        current_mean = predicted_endpoint_means_sampled # (2,) - normalized

        # Denormalize the mean for ellipse center
        current_mean_abs = denormalize_trajectory(current_mean, mean_x, std_x, mean_y, std_y).squeeze()

        # Calculate denormalized variance components
        var_x_abs = np.exp(current_logvar[0]) * (std_x**2)
        var_y_abs = np.exp(current_logvar[1]) * (std_y**2)
        cov_matrix_abs = np.array([[var_x_abs, 0], [0, var_y_abs]]) # Assuming X, Y are uncorrelated

        eigvals, eigvecs = np.linalg.eigh(cov_matrix_abs)
        order = eigvals.argsort()[::-1]
        eigvals = eigvals[order]
        eigvecs = eigvecs[:, order]

        confidence_multiplier = np.sqrt(chi2.ppf(0.95, 2))

        width = 2 * confidence_multiplier * np.sqrt(eigvals[0])
        height = 2 * confidence_multiplier * np.sqrt(eigvals[1])
        angle = np.degrees(np.arctan2(*eigvecs[:, 0][::-1]))

        ellipse = Ellipse(xy=current_mean_abs, width=width, height=height, 
                          angle=angle, color='purple', alpha=0.2, fill=True) # 半透明填充
        ax.add_patch(ellipse)

    else: # Plotting for other models (GridClassifierV1, EndToEndModel)
        # --- 修改：通用绘图逻辑 ---
        # 检查 pred_traj_abs 是否是多条轨迹（例如PECNet的采样结果）
        if pred_traj_abs.ndim == 3: # 形状为 (num_samples, num_points, 2)
            for i, traj in enumerate(pred_traj_abs):
                ax.plot(traj[:, 0], traj[:, 1], 'r--', label=f'预测轨迹 {i+1}' if i == 0 else "", markersize=1, linewidth=1, alpha=0.5)
        else: # 形状为 (num_points, 2)
        ax.plot(pred_traj_abs[:, 0], pred_traj_abs[:, 1], 'r-o', label='预测未来轨迹', markersize=2, linewidth=2)

        # 检查 pred_dest_abs 是否是多个点
        if 'pred_dest_abs_sampled' in locals() and pred_dest_abs_sampled is not None:
            ax.plot(pred_dest_abs_sampled[:, 0], pred_dest_abs_sampled[:, 1], 'm*', label='预测目的地(采样)', markersize=8, alpha=0.6)
        elif 'pred_dest_mean_abs_offsetted' in locals() and pred_dest_mean_abs_offsetted is not None: # PECNet 均值
            ax.plot(pred_dest_mean_abs_offsetted[0], pred_dest_mean_abs_offsetted[1], 'm*', label='预测目的地(均值)', markersize=12)
        else:
            ax.plot(pred_dest_abs[0], pred_dest_abs[1], 'm*', label='预测目的地', markersize=12)

        # 绘制真实目的地
    ax.plot(gt_dest_abs[0], gt_dest_abs[1], 'yH', label='真实目的地', markersize=8)

        # 绘制置信椭圆 (如果可用)
        if 'predicted_endpoint_means_sampled' in locals() and predicted_endpoint_means_sampled is not None and \
           'predicted_endpoint_logvars_sampled' in locals() and predicted_endpoint_logvars_sampled is not None:
            
            confidence_multiplier = np.sqrt(chi2.ppf(0.95, 2))
            
            # 反归一化均值
            current_mean_abs = denormalize_trajectory(predicted_endpoint_means_sampled, mean_x, std_x, mean_y, std_y).squeeze()

            # 计算反归一化后的方差
            var_x_abs = np.exp(predicted_endpoint_logvars_sampled[0]) * (std_x**2)
            var_y_abs = np.exp(predicted_endpoint_logvars_sampled[1]) * (std_y**2)

            # 假设X,Y不相关来创建协方差矩阵
            cov_matrix_abs = np.array([[var_x_abs, 0], [0, var_y_abs]]) 

            eigvals, eigvecs = np.linalg.eigh(cov_matrix_abs)
            order = eigvals.argsort()[::-1]
            eigvals, eigvecs = eigvals[order], eigvecs[:, order]

            width = 2 * confidence_multiplier * np.sqrt(eigvals[0])
            height = 2 * confidence_multiplier * np.sqrt(eigvals[1])
            angle = np.degrees(np.arctan2(*eigvecs[:, 0][::-1]))

            ellipse = Ellipse(xy=current_mean_abs, width=width, height=height, 
                              angle=angle, color='purple', alpha=0.2, fill=True)
            ax.add_patch(ellipse)

    # Title and labels
    # 确保坐标轴标签和图例字体大小符合规范
    ax.set_title('样本 ' + str(sample_idx) + ' 轨迹预测与可视化', fontsize=22) # 标题22号，动态样本ID
    ax.set_xlabel('X 坐标 (米)', fontsize=20) # 坐标轴标签20号
    ax.set_ylabel('Y 坐标 (米)', fontsize=20) # 坐标轴标签20号
    ax.legend(fontsize=20) # 图例字体20号
    ax.grid(True, linestyle='--', alpha=0.7)

    # --- 关键修复：从命令行参数而不是config中获取模型名称 ---
    model_name_for_output = Path(args.model_config_name).stem
    # output_dir = os.path.join(args.output_dir, model_name_for_output) # 旧的拼接逻辑
    final_output_dir = os.path.join(args.output_dir, model_name_for_output) # 使用命令行传入的output_dir，并增加模型子目录
    os.makedirs(final_output_dir, exist_ok=True) # 确保输出目录存在
    output_path = os.path.join(final_output_dir, f"prediction_sample_{sample_idx}.png") # 修正输出路径
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"可视化结果已保存至: {output_path}")
    plt.close()

if __name__ == "__main__":
    # 1. 定义命令行参数
    parser = argparse.ArgumentParser(description="加载模型，预测并可视化LMDB样本。")
    parser.add_argument('--config', type=str, default='configs/main_config.yaml', 
                        help='主配置文件路径，例如 configs/main_config.yaml。')
    parser.add_argument('--model_config_name', type=str, required=True,
                        help='模型特定配置文件的名称（位于configs/models/下）。例如 `v1_grid_classifier_model.yaml`。')
    parser.add_argument('--sample_idx', type=int, default=-1, 
                        help='要可视化的LMDB样本索引。如果为-1则随机选择。')
    parser.add_argument('--checkpoint', type=str, required=True, 
                        help='已训练模型权重文件的路径。')
    parser.add_argument('--num_samples', type=int, default=1, 
                        help='PECNet模型进行预测时采样的轨迹数量（用于多模态预测）。')
    parser.add_argument('--output_dir', type=str, default='outputs/inference_results', 
                        help='可视化结果图片保存的根目录。')
    args = parser.parse_args()

    # --- 关键修复：确保加载了所有需要的配置文件 ---
    # 2. 加载并合并配置
    main_config_path = args.config
    model_specific_config_path = os.path.join('configs', 'models', args.model_config_name)
    data_preprocessing_config_path = 'configs/data_preprocessing.yaml' # 总是加载数据预处理配置
    default_config_path = 'configs/default.yaml' 

    # 使用新的load_config函数一次性加载所有相关配置
    config = load_config(default_config_path, main_config_path, data_preprocessing_config_path, model_specific_config_path)
    
    # 调用可视化函数
    predict_and_visualize(config, args.sample_idx, args.checkpoint) 