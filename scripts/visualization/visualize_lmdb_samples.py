# @Time    : 2024/07/30
# <AUTHOR> AI Assistant
# @File    : visualize_lmdb_samples.py
# @Description : 可视化LMDB数据库中的预处理样本，包括环境ROI序列和轨迹的动态变化。
# @用途 : 
#   本脚本用于从LMDB数据库中读取指定索引的样本，并进行动画可视化。它能够展示环境ROI序列（DEM、坡度、坡向、地表覆盖）
#   以及历史轨迹、未来真实轨迹和目的地点的动态变化，帮助用户直观地理解数据样本的结构和时空特征。
# @原理 : 
#   - **LMDB读取:** 使用 `lmdb` 库以只读模式打开数据库，并通过 `pickle` 反序列化样本数据。
#   - **数据提取与逆归一化:** 从样本中提取环境ROI序列、轨迹、目的地等信息，并根据预先计算的归一化统计数据进行逆归一化，
#     将归一化坐标还原为物理世界坐标，以便在地图上正确显示。
#   - **物理到像素坐标转换:** 将物理世界坐标转换为对应图像（ROI）的像素坐标，确保轨迹可以正确叠加在图像上。
#   - **全局背景加载:** 加载一个覆盖整个轨迹范围的全局环境背景（DEM或地表覆盖），提供更大的空间上下文。
#   - **Matplotlib动画:** 使用 `matplotlib.animation.FuncAnimation` 创建动画，在每个时间步更新环境ROI和轨迹的显示，
#     包括当前历史段、原始采样点、当前位置点以及最终的未来轨迹和目的地。
#   - **多子图布局:** 利用 `gridspec` 创建灵活的图表布局，同时展示全局视图和局部环境特征视图。
# @输入 : 
#   - `lmdb_dir` (命令行参数): LMDB数据库的路径（例如：`data/processed_lmdb_.../train`）。
#   - `sample_index` (命令行参数): 要可视化的样本索引。
#   - `env_background_type` (命令行参数): 环境背景类型（`dem` 或 `landcover`）。
#   - `interval_ms` (命令行参数): 动画帧之间的间隔时间（毫秒）。
#   - 配置文件：`configs/main_config.yaml` 和 `configs/data_preprocessing.yaml`，用于获取数据路径、环境地图信息、网格分辨率等。
#   - 归一化统计文件：位于LMDB数据库目录下的 `normalization_stats.pkl`。
# @输出 : 
#   - 动态生成的matplotlib图表，展示轨迹和环境的动画。
#   - 命令行输出调试信息和进度。
# @处理方法 : 
#   - 解析命令行参数获取输入。
#   - 打开LMDB环境，读取指定样本。
#   - 加载归一化统计数据和项目配置。
#   - 进行坐标逆归一化和物理-像素转换。
#   - 初始化matplotlib图表，包括全局背景和局部环境特征子图。
#   - 定义 `init` 和 `update` 函数用于动画的初始化和每帧更新逻辑。
#   - 创建 `FuncAnimation` 对象并显示动画。
#   - 统一使用中文标签和标题，优化图表的可读性和美观性。

import lmdb
import pickle
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib
from matplotlib.colors import ListedColormap
import sys
import os
import matplotlib.colors as mpl
import math
from matplotlib.animation import FuncAnimation
import yaml
import logging 

# 将项目根目录添加到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入我们自己的config_loader和environment_processor
from src.utils.config_loader import load_config, update_config 
from src.data.environment_processor import _load_super_window
import rasterio

# 从src.utils.normalization导入必要的函数
from src.utils.normalization import denormalize_by_stats, denormalize_trajectory

# 配置日志级别，确保调试信息输出
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# 设置中文字体
matplotlib.rcParams['font.family'] = ['WenQuanYi Zen Hei', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False # 解决负号显示问题

# 辅助函数：物理坐标到像素坐标转换 (针对特定ROI)
def physical_to_pixel_coords(physical_coords, roi_origin_physical_x, roi_origin_physical_y, grid_resolution_m):
    """
    将物理坐标转换为给定ROI的像素坐标。
    roi_origin_physical_x, roi_origin_physical_y: ROI左上角的物理坐标
    grid_resolution_m: 每个像素对应的物理尺寸
    """
    pixel_x = (physical_coords[:, 0] - roi_origin_physical_x) / grid_resolution_m
    pixel_y = (physical_coords[:, 1] - roi_origin_physical_y) / grid_resolution_m
    return np.stack([pixel_x, pixel_y], axis=1)

# 辅助函数：反归一化并累积delta_x, delta_y到绝对坐标
def denormalize_and_accumulate_deltas(normalized_deltas, history_last_point_global, normalization_stats):
    """
    反归一化归一化的delta_x, delta_y，并累积到绝对坐标。
    Args:
        normalized_deltas (np.ndarray): 归一化的 (delta_x, delta_y) 序列. 形状 (T, 2).
        history_last_point_global (np.ndarray): 历史轨迹的最后一个点的全局绝对坐标 (x, y). 形状 (2,).
        normalization_stats (dict): 归一化统计数据。
    Returns:
        np.ndarray: 反归一化并累积后的绝对坐标序列. 形状 (T, 2).
    """
    if normalized_deltas.shape[0] == 0:
        return np.array([])

    denormalized_delta_x = denormalize_by_stats(
        normalized_deltas[:, 0],
        normalization_stats['target']['delta_x']['mean'],
        normalization_stats['target']['delta_x']['std']
    )
    denormalized_delta_y = denormalize_by_stats(
        normalized_deltas[:, 1],
        normalization_stats['target']['delta_y']['mean'],
        normalization_stats['target']['delta_y']['std']
    )

    accumulated_abs_coords = np.zeros_like(normalized_deltas)
    current_abs_x, current_abs_y = history_last_point_global[0], history_last_point_global[1]

    for i in range(normalized_deltas.shape[0]):
        current_abs_x += denormalized_delta_x[i]
        current_abs_y += denormalized_delta_y[i]
        accumulated_abs_coords[i, 0] = current_abs_x
        accumulated_abs_coords[i, 1] = current_abs_y
    
    return accumulated_abs_coords

# 定义地表覆盖类别和颜色 (需要与preprocess_script.py中的CONFIG['data_preprocessing']['landcover_classes']一致)
# 这些颜色仅为示例，可根据需要调整
LANDCOVER_CLASSES = [10, 20, 30, 40, 50, 60, 80, 90, 255] # 假设的类别值
LANDCOVER_COLORS = [
    '#0077BE',  # 10 - 水域 (蓝色)
    '#80CCFF',  # 20 - 湿地 (浅蓝色)
    '#90EE90',  # 30 - 草地 (浅绿色)
    '#228B22',  # 40 - 灌木地 (深绿色)
    '#CD5C5C',  # 50 - 建筑用地 (红褐色)
    '#FFD700',  # 60 - 农田 (金黄色)
    '#006400',  # 80 - 森林 (深绿色)
    '#DEB887',  # 90 - 荒地 (棕色)
    '#808080',  # 255 - 未分类 (灰色)
] 

# 创建自定义colormap
landcover_cmap = ListedColormap(LANDCOVER_COLORS)

def visualize_lmdb_sample_animation(lmdb_path: str, sample_index: int = 0, env_background_type: str = 'dem', interval_ms: int = 200):
    """
    从LMDB数据库中读取指定索引的样本，并进行动画可视化，展示环境ROI序列和轨迹的动态变化。
    """
    # 调整sys.path，确保能导入项目根目录下的模块
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    if project_root not in sys.path:
        sys.path.append(project_root)

    lmdb_env = None
    try:
        lmdb_env = lmdb.open(lmdb_path, readonly=True, lock=False)
        with lmdb_env.begin() as txn:
            keys = [key for key, _ in txn.cursor()]
            keys.sort()
            
            if sample_index < 0 or sample_index >= len(keys):
                print(f"错误: 样本索引 {sample_index} 超出范围。有效索引范围是 0 到 {len(keys) - 1}。")
                return
            
            key_to_fetch = keys[sample_index]
            value = txn.get(key_to_fetch)
            
            if value is None:
                print(f"错误: 未找到索引为 {sample_index} (键 {key_to_fetch.decode('ascii')}) 的样本。")
                return

            sample = pickle.loads(value)

            print(f"--- 正在为LMDB数据库 '{lmdb_path}' 中的样本 (索引: {sample_index}, Key: {key_to_fetch.decode('ascii')}) 生成动画 ---")
            
            # 提取数据
            trajectory_sequence = sample.get('history_features') # 聚合历史轨迹
            environment_roi_sequence = sample.get('environment_roi') # 环境ROI序列 (T, C, H, W)
            land_cover_roi_sequence = sample.get('land_cover_roi_raw_sequence', []) # 原始地表覆盖ROI序列 (T, H, W)
            ground_truth_trajectory = sample.get('ground_truth_trajectory') # 真实未来轨迹
            ground_truth_destination = sample.get('ground_truth_destination') # 真实目的地
            original_history_points_sequence = sample.get('original_history_points', []) # 原始轨迹点序列

            file_id = sample.get('file_id', 'N/A')
            timestamp_ms = sample.get('timestamp_ms', 'N/A')

            # Debug: 打印 environment_roi_sequence 的初始信息
            print(f"DEBUG: environment_roi_sequence 形状: {environment_roi_sequence.shape}")
            print(f"DEBUG: environment_roi_sequence 最小值: {environment_roi_sequence.min()}")
            print(f"DEBUG: environment_roi_sequence 最大值: {environment_roi_sequence.max()}")
            print(f"DEBUG: environment_roi_sequence DEM通道形状: {environment_roi_sequence[0, :, :].shape}")
            print(f"DEBUG: environment_roi_sequence DEM通道值范围: [{environment_roi_sequence[:, 0, :, :].min()}, {environment_roi_sequence[:, 0, :, :].max()}]")
            print(f"DEBUG: environment_roi_sequence Slope通道形状: {environment_roi_sequence[0, 1, :, :].shape}")
            print(f"DEBUG: environment_roi_sequence Slope通道值范围: [{environment_roi_sequence[:, 1, :, :].min()}, {environment_roi_sequence[:, 1, :, :].max()}]")

            # 载入归一化参数
            stats_file = os.path.join(os.path.dirname(lmdb_path), "normalization_stats.pkl")
            normalization_stats = None
            if os.path.exists(stats_file):
                try:
                    with open(stats_file, 'rb') as f:
                        normalization_stats = pickle.load(f)
                except Exception as e:
                    print(f"错误: 加载归一化统计文件 {stats_file} 时出错: {e}")
                    
            if normalization_stats is None:
                print(f"严重错误: 归一化统计文件 {stats_file} 无法加载，无法进行可视化。")
                return
            
            # 载入项目配置 (使用config_loader合并主配置和数据预处理配置)
            main_config_path = 'configs/main_config.yaml'
            data_prep_config_path = 'configs/data_preprocessing.yaml'

            # 加载主配置并合并数据预处理配置
            config = load_config(main_config_path, data_prep_config_path)
            logging.debug(f"DEBUG: config_loader used: {os.path.abspath(load_config.__module__)}") # 打印实际加载的 config_loader 文件路径
            
            # 修正 env_dir 的构建，直接指向项目根目录下的 environment 目录
            env_dir = 'environment' 

            env_paths = {
                'dem': os.path.join(env_dir, config['data_preprocessing']['env_maps']['dem']),
                'landcover': os.path.join(env_dir, config['data_preprocessing']['env_maps']['landcover']),
                'slope': os.path.join(env_dir, config['data_preprocessing']['env_maps']['slope']),
                'aspect': os.path.join(env_dir, config['data_preprocessing']['env_maps']['aspect'])
            }
            
            # 从配置文件中获取grid_resolution_m
            grid_resolution_m = config['data_preprocessing']['environment']['grid_resolution_m']

            # 逆归一化所有轨迹坐标
            denorm_history = denormalize_trajectory(trajectory_sequence[:, :2], 
                                                normalization_stats['history']['x']['mean'],
                                                normalization_stats['history']['x']['std'],
                                                normalization_stats['history']['y']['mean'],
                                                normalization_stats['history']['y']['std'])

            # 绘制真实未来轨迹
            # ground_truth_trajectory 现在是 (delta_x, delta_y, delta_t)
            # 我们需要将其转换回绝对坐标进行可视化
            # 首先，获取历史轨迹的最后一个有效点的原始绝对坐标
            last_observed_idx = np.where(sample['history_mask'] == 1)[0]
            if len(last_observed_idx) > 0:
                # 找到历史轨迹中最后一个有效观测点在原始坐标系下的位置
                last_obs_point_global = sample['original_history_points'][last_observed_idx[-1], :2]
            else:
                # 如果没有历史观测点 (例如，样本有问题或历史窗口全为间歇/填充)
                # 则使用历史轨迹的第一个点的原始绝对坐标作为基准
                # 这里需要更鲁棒的逻辑，目前假设至少有1个历史点
                if sample['original_history_points'].shape[0] > 0:
                    # 优先使用denorm_history中的最后一个有效点
                    last_obs_point_global = denorm_history[0, :2] # 使用历史的第一个点作为起始
                else:
                    # 如果原始历史轨迹点也为空，则无法进行可视化
                    print(f"错误: 样本 {sample_index} 的原始历史轨迹点为空，无法找到预测起始点。")
                    return

            # 对归一化的 delta_x, delta_y 进行反归一化并累积
            denorm_gt_trajectory = denormalize_and_accumulate_deltas(
                sample['ground_truth_trajectory'][:, :2], # 只需要delta_x, delta_y
                last_obs_point_global,
                normalization_stats
            )

            # denorm_gt_destination 是未来轨迹的最后一个点，现在直接从 denorm_gt_trajectory 获取
            if denorm_gt_trajectory.shape[0] > 0:
                denorm_gt_destination = denorm_gt_trajectory[-1, :2]
            else:
                denorm_gt_destination = last_obs_point_global # 如果未来轨迹为空，目的地就是当前点

            num_history_steps = len(environment_roi_sequence)
            roi_size_pixels = environment_roi_sequence.shape[2] # 9x9
            
            # --- 全局视图的设置 --- 
            # 计算整个轨迹的边界，以便加载足够大的全局背景
            all_trajectory_points = np.vstack((denorm_history, denorm_gt_trajectory))
            min_x, min_y = np.min(all_trajectory_points, axis=0)
            max_x, max_y = np.max(all_trajectory_points, axis=0)

            # 为全局视图增加一些边距
            margin_physical_m = config['data_preprocessing']['super_window_margin_m'] # 使用配置中的margin
            global_bbox = {
                'min_x': min_x - margin_physical_m,
                'min_y': min_y - margin_physical_m,
                'max_x': max_x + margin_physical_m,
                'max_y': max_y + margin_physical_m,
            }

            # 加载全局背景地图 (DEM和Landcover)
            global_dem_map, global_lc_map, global_slope_map, global_aspect_map, global_transform = _load_super_window(
                trajectory_file=None, # 不使用轨迹文件路径，而是使用手动提供的bbox
                env_paths=env_paths,
                config=config,
                margin_m=0, # margin已在global_bbox中添加
                manual_bbox=global_bbox
            )
            if global_dem_map is None or global_lc_map is None:
                print("错误: 无法加载全局环境地图。")
                return

            # 获取全局地图的左上角物理坐标
            global_origin_x = global_transform[2]
            global_origin_y = global_transform[5] # 注意：rasterio transform中Y轴通常是反的，原点是左上角
            
            # 重新计算origin_y以匹配imshow('lower')
            global_height_pixels = global_dem_map.shape[0]
            global_origin_y_lower_left = global_transform[5] + global_height_pixels * global_transform[4] # global_transform[4] is usually negative


            # --- Matplotlib 图形设置 --- 
            fig = plt.figure(figsize=(20, 10)) # 调整整体图大小，增加宽度和高度
            gs = fig.add_gridspec(2, 2, width_ratios=[2, 1], height_ratios=[1, 1]) # 2行2列，左侧大，右侧小

            ax_global = fig.add_subplot(gs[:, 0]) # 左侧大图，占据所有行

            # 细化右侧的子图布局
            gs_local = gs[0:, 1].subgridspec(2, 2) # 右侧的2x2子网格
            ax_local_dem = fig.add_subplot(gs_local[0, 0])
            ax_local_slope = fig.add_subplot(gs_local[0, 1])
            ax_local_aspect = fig.add_subplot(gs_local[1, 0])
            ax_local_landcover = fig.add_subplot(gs_local[1, 1])

            fig.suptitle(f'轨迹与环境动态可视化 - 文件ID: {file_id}', fontsize=22)
            
            # --- 全局视图初始化 --- 
            im_global_bg = ax_global.imshow(global_dem_map, cmap='terrain', origin='lower',
                                            extent=[global_origin_x, global_origin_x + global_dem_map.shape[1] * global_transform[0],
                                                    global_origin_y_lower_left, global_origin_y_lower_left + global_dem_map.shape[0] * abs(global_transform[4])])
            ax_global.set_title('全局DEM背景与轨迹', fontsize=20)
            ax_global.set_xlabel('X 坐标 (米)', fontsize=18)
            ax_global.set_ylabel('Y 坐标 (米)', fontsize=18)
            ax_global.tick_params(labelsize=14)
            ax_global.set_aspect('equal', adjustable='box')

            # 绘制完整轨迹（用于全局背景，初始隐藏，动画时高亮当前部分）
            line_global_full_history, = ax_global.plot(denorm_history[:, 0], denorm_history[:, 1], 'r--', linewidth=1, alpha=0.6, label='完整历史轨迹') # 虚线
            line_global_full_future, = ax_global.plot(denorm_gt_trajectory[:, 0], denorm_gt_trajectory[:, 1], 'b--', linewidth=1, alpha=0.6, label='完整未来轨迹') # 虚线
            point_global_destination, = ax_global.plot(denorm_gt_destination[0], denorm_gt_destination[1], 'gx', markersize=8, label='真实目的地')

            # 动画更新的元素
            line_global_current_history, = ax_global.plot([], [], 'r-', linewidth=3, label='当前历史段') # 粗实线
            line_global_current_original_points, = ax_global.plot([], [], 'k.', markersize=3, alpha=0.8, label='当前原始点')
            point_global_current_location, = ax_global.plot([], [], 'ro', markersize=5, label='当前位置') # 当前位置点
            

            # --- 局部视图初始化 --- 
            # 初始化局部视图的图像和轨迹线
            im_local_dem = ax_local_dem.imshow(np.zeros((roi_size_pixels, roi_size_pixels)), cmap='terrain', origin='lower', vmin=200, vmax=950) # 设置DEM的vmin/vmax
            im_local_slope = ax_local_slope.imshow(np.zeros((roi_size_pixels, roi_size_pixels)), cmap='viridis', origin='lower', vmin=0, vmax=45) # 设置坡度的vmin/vmax
            im_local_aspect = ax_local_aspect.imshow(np.zeros((roi_size_pixels, roi_size_pixels)), cmap='RdBu', origin='lower', vmin=-1, vmax=1)
            im_local_landcover = ax_local_landcover.imshow(np.zeros((roi_size_pixels, roi_size_pixels)), cmap=landcover_cmap, norm=mpl.BoundaryNorm(sorted(LANDCOVER_CLASSES) + [max(LANDCOVER_CLASSES) + 1], landcover_cmap.N), origin='lower')

            # 局部图的标题设置
            ax_local_dem.set_title('局部DEM', fontsize=16)
            ax_local_slope.set_title('局部坡度', fontsize=16)
            ax_local_aspect.set_title('局部坡向 (sin)', fontsize=16)
            ax_local_landcover.set_title('局部地表覆盖', fontsize=16)

            # 统一设置局部图的坐标轴和显示
            for ax_local in [ax_local_dem, ax_local_slope, ax_local_aspect, ax_local_landcover]:
                ax_local.set_aspect('equal', adjustable='box')
                ax_local.set_xlim([0, roi_size_pixels])
                ax_local.set_ylim([0, roi_size_pixels])
                ax_local.set_xticks([]) # 隐藏刻度
                ax_local.set_yticks([]) # 隐藏刻度
                ax_local.set_box_aspect(1) # 强制局部子图为正方形

            # 局部视图的轨迹线 (只叠加在局部DEM上)
            line_local_history, = ax_local_dem.plot([], [], 'r.-', markersize=2, label='局部历史轨迹')
            line_local_original_points, = ax_local_dem.plot([], [], 'k.', markersize=0.5, alpha=0.5, label='局部原始点')
            line_local_future, = ax_local_dem.plot([], [], 'b-', label='局部未来轨迹')
            point_local_destination, = ax_local_dem.plot([], [], 'gx', markersize=5, label='局部目的地')
            
            # 动画标题 (初始显示通用标题，具体帧信息在update函数中更新)
            anim_title_text = fig.suptitle(f'样本动画 - 文件ID: {file_id}, 时间戳: {timestamp_ms}', fontsize=22)

            # 局部颜色条 (只为Landcover添加)
            cbar_local_landcover = None
            if env_background_type == 'landcover': # 确保只有当landcover是背景时才生成这个颜色条
                cbar_local_landcover = fig.colorbar(im_local_landcover, ax=ax_local_landcover, ticks=list(LANDCOVER_CLASSES))
                cbar_labels_dict = {
                    10: '水域', 20: '湿地', 30: '草地', 40: '灌木地', 50: '建筑用地', 
                    60: '农田', 80: '森林', 90: '荒地', 255: '未分类'
                }
                cbar_tick_labels = [cbar_labels_dict.get(key, '未知') for key in sorted(LANDCOVER_CLASSES)]
                cbar_local_landcover.ax.set_yticklabels(cbar_tick_labels, fontsize=10)
                cbar_local_landcover.set_label('地表覆盖类别', fontsize=12)


            def init():
                # 全局视图初始化
                im_global_bg.set_array(global_dem_map if env_background_type == 'dem' else global_lc_map)
                im_global_bg.set_cmap('terrain' if env_background_type == 'dem' else landcover_cmap)
                if env_background_type == 'landcover':
                    im_global_bg.set_norm(mpl.BoundaryNorm(sorted(LANDCOVER_CLASSES) + [max(LANDCOVER_CLASSES) + 1], landcover_cmap.N))

                line_global_current_history.set_data([], [])
                line_global_current_original_points.set_data([], [])
                point_global_current_location.set_data([], [])

                # 局部视图初始化
                im_local_dem.set_array(np.zeros((roi_size_pixels, roi_size_pixels)))
                im_local_slope.set_array(np.zeros((roi_size_pixels, roi_size_pixels)))
                im_local_aspect.set_array(np.zeros((roi_size_pixels, roi_size_pixels)))
                im_local_landcover.set_array(np.zeros((roi_size_pixels, roi_size_pixels)))

                line_local_history.set_data([], [])
                line_local_original_points.set_data([], [])
                line_local_future.set_data([], [])
                point_local_destination.set_data([], [])
                
                return im_global_bg, line_global_current_history, line_global_current_original_points, point_global_current_location, \
                       im_local_dem, im_local_slope, im_local_aspect, im_local_landcover, \
                       line_local_history, line_local_original_points, line_local_future, point_local_destination

            def update(frame):
                # --- 全局视图更新 --- 
                # 更新全局图的当前历史轨迹部分
                global_pixel_history_segment = physical_to_pixel_coords(denorm_history[:frame+1], global_origin_x, global_origin_y_lower_left, grid_resolution_m)
                line_global_current_history.set_data(global_pixel_history_segment[:, 0], global_pixel_history_segment[:, 1])
                
                # 更新全局图的当前原始点（只显示当前聚合窗口的原始点）
                if frame < len(original_history_points_sequence) and original_history_points_sequence[frame].size > 0:
                    current_original_points_global = original_history_points_sequence[frame]
                    # --- 关键修复：确保传递给 physical_to_pixel_coords 的是二维数组 ---
                    pixel_original_points_global = physical_to_pixel_coords(np.atleast_2d(current_original_points_global), global_origin_x, global_origin_y_lower_left, grid_resolution_m)
                    line_global_current_original_points.set_data(pixel_original_points_global[:, 0], pixel_original_points_global[:, 1])
                else:
                    line_global_current_original_points.set_data([],[])

                # 更新全局图的当前位置点
                current_loc_global = physical_to_pixel_coords(denorm_history[frame:frame+1], global_origin_x, global_origin_y_lower_left, grid_resolution_m)
                point_global_current_location.set_data(current_loc_global[:, 0], current_loc_global[:, 1])

                # --- 局部视图更新 --- 
                current_env_roi = environment_roi_sequence[frame] # (C, H, W)
                current_lc_roi = land_cover_roi_sequence[frame] # (H, W)

                # Debug: 打印当前帧的局部ROI信息
                print(f"DEBUG Update Frame {frame}: current_env_roi 形状: {current_env_roi.shape}")
                print(f"DEBUG Update Frame {frame}: current_env_roi DEM通道值范围: [{current_env_roi[0, :, :].min()}, {current_env_roi[0, :, :].max()}]")
                print(f"DEBUG Update Frame {frame}: current_env_roi Slope通道值范围: [{current_env_roi[1, :, :].min()}, {current_env_roi[1, :, :].max()}]")
                print(f"DEBUG Update Frame {frame}: current_env_roi Aspect通道值范围: [{current_env_roi[2, :, :].min()}, {current_env_roi[2, :, :].max()}]")
                print(f"DEBUG Update Frame {frame}: current_lc_roi 形状: {current_lc_roi.shape}")
                print(f"DEBUG Update Frame {frame}: current_lc_roi 值范围: [{current_lc_roi.min()}, {current_lc_roi.max()}]")

                # 获取当前ROI的中心物理坐标 (对应当前聚合历史点的坐标)
                roi_center_physical_x = denorm_history[frame, 0]
                roi_center_physical_y = denorm_history[frame, 1]
                
                # 局部ROI的左下角物理坐标（origin='lower'）
                local_roi_origin_x = roi_center_physical_x - (roi_size_pixels / 2.0) * grid_resolution_m
                local_roi_origin_y = roi_center_physical_y - (roi_size_pixels / 2.0) * grid_resolution_m

                # 更新局部背景图像
                im_local_dem.set_array(current_env_roi[0, :, :]) # DEM是第一个通道
                im_local_slope.set_array(current_env_roi[1, :, :]) # 坡度是第二个通道
                im_local_aspect.set_array(current_env_roi[2, :, :]) # 坡向正弦是第三个通道
                im_local_landcover.set_array(current_lc_roi) # 原始地表覆盖ROI

                # 更新局部历史轨迹
                pixel_local_history_segment = physical_to_pixel_coords(denorm_history[:frame+1], local_roi_origin_x, local_roi_origin_y, grid_resolution_m)
                line_local_history.set_data(pixel_local_history_segment[:, 0], pixel_local_history_segment[:, 1])

                # 更新局部原始点
                if frame < len(original_history_points_sequence) and original_history_points_sequence[frame].size > 0:
                    pixel_local_original_points = physical_to_pixel_coords(np.atleast_2d(original_history_points_sequence[frame]), local_roi_origin_x, local_roi_origin_y, grid_resolution_m)
                line_local_original_points.set_data(pixel_local_original_points[:, 0], pixel_local_original_points[:, 1])
                else:
                    line_local_original_points.set_data([],[])

                # 只有在最后一个历史步长才绘制未来轨迹和目的地
                if frame == num_history_steps - 1:
                    pixel_local_gt_trajectory = physical_to_pixel_coords(denorm_gt_trajectory, local_roi_origin_x, local_roi_origin_y, grid_resolution_m)
                    pixel_local_gt_destination = physical_to_pixel_coords(denorm_gt_destination[np.newaxis, :], local_roi_origin_x, local_roi_origin_y, grid_resolution_m).squeeze()
                    line_local_future.set_data(pixel_local_gt_trajectory[:, 0], pixel_local_gt_trajectory[:, 1])
                    point_local_destination.set_data([pixel_local_gt_destination[0]], [pixel_local_gt_destination[1]]) # 修正DeprecationWarning
                else:
                    line_local_future.set_data([], [])
                    point_local_destination.set_data([], [])

                # 更新标题
                anim_title_text.set_text(f'样本动画 - 文件ID: {file_id}, 时间戳: {timestamp_ms}, 历史步长: {frame+1}/{num_history_steps}')

                return im_global_bg, line_global_current_history, line_global_current_original_points, point_global_current_location, \
                       im_local_dem, im_local_slope, im_local_aspect, im_local_landcover, \
                       line_local_history, line_local_original_points, line_local_future, point_local_destination

            # 创建动画
            anim = FuncAnimation(fig, update, frames=num_history_steps, 
                                 init_func=init, blit=False, interval=interval_ms, repeat=False) # 暂时禁用 blit
            
            # 添加图例（一次性添加所有可能出现的图例元素）
            handles_global, labels_global = ax_global.get_legend_handles_labels()
            by_label_global = dict(zip(labels_global, handles_global))
            if by_label_global:
                ax_global.legend(by_label_global.values(), by_label_global.keys(), 
                                 loc='upper left', bbox_to_anchor=(1.05, 1), # 将图例放置在轴的右上方，避免覆盖地图
                                 fontsize=10)

            handles_local, labels_local = ax_local_dem.get_legend_handles_labels()
            by_label_local = dict(zip(labels_local, handles_local))
            if by_label_local:
                # 移除重复的图例（例如，只有local dem subplot需要轨迹图例）
                unique_labels = []
                unique_handles = []
                for label, handle in zip(labels_local, handles_local):
                    if label not in unique_labels:
                        unique_labels.append(label)
                        unique_handles.append(handle)
                ax_local_dem.legend(unique_handles, unique_labels, loc='upper left', fontsize=8)

            plt.tight_layout(rect=[0, 0.03, 0.98, 0.95]) # 调整布局，为suptitle和全局图例留出空间
            plt.show()

    except lmdb.Error as e:
        print(f"错误: 无法打开LMDB环境 '{lmdb_path}': {e}")
    except Exception as e:
        print(f"错误: 生成动画时出错: {e}")
        import traceback
        traceback.print_exc(file=sys.stderr)
    finally:
        if lmdb_env:
            lmdb_env.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="从LMDB数据库中读取并可视化预处理样本的动画。")
    parser.add_argument('--lmdb_dir', type=str, default='data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq_sequence/train', 
                        help='LMDB数据库的路径，例如 data/processed_lmdb/train 或 data/processed_lmdb/val。')
    parser.add_argument('--sample_index', type=int, default=0, 
                        help='要可视化的样本索引。')
    parser.add_argument('--env_background_type', type=str, choices=['dem', 'landcover'], default='dem',
                        help='环境背景类型：dem (DEM地形) 或 landcover (地表覆盖)。')
    parser.add_argument('--interval_ms', type=int, default=200,
                        help='动画帧之间的间隔时间（毫秒）。')
    args = parser.parse_args()
    visualize_lmdb_sample_animation(args.lmdb_dir, args.sample_index, args.env_background_type, args.interval_ms) 