#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建时间: 2025-01-29
功能: V3 MaskedAutoregressiveModel 测试和可视化脚本
输入: 训练好的V3模型检查点、验证数据集
输出: 轨迹预测可视化图表，包括历史轨迹、真实轨迹和预测轨迹
原理: 加载最佳模型，对验证集进行预测，在DEM地形图上可视化结果
处理方法: 
1. 加载训练好的V3模型
2. 从验证集中选择样本进行预测
3. 反归一化坐标到真实世界坐标
4. 在地形图上绘制历史、真实和预测轨迹
5. 分析预测质量和地形适应性
"""

import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import pickle
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

from src.utils.config_loader import load_config
from src.utils.normalization import load_normalization_stats
from src.data.datasets import LMDBDataset
from src.data.collate import custom_collate_fn
from src.models.v3_masked_autoregressive.masked_autoregressive_model import MaskedAutoregressiveModel
# from src.data.environment_processor import EnvironmentProcessor  # 暂时注释掉
from torch.utils.data import DataLoader

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_trained_model(checkpoint_path, config):
    """加载训练好的V3模型"""
    model = MaskedAutoregressiveModel(config)
    
    # 加载最佳模型权重
    if checkpoint_path.exists():
        logging.info(f"正在加载模型权重: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint)
        logging.info("模型权重加载完成")
    else:
        logging.error(f"模型检查点不存在: {checkpoint_path}")
        return None
    
    model.eval()
    return model

def denormalize_coordinates(normalized_coords, stats):
    """反归一化坐标到真实世界坐标"""
    # 使用历史轨迹的归一化统计数据
    x_mean = stats['history_features']['x']['mean']
    x_std = stats['history_features']['x']['std']
    y_mean = stats['history_features']['y']['mean'] 
    y_std = stats['history_features']['y']['std']
    
    # 反归一化
    real_x = normalized_coords[..., 0] * x_std + x_mean
    real_y = normalized_coords[..., 1] * y_std + y_mean
    
    return np.stack([real_x, real_y], axis=-1)

def visualize_prediction(sample_idx, history_trajectory, history_mask, predicted_trajectory, 
                        ground_truth_trajectory, original_history_points, stats, save_path):
    """可视化单个样本的预测结果"""
    
    # 反归一化所有轨迹到真实世界坐标
    if isinstance(history_trajectory, torch.Tensor):
        history_trajectory = history_trajectory.cpu().numpy()
    if isinstance(predicted_trajectory, torch.Tensor):
        predicted_trajectory = predicted_trajectory.cpu().numpy()
    if isinstance(ground_truth_trajectory, torch.Tensor):
        ground_truth_trajectory = ground_truth_trajectory.cpu().numpy()
    if isinstance(original_history_points, torch.Tensor):
        original_history_points = original_history_points.cpu().numpy()
    if isinstance(history_mask, torch.Tensor):
        history_mask = history_mask.cpu().numpy()
    
    # 反归一化坐标
    real_history = denormalize_coordinates(history_trajectory, stats)
    real_predicted = denormalize_coordinates(predicted_trajectory, stats)
    real_ground_truth = denormalize_coordinates(ground_truth_trajectory, stats)
    real_original_history = denormalize_coordinates(original_history_points, stats)
    
    # 获取有效的历史点（根据掩码）
    valid_mask = history_mask.astype(bool)
    valid_history = real_history[valid_mask]
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # === 左图: 完整轨迹视图 ===
    # 绘制历史轨迹
    if len(valid_history) > 0:
        ax1.plot(valid_history[:, 0], valid_history[:, 1], 
                'go-', linewidth=3, markersize=4, label='观测历史', alpha=0.8)
        ax1.scatter(valid_history[0, 0], valid_history[0, 1], 
                   c='green', s=100, marker='s', label='历史起点', zorder=10)
        ax1.scatter(valid_history[-1, 0], valid_history[-1, 1], 
                   c='darkgreen', s=100, marker='o', label='观测终点', zorder=10)
    
    # 绘制真实轨迹
    ax1.plot(real_ground_truth[:, 0], real_ground_truth[:, 1], 
            'b-', linewidth=3, label='真实轨迹', alpha=0.7)
    ax1.scatter(real_ground_truth[-1, 0], real_ground_truth[-1, 1], 
               c='blue', s=100, marker='*', label='真实终点', zorder=10)
    
    # 绘制预测轨迹
    ax1.plot(real_predicted[:, 0], real_predicted[:, 1], 
            'r--', linewidth=3, label='预测轨迹', alpha=0.8)
    ax1.scatter(real_predicted[-1, 0], real_predicted[-1, 1], 
               c='red', s=100, marker='X', label='预测终点', zorder=10)
    
    ax1.set_xlabel('X坐标 (米)', fontsize=20)
    ax1.set_ylabel('Y坐标 (米)', fontsize=20)
    ax1.set_title(f'样本 {sample_idx} - 完整轨迹对比', fontsize=22)
    ax1.legend(fontsize=16)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(labelsize=18)
    
    # === 右图: 轨迹详细对比视图 ===
    # 暂时不加载地形数据，专注于轨迹对比
    
    # 在地形图上绘制轨迹
    if len(valid_history) > 0:
        ax2.plot(valid_history[:, 0], valid_history[:, 1], 
                'go-', linewidth=4, markersize=6, label='观测历史', alpha=0.9)
        ax2.scatter(valid_history[0, 0], valid_history[0, 1], 
                   c='green', s=120, marker='s', edgecolor='white', linewidth=2, 
                   label='历史起点', zorder=15)
        ax2.scatter(valid_history[-1, 0], valid_history[-1, 1], 
                   c='darkgreen', s=120, marker='o', edgecolor='white', linewidth=2,
                   label='观测终点', zorder=15)
    
    ax2.plot(real_ground_truth[:, 0], real_ground_truth[:, 1], 
            'b-', linewidth=4, label='真实轨迹', alpha=0.8)
    ax2.scatter(real_ground_truth[-1, 0], real_ground_truth[-1, 1], 
               c='blue', s=120, marker='*', edgecolor='white', linewidth=2,
               label='真实终点', zorder=15)
    
    ax2.plot(real_predicted[:, 0], real_predicted[:, 1], 
            'r--', linewidth=4, label='预测轨迹', alpha=0.9)
    ax2.scatter(real_predicted[-1, 0], real_predicted[-1, 1], 
               c='red', s=120, marker='X', edgecolor='white', linewidth=2,
               label='预测终点', zorder=15)
    
    ax2.set_xlabel('X坐标 (米)', fontsize=20)
    ax2.set_ylabel('Y坐标 (米)', fontsize=20)
    ax2.set_title(f'样本 {sample_idx} - 轨迹详细对比', fontsize=22)
    ax2.legend(fontsize=16)
    ax2.tick_params(labelsize=18)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    # 计算预测误差
    endpoint_error = np.linalg.norm(real_predicted[-1] - real_ground_truth[-1])
    avg_trajectory_error = np.mean(np.linalg.norm(real_predicted - real_ground_truth, axis=1))
    
    logging.info(f"样本 {sample_idx} 预测质量:")
    logging.info(f"  - 终点误差: {endpoint_error:.2f} 米")
    logging.info(f"  - 平均轨迹误差: {avg_trajectory_error:.2f} 米")
    
    return endpoint_error, avg_trajectory_error

def main():
    """主函数"""
    logging.info("开始V3模型测试和可视化...")
    
    # 加载配置
    config = load_config(
        'configs/main_config.yaml',
        'configs/data_preprocessing.yaml', 
        'configs/models/v3_masked_autoregressive_model.yaml'
    )
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"使用设备: {device}")
    
    # 加载训练好的模型
    checkpoint_path = Path("checkpoints/MaskedAutoregressiveModel/best_model.pth")
    model = load_trained_model(checkpoint_path, config)
    if model is None:
        return
    
    model = model.to(device)
    
    # 加载归一化统计数据
    stats_path = Path("data/processed_lmdb_obs_5min_pred_40min_v3_with_mask/normalization_stats.pkl")
    stats = load_normalization_stats(stats_path)
    
    # 加载验证数据集
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    val_loader = DataLoader(
        val_dataset, 
        batch_size=1,  # 单个样本测试
        shuffle=False, 
        collate_fn=custom_collate_fn
    )
    
    # 初始化环境处理器（暂时注释掉）
    # env_processor = EnvironmentProcessor(config)
    
    # 创建输出目录
    output_dir = Path("outputs/v3_visualization")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 测试多个样本
    test_samples = min(5, len(val_dataset))  # 测试前5个样本
    all_endpoint_errors = []
    all_trajectory_errors = []
    
    logging.info(f"开始测试 {test_samples} 个验证样本...")
    
    with torch.no_grad():
        for i, batch in enumerate(val_loader):
            if i >= test_samples:
                break
                
            # 移动数据到设备
            for key in batch:
                if isinstance(batch[key], torch.Tensor):
                    batch[key] = batch[key].to(device)
            
            # 进行预测
            predicted_trajectory = model(
                history_trajectory=batch['history_features'],
                environment_roi=batch['environment_roi'], 
                history_mask=batch['history_mask'],
                ground_truth_trajectory=None  # 预测时不使用真实轨迹
            )
            
            # 可视化结果
            save_path = output_dir / f"v3_prediction_sample_{i+1}.png"
            
            endpoint_error, trajectory_error = visualize_prediction(
                sample_idx=i+1,
                history_trajectory=batch['history_features'][0],  # 取第一个（也是唯一的）样本
                history_mask=batch['history_mask'][0],
                predicted_trajectory=predicted_trajectory[0],
                ground_truth_trajectory=batch['ground_truth_trajectory'][0],
                original_history_points=batch['original_history_points'][0],
                stats=stats,
                save_path=save_path
            )
            
            all_endpoint_errors.append(endpoint_error)
            all_trajectory_errors.append(trajectory_error)
    
    # 输出总体统计
    logging.info("\n=== V3模型整体性能评估 ===")
    logging.info(f"测试样本数: {len(all_endpoint_errors)}")
    logging.info(f"平均终点误差: {np.mean(all_endpoint_errors):.2f} ± {np.std(all_endpoint_errors):.2f} 米")
    logging.info(f"平均轨迹误差: {np.mean(all_trajectory_errors):.2f} ± {np.std(all_trajectory_errors):.2f} 米")
    logging.info(f"可视化结果已保存到: {output_dir}")

if __name__ == "__main__":
    main() 