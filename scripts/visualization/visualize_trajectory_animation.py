# 创建时间: 2025-07-20 23:40:00+08:00
# 功能: 可视化LMDB数据库中单个样本的轨迹和环境ROI随时间变化的动画。
# 输入: LMDB数据库路径，样本索引。
# 输出: 显示matplotlib动画，包含主图（完整轨迹）和子图（随时间变化的局部环境ROI）。
# 原理: 从LMDB读取样本数据，进行逆归一化和坐标转换，使用matplotlib.animation创建动态图表。
# 处理方法: 更新图像数据和轨迹高亮以模拟时间步的推进。

import lmdb
import pickle
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import matplotlib
import os
import sys
import yaml # 新增导入
import rasterio # 新增导入
from matplotlib.colors import ListedColormap # 新增导入
from rasterio.windows import Window # 新增导入
from pathlib import Path # 新增导入

# 导入环境处理函数，用于加载超级窗口
# 确保 src 模块在 PYTHONPATH 中
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
from src.data.environment_processor import _load_super_window
from src.utils.config_loader import load_config # 从新创建的模块导入 load_config

# 设置中文字体
matplotlib.rcParams['font.family'] = ['WenQuanYi Zen Hei', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False # 解决负号显示问题

# 新增辅助函数：地表覆盖类别和颜色
LANDCOVER_CLASSES = [10, 20, 30, 40, 50, 60, 80, 90, 255] # 假设的类别值
LANDCOVER_COLORS = [
    '#0077BE',  # 10 - 水域 (蓝色)
    '#80CCFF',  # 20 - 湿地 (浅蓝色)
    '#90EE90',  # 30 - 草地 (浅绿色)
    '#228B22',  # 40 - 灌木地 (深绿色)
    '#CD5C5C',  # 50 - 建筑用地 (红褐色)
    '#FFD700',  # 60 - 农田 (金黄色)
    '#006400',  # 80 - 森林 (深绿色)
    '#DEB887',  # 90 - 荒地 (棕色)
    '#808080',  # 255 - 未分类 (灰色)
] 

# 创建自定义colormap
landcover_cmap = ListedColormap(LANDCOVER_COLORS)

# 辅助函数：逆归一化
def denormalize_coords(normalized_coords, mean_x, std_x, mean_y, std_y):
    """逆归一化X, Y坐标"""
    # 确保输入是二维的 (N, 2)
    if normalized_coords.ndim == 1:
        normalized_coords = normalized_coords[np.newaxis, :]
    denorm_x = (normalized_coords[:, 0] * std_x) + mean_x
    denorm_y = (normalized_coords[:, 1] * std_y) + mean_y
    return np.stack([denorm_x, denorm_y], axis=1)

# 辅助函数：物理坐标到像素坐标转换
def physical_to_pixel_coords(physical_coords, roi_center_physical_x, roi_center_physical_y, roi_size_pixels, grid_resolution_m):
    """
    将物理坐标转换为ROI的像素坐标。
    roi_center_physical_x, roi_center_physical_y: ROI中心的物理坐标 (对应轨迹的当前点)
    roi_size_pixels: ROI的像素边长 (例如 9)
    grid_resolution_m: 每个像素对应的物理尺寸 (例如 30米/像素)
    """
    # 确保输入是二维的 (N, 2)
    if physical_coords.ndim == 1:
        physical_coords = physical_coords[np.newaxis, :]

    # 计算ROI左下角的物理坐标
    half_roi_physical_extent = (roi_size_pixels / 2.0) * grid_resolution_m
    roi_min_physical_x = roi_center_physical_x - half_roi_physical_extent
    roi_min_physical_y = roi_center_physical_y - half_roi_physical_extent
    
    # 计算物理坐标相对于ROI左下角的偏移量
    offset_x = physical_coords[:, 0] - roi_min_physical_x
    offset_y = physical_coords[:, 1] - roi_min_physical_y

    # 转换为像素坐标
    pixel_x = offset_x / grid_resolution_m
    pixel_y = offset_y / grid_resolution_m
    
    return np.stack([pixel_x, pixel_y], axis=1)

# Removed load_config and update_config as they are moved to src/utils/config_loader.py

def animate_lmdb_sample(lmdb_path: str, sample_index: int = 0, frame_interval_ms: int = 200, save_animation: bool = False):
    """
    从LMDB数据库中读取指定索引的样本，并创建动画可视化。
    """
    # 加载配置
    config = load_config() # 现在直接从导入的函数调用
    # 从config中获取环境地图路径
    env_paths = {
        'dem': Path(config['data_preprocessing']['environment_path']) / config['data_preprocessing']['env_maps']['dem'],
        'landcover': Path(config['data_preprocessing']['environment_path']) / config['data_preprocessing']['env_maps']['landcover'],
        'slope': Path(config['data_preprocessing']['environment_path']) / config['data_preprocessing']['env_maps']['slope'],
        'aspect': Path(config['data_preprocessing']['environment_path']) / config['data_preprocessing']['env_maps']['aspect']
    }

    lmdb_env = None
    try:
        lmdb_env = lmdb.open(lmdb_path, readonly=True, lock=False)
        with lmdb_env.begin() as txn:
            keys = [key for key, _ in txn.cursor()]
            keys.sort()
            
            if sample_index < 0 or sample_index >= len(keys):
                print(f"错误: 样本索引 {sample_index} 超出范围。有效索引范围是 0 到 {len(keys) - 1}。")
                return
            
            key_to_fetch = keys[sample_index]
            value = txn.get(key_to_fetch)
            
            if value is None:
                print(f"错误: 未找到索引为 {sample_index} (键 {key_to_fetch.decode('ascii')}) 的样本。")
                return

            sample = pickle.loads(value)

            print(f"--- 正在动画可视化LMDB数据库 '{lmdb_path}' 中的样本 (索引: {sample_index}, Key: {key_to_fetch.decode('ascii')}) ---")
            
            # 提取数据
            history_norm = sample.get('history') # 聚合历史轨迹 (归一化)
            environment_roi_norm = sample.get('environment_roi') # 环境ROI (归一化) (T, C, H, W)
            ground_truth_trajectory_norm = sample.get('ground_truth_trajectory') # 真实未来轨迹 (归一化)
            ground_truth_destination_norm = sample.get('ground_truth_destination') # 真实目的地 (归一化)
            original_history_points_sequence_norm = sample.get('original_history_points', []) # 原始历史点序列 (归一化)
            land_cover_roi_sequence = sample.get('land_cover_roi_sequence', []) # 新增：原始地表覆盖ROI序列

            file_id = sample.get('file_id', 'N/A')
            timestamp_ms = sample.get('timestamp_ms', 'N/A')

            # 载入归一化参数
            stats_file = os.path.join(os.path.dirname(lmdb_path), "normalization_stats.pkl")
            normalization_stats = None
            if os.path.exists(stats_file):
                try:
                    with open(stats_file, 'rb') as f:
                        normalization_stats = pickle.load(f)
                except Exception as e:
                    print(f"错误: 加载归一化统计文件 {stats_file} 时出错: {e}")
                    return

            if normalization_stats is None:
                print("错误: 未能加载归一化统计文件，无法进行逆归一化和动画可视化。")
                return

            # 在动画开始前加载整个超级窗口
            # 这里需要传入 config 字典
            try:
                dem_super_window, _, _, _, super_window_transform = _load_super_window(
                    trajectory_file=os.path.join(config['data_preprocessing']['trajectory_path'], file_id + ".csv"),
                    env_paths=env_paths,
                    config=config, # 传入config
                    margin_m=config['data_preprocessing']['super_window_margin_m'] * 2, # 使用更大的margin
                    debug=False
                )
            except Exception as e:
                print(f"错误: 加载环境超级窗口时出错: {e}")
                return

            # --- 逆归一化所有轨迹坐标 ---
            denorm_history = denormalize_coords(history_norm[:, :2], 
                                                normalization_stats['history_mean']['x'],
                                                normalization_stats['history_std']['x'],
                                                normalization_stats['history_mean']['y'],
                                                normalization_stats['history_std']['y'])

            denorm_gt_trajectory = denormalize_coords(ground_truth_trajectory_norm, 
                                                    normalization_stats['target_x_mean'],
                                                    normalization_stats['target_x_std'],
                                                    normalization_stats['target_y_mean'],
                                                    normalization_stats['target_y_std'])
            
            denorm_gt_destination = denormalize_coords(ground_truth_destination_norm,
                                                        normalization_stats['target_x_mean'],
                                                        normalization_stats['target_x_std'],
                                                        normalization_stats['target_y_mean'],
                                                        normalization_stats['target_y_std']).squeeze()

            denorm_original_history_points_sequence = []
            for original_points_agg_window_norm in original_history_points_sequence_norm:
                denorm_original_points = denormalize_coords(original_points_agg_window_norm, 
                                                            normalization_stats['history_mean']['x'],
                                                            normalization_stats['history_std']['x'],
                                                            normalization_stats['history_mean']['y'],
                                                            normalization_stats['history_std']['y'])
                denorm_original_history_points_sequence.append(denorm_original_points)

            # --- 设置动画参数 ---
            total_frames = history_norm.shape[0] # 动画帧数等于历史轨迹的长度
            roi_size_pixels = config['data_preprocessing']['env_roi_size_pixels'] # 9x9 小ROI
            grid_resolution_m = 30 # 每个栅格30m [[memory:3786535]]
            main_roi_display_size_pixels = 30 # 主图显示范围的像素边长，对应更大的物理范围

            # --- 创建图表和子图 ---
            fig = plt.figure(figsize=(28, 8)) # 调整图表大小以容纳更多子图
            gs = fig.add_gridspec(1, 5) # 1行5列的网格布局 (主图 + DEM + Slope + Aspect + Landcover)

            # 主轨迹图
            ax_main_traj = fig.add_subplot(gs[0, 0])
            ax_main_traj.set_title('完整轨迹概览', fontsize=20)
            ax_main_traj.set_xlabel('X 坐标', fontsize=20)
            ax_main_traj.set_ylabel('Y 坐标', fontsize=20)
            ax_main_traj.grid(True)
            ax_main_traj.set_aspect('equal', adjustable='box')
            
            # 初始化主图的DEM背景
            # 注意：im_main_dem 将在 update 函数中更新数据和 extent
            # 初始 extent 设为任意值，因为会在 update 中立即更新
            im_main_dem = ax_main_traj.imshow(np.zeros((main_roi_display_size_pixels, main_roi_display_size_pixels)), 
                                                cmap='terrain', origin='lower', alpha=0.7, 
                                                extent=[0, main_roi_display_size_pixels, 0, main_roi_display_size_pixels]) # 临时设置 extent

            # 绘制完整轨迹（历史+未来真实）
            full_path = np.vstack([denorm_history, denorm_gt_trajectory])
            ax_main_traj.plot(full_path[:, 0], full_path[:, 1], 'k--', alpha=0.5, label='完整轨迹') # 虚线显示完整路径
            ax_main_traj.plot(denorm_gt_destination[0], denorm_gt_destination[1], 'gx', markersize=10, label='真实目的地')
            
            # 初始化当前观测窗口的轨迹高亮
            current_obs_line, = ax_main_traj.plot([], [], 'r-o', markersize=4, label='当前观测窗口')
            current_future_line, = ax_main_traj.plot([], [], 'b-', label='预测未来轨迹')
            current_original_points_lines = [] # 用于存储原始点的线对象
            if denorm_original_history_points_sequence: # 确保有原始点数据
                for _ in range(denorm_original_history_points_sequence[0].shape[0]):
                     line, = ax_main_traj.plot([], [], 'c.', markersize=2, alpha=0.5)
                     current_original_points_lines.append(line)


            ax_main_traj.legend(fontsize=16)

            # 环境ROI子图 (DEM, Slope, Aspect, Landcover)
            ax_dem = fig.add_subplot(gs[0, 1])
            ax_slope = fig.add_subplot(gs[0, 2])
            ax_aspect = fig.add_subplot(gs[0, 3])
            ax_landcover = fig.add_subplot(gs[0, 4]) # 新增地表覆盖子图

            ax_dem.set_title('环境ROI - DEM', fontsize=20)
            ax_slope.set_title('环境ROI - 坡度', fontsize=20)
            ax_aspect.set_title('环境ROI - 坡向 (正弦)', fontsize=20)
            ax_landcover.set_title('环境ROI - 地表覆盖', fontsize=20) # 新增标题

            # 初始化ROI图像
            im_dem = ax_dem.imshow(environment_roi_norm[0, 0, :, :], cmap='terrain', origin='lower', vmin=environment_roi_norm[:,0,:,:].min(), vmax=environment_roi_norm[:,0,:,:].max())
            im_slope = ax_slope.imshow(environment_roi_norm[0, 1, :, :], cmap='viridis', origin='lower', vmin=environment_roi_norm[:,1,:,:].min(), vmax=environment_roi_norm[:,1,:,:].max())
            im_aspect = ax_aspect.imshow(environment_roi_norm[0, 2, :, :], cmap='RdBu', origin='lower', vmin=-1, vmax=1)
            
            # 初始化地表覆盖图像
            if land_cover_roi_sequence: # 确保有地表覆盖数据
                im_landcover = ax_landcover.imshow(land_cover_roi_sequence[0], cmap=landcover_cmap, origin='lower', 
                                                    vmin=min(LANDCOVER_CLASSES), vmax=max(LANDCOVER_CLASSES))
                # 添加地表覆盖的颜色条，并设置刻度
                cbar_lc = fig.colorbar(im_landcover, ax=ax_landcover, label='地表覆盖类别', ticks=LANDCOVER_CLASSES)
                # 设置颜色条的标签
                cbar_lc.ax.set_yticklabels([
                    '水域 (10)', '湿地 (20)', '草地 (30)', '灌木地 (40)', '建筑用地 (50)', 
                    '农田 (60)', '森林 (80)', '荒地 (90)', '未分类 (255)'
                ])
            else:
                im_landcover = None # 如果没有数据，则不初始化 imshow 对象

            fig.colorbar(im_dem, ax=ax_dem, label='归一化DEM值')
            fig.colorbar(im_slope, ax=ax_slope, label='归一化坡度值')
            fig.colorbar(im_aspect, ax=ax_aspect, label='归一化坡向正弦值')


            for ax_obj in [ax_dem, ax_slope, ax_aspect, ax_landcover]: # 将 ax_landcover 加入循环
                ax_obj.set_xlabel('X 像素', fontsize=20)
                ax_obj.set_ylabel('Y 像素', fontsize=20)
                ax_obj.set_xlim([0, roi_size_pixels - 1])
                ax_obj.set_ylim([0, roi_size_pixels - 1])
                ax_obj.set_facecolor('#f0f0f0') # 设置背景色，模拟地表

            plt.tight_layout()

            # --- 动画更新函数 ---
            def update(frame):
                # 更新主轨迹图上的高亮
                # 历史轨迹的当前帧
                current_history_slice_physical = denorm_history[max(0, frame - 1):frame+1, :] # 简化的当前历史窗口
                current_obs_line.set_data(current_history_slice_physical[:, 0], current_history_slice_physical[:, 1])

                # 预测未来轨迹的当前帧
                start_of_prediction_idx = frame 
                if start_of_prediction_idx < denorm_gt_trajectory.shape[0]:
                    current_future_slice_physical = denorm_gt_trajectory[start_of_prediction_idx:, :]
                    current_future_line.set_data(current_future_slice_physical[:, 0], current_future_slice_physical[:, 1])
                else:
                    current_future_line.set_data([], []) # 隐藏

                # 更新原始历史点
                if frame < len(denorm_original_history_points_sequence):
                    current_original_points = denorm_original_history_points_sequence[frame]
                    # 确保 current_original_points 是二维数组 (N, 2)
                    if current_original_points.ndim == 1:
                        current_original_points = current_original_points[np.newaxis, :]
                    
                    # 如果当前帧的原始点数量少于初始化时的line对象数量，隐藏多余的
                    for i in range(len(current_original_points_lines)):
                        if i < current_original_points.shape[0]:
                            current_original_points_lines[i].set_data(current_original_points[i, 0], current_original_points[i, 1])
                        else:
                            current_original_points_lines[i].set_data([], []) # 隐藏多余的线
                else: # 如果没有更多原始点数据，隐藏所有线
                    for line in current_original_points_lines:
                        line.set_data([], [])

                # --- 更新主图的动态DEM背景 ---
                # 获取当前帧对应的历史轨迹中心物理坐标
                current_center_x, current_center_y = denorm_history[frame, 0], denorm_history[frame, 1]

                # 定义主图ROI的物理尺寸 (例如，比小ROI大几倍)
                main_roi_physical_extent = main_roi_display_size_pixels * grid_resolution_m # 例如 30像素 * 30m/像素 = 900m
                half_main_roi_physical_extent = main_roi_physical_extent / 2.0

                # 计算主图ROI的物理边界
                main_roi_min_x = current_center_x - half_main_roi_physical_extent
                main_roi_max_x = current_center_x + half_main_roi_physical_extent
                main_roi_min_y = current_center_y - half_main_roi_physical_extent
                main_roi_max_y = current_center_y + half_main_roi_physical_extent
                
                # 将主图ROI的物理边界转换为超级窗口的像素索引
                main_roi_row_start, main_roi_col_start = rasterio.transform.rowcol(super_window_transform, main_roi_min_x, main_roi_max_y)
                main_roi_row_end, main_roi_col_end = rasterio.transform.rowcol(super_window_transform, main_roi_max_x, main_roi_min_y)

                # 确保索引在超级窗口范围内，并调整以保证尺寸
                main_roi_row_start = max(0, main_roi_row_start)
                main_roi_row_end = min(dem_super_window.shape[0], main_roi_row_end)
                main_roi_col_start = max(0, main_roi_col_start)
                main_roi_col_end = min(dem_super_window.shape[1], main_roi_col_end)
                
                # 提取主图的DEM数据
                current_main_dem_roi = dem_super_window[main_roi_row_start:main_roi_row_end, main_roi_col_start:main_roi_col_end]

                # 更新主图的DEM背景图像和范围
                # 计算主图DEM背景的物理范围 (而不是像素范围)
                # super_window_transform 是从 DEM 超级窗口的左上角开始的仿射变换
                # rasterio.transform.window_bounds 返回 (left, bottom, right, top)
                
                # 计算当前中心点在超级窗口中的像素坐标
                current_pixel_col, current_pixel_row = rasterio.transform.colrow(super_window_transform, current_center_x, current_center_y)

                # 计算主图DEM窗口在超级窗口中的像素范围
                main_dem_half_pixels = main_roi_display_size_pixels // 2
                main_dem_col_start = int(current_pixel_col - main_dem_half_pixels)
                main_dem_row_start = int(current_pixel_row - main_dem_half_pixels)
                main_dem_col_end = int(current_pixel_col + main_dem_half_pixels + (main_roi_display_size_pixels % 2))
                main_dem_row_end = int(current_pixel_row + main_dem_half_pixels + (main_roi_display_size_pixels % 2))

                # 确保索引在超级窗口范围内
                main_dem_col_start = max(0, main_dem_col_start)
                main_dem_row_start = max(0, main_dem_row_start)
                main_dem_col_end = min(dem_super_window.shape[1], main_dem_col_end)
                main_dem_row_end = min(dem_super_window.shape[0], main_dem_row_end)
                
                # 提取主图的DEM数据
                current_main_dem_roi = dem_super_window[main_dem_row_start:main_dem_row_end, main_dem_col_start:main_dem_col_end]
                
                # 重新计算当前裁剪区域的地理边界 (extent)
                left, bottom = rasterio.transform.xy(super_window_transform, main_dem_col_start, main_dem_row_end)
                right, top = rasterio.transform.xy(super_window_transform, main_dem_col_end, main_dem_row_start)

                im_main_dem.set_data(current_main_dem_roi)
                # 设置 extent 使用地理坐标
                im_main_dem.set_extent([left, right, bottom, top])

                # 动态调整主图的X/Y轴限制以匹配DEM背景的范围
                ax_main_traj.set_xlim([left, right])
                ax_main_traj.set_ylim([bottom, top])
                
                # 重新计算主图的纵横比
                ax_main_traj.set_aspect('equal', adjustable='box')

                # 更新ROI子图
                if frame < environment_roi_norm.shape[0]: # 确保帧索引在范围内
                    im_dem.set_data(environment_roi_norm[frame, 0, :, :])
                    im_slope.set_data(environment_roi_norm[frame, 1, :, :])
                    im_aspect.set_data(environment_roi_norm[frame, 2, :, :])
                    if im_landcover: # 检查 im_landcover 是否已初始化
                        im_landcover.set_data(land_cover_roi_sequence[frame]) # 更新地表覆盖图像
                    
                    # 返回所有更新的艺术家对象
                    return_artists = [current_obs_line, current_future_line, im_main_dem, im_dem, im_slope, im_aspect] + current_original_points_lines
                    if im_landcover:
                        return_artists.append(im_landcover) # 添加地表覆盖图像到返回列表
                    
                    return return_artists
                else:
                    return_artists = [current_obs_line, current_future_line, im_main_dem, im_dem, im_slope, im_aspect] + current_original_points_lines
                    if im_landcover:
                        return_artists.append(im_landcover)
                    return return_artists # 返回，即使没有更新


            ani = animation.FuncAnimation(fig, update, frames=total_frames, 
                                        interval=frame_interval_ms, blit=True, repeat=False) # 保持 blit=True

            if save_animation:
                animation_output_path = f"animation_sample_{sample_index}.gif"
                print(f"正在保存动画至 {animation_output_path} (这可能需要一些时间)...")
                ani.save(animation_output_path, writer='pillow', fps=1000/frame_interval_ms) # 使用pillow writer
                print("动画保存完成。")
            else:
                plt.show()

    except lmdb.Error as e:
        print(f"错误: 无法打开LMDB环境 '{lmdb_path}': {e}")
    except Exception as e:
        print(f"错误: 可视化样本时出错: {e}")
        import traceback
        traceback.print_exc(file=sys.stderr)
    finally:
        if lmdb_env:
            lmdb_env.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="从LMDB数据库中读取并动画可视化预处理样本。")
    parser.add_argument('--lmdb_dir', type=str, default='data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train', 
                        help='LMDB数据库的路径，例如 data/processed_lmdb/train 或 data/processed_lmdb/val。')
    parser.add_argument('--sample_index', type=int, default=0, 
                        help='要可视化动画的样本索引。')
    parser.add_argument('--frame_interval_ms', type=int, default=200, 
                        help='动画帧之间的时间间隔（毫秒）。')
    parser.add_argument('--save_animation', action='store_true', 
                        help='如果设置，则将动画保存为GIF文件，而不是实时显示。')
    
    args = parser.parse_args()
    animate_lmdb_sample(args.lmdb_dir, args.sample_index, args.frame_interval_ms, args.save_animation) 