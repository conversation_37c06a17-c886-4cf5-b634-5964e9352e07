# -*- coding: utf-8 -*-
"""
创建时间: 2024年7月22日
功能: 可视化LMDB数据集中的单个样本，展示历史轨迹在DEM背景上的位置，
      并为每个历史轨迹点显示其对应的局部环境ROI图像。
输入:
    - LMDB数据集路径 (train/val)
    - 归一化统计文件 (normalization_stats.pkl)
    - DEM环境地图 (flat_areas.tif)
输出:
    - 保存的可视化图片文件 (PNG格式)

原理及处理方法:
1. 加载必要的库，包括PyTorch, NumPy, Matplotlib, Rasterio (用于处理GIS数据), LMDB, Pickle。
2. 配置项目根目录到系统路径，确保模块导入正确。
3. 定义数据路径和配置文件路径。
4. 加载归一化统计数据 (normalization_stats.pkl)，用于反归一化轨迹坐标。
5. 初始化 LMDBDataset 并加载一个训练样本。
6. 从样本中提取历史轨迹、局部环境ROI、真实目的地和真实未来轨迹。
7. 对历史轨迹和目的地坐标进行反归一化，将其转换回世界坐标系。
8. 加载背景DEM图像 (flat_areas.tif) 并获取其地理变换信息和边界。
9. 创建一个大的Matplotlib图，包含一个主轴用于显示整个轨迹，以及一个子图网格用于显示每个时间步的ROI。
10. 在主轴上绘制DEM背景，然后叠加绘制历史轨迹和真实目的地。
11. 遍历历史轨迹的每个时间步：
    a. 在主图上标记当前时间步的轨迹点。
    b. 从 environment_roi 中提取对应时间步的DEM通道（通常是第一个通道）。
    c. 在 ROI 网格的相应子图中显示这个 9x9 的 DEM ROI 图像。
    d. 为每个 ROI 子图添加标题，指示其对应的历史时间步。
12. 添加图例、标题等，并保存为PNG文件。
"""

import sys
from pathlib import Path
import torch
import numpy as np
import matplotlib.pyplot as plt
import rasterio
import lmdb
import pickle
import os

# 将项目根目录添加到PYTHONPATH
project_root = Path(__file__).resolve().parents[2]
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.data.datasets import LMDBDataset
from src.configs.v1_grid_classifier_config import Config as v1_model_config

# 定义一个本地的反归一化函数，用于PyTorch张量
def denormalize(tensor, mean_tensor, std_tensor):
    # 确保 std_tensor 不为零，避免除以零错误
    std_tensor = torch.where(std_tensor == 0, torch.ones_like(std_tensor), std_tensor)
    return tensor * std_tensor + mean_tensor

def visualize_sample_with_roi(
    lmdb_path: str,
    normalization_stats_path: str,
    dem_path: str,
    output_dir: str,
    sample_idx: int = 0
):
    """
    可视化LMDB数据集中的单个样本，显示历史轨迹在DEM背景上的位置，
    并为每个历史轨迹点显示其对应的局部环境ROI图像。
    """
    os.makedirs(output_dir, exist_ok=True)

    # 1. 加载归一化统计数据
    with open(normalization_stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    print(f"归一化统计数据加载完毕。键: {normalization_stats.keys()}")

    history_features_list = [
        'x', 'y', 'velocity_x', 'velocity_y', 'acceleration_x', 'acceleration_y',
        'heading_sin', 'heading_cos', 'curvature',
        'dem_agg', 'slope_agg', 'aspect_sin_agg', 'aspect_cos_agg',
        'lc_class_10', 'lc_class_20', 'lc_class_30', 'lc_class_40', 'lc_class_50',
        'lc_class_60', 'lc_class_80', 'lc_class_90', 'lc_class_255'
    ] # 确保与预处理时的特征列表一致

    history_mean_vec = torch.tensor([normalization_stats['history_mean'][f] for f in history_features_list], dtype=torch.float32)
    history_std_vec = torch.tensor([normalization_stats['history_std'][f] for f in history_features_list], dtype=torch.float32)

    dest_mean = torch.tensor([normalization_stats['target_x_mean'], normalization_stats['target_y_mean']], dtype=torch.float32)
    dest_std = torch.tensor([normalization_stats['target_x_std'], normalization_stats['target_y_std']], dtype=torch.float32)

    # 2. 加载数据集
    dataset = LMDBDataset(lmdb_path)
    if len(dataset) == 0:
        print(f"错误: 数据集 '{lmdb_path}' 中没有样本。")
        return

    sample = dataset[sample_idx]
    print(f"成功加载样本 {sample_idx}。样本键: {sample.keys()}")

    history_data_normalized = sample['history']
    environment_roi_data_normalized = sample['environment_roi']
    ground_truth_destination_normalized = sample['ground_truth_destination']
    ground_truth_trajectory_normalized = sample['ground_truth_trajectory']

    # 3. 反归一化轨迹和目的地
    history_x_y_normalized = history_data_normalized[:, 0:2] # 历史轨迹的前两个特征是x, y
    history_x_y_denormalized = denormalize(history_x_y_normalized, history_mean_vec[0:2], history_std_vec[0:2])

    # 获取历史轨迹点的数量
    num_history_points = history_x_y_denormalized.shape[0]

    ground_truth_destination_denormalized = denormalize(ground_truth_destination_normalized, dest_mean, dest_std)
    ground_truth_trajectory_denormalized = denormalize(ground_truth_trajectory_normalized, dest_mean, dest_std)

    # 4. 加载DEM背景图
    try:
        with rasterio.open(dem_path) as src:
            dem_image = src.read(1)
            dem_transform = src.transform
            dem_bounds = src.bounds
            dem_crs = src.crs
        print(f"成功加载DEM地图: {dem_path}")
    except Exception as e:
        print(f"错误: 无法加载DEM地图 {dem_path} - {e}")
        return

    # 将DEM图像的像素坐标转换为世界坐标
    # 注意：rasterio的bounds是(left, bottom, right, top)
    dem_extent = [dem_bounds.left, dem_bounds.right, dem_bounds.bottom, dem_bounds.top]

    # 获取DEM的像素分辨率
    pixel_width = dem_transform.a
    pixel_height = dem_transform.e # 通常为负值

    # 5. 创建可视化图
    fig, ax_main = plt.subplots(figsize=(12, 12)) # 调整图大小

    # 主轨迹图
    ax_main.imshow(dem_image, cmap='terrain', extent=dem_extent, origin='upper') # DEM背景，terrain配色
    ax_main.set_title("历史轨迹与局部环境ROI叠加 (DEM背景)", fontsize=22, fontname='Microsoft YaHei', color='black')
    ax_main.set_xlabel("X坐标 (米)", fontsize=20, fontname='Microsoft YaHei', color='black')
    ax_main.set_ylabel("Y坐标 (米)", fontsize=20, fontname='Microsoft YaHei', color='black')
    ax_main.tick_params(axis='both', which='major', labelsize=16, colors='black')
    
    # 绘制历史轨迹
    ax_main.plot(history_x_y_denormalized[:, 0].cpu().numpy(), history_x_y_denormalized[:, 1].cpu().numpy(),
                 color='blue', linestyle='-', marker='o', markersize=4, label='历史轨迹')
    # 绘制真实目的地
    ax_main.plot(ground_truth_destination_denormalized[0].cpu().numpy(), ground_truth_destination_denormalized[1].cpu().numpy(),
                 color='red', marker='X', markersize=8, label='真实目的地')
    ax_main.legend(fontsize=16, prop={'family':'Microsoft YaHei'})
    ax_main.grid(True)
    ax_main.set_aspect('equal', adjustable='box') # 保持坐标轴比例

    # 在主图上叠加绘制每个历史点的局部环境ROI
    roi_pixel_size = v1_model_config.ROI_PIXEL_SIZE # ROI是 9x9 像素
    half_roi_world_width = (roi_pixel_size * abs(pixel_width)) / 2
    half_roi_world_height = (roi_pixel_size * abs(pixel_height)) / 2

    for i in range(num_history_points):
        # 获取当前历史轨迹点的世界坐标
        px, py = history_x_y_denormalized[i, 0].item(), history_x_y_denormalized[i, 1].item()

        # 计算当前ROI在世界坐标系中的extent
        roi_extent = [
            px - half_roi_world_width,
            px + half_roi_world_width,
            py - half_roi_world_height,
            py + half_roi_world_height
        ]

        # 提取DEM通道的ROI
        roi_dem_channel = environment_roi_data_normalized[i, 0, :, :].cpu().numpy() # 假设DEM是第一个通道 (索引0)

        # 叠加绘制ROI图像，并设置透明度
        ax_main.imshow(roi_dem_channel, cmap='terrain', extent=roi_extent, origin='upper', alpha=0.6) # alpha 设为 0.6 半透明
        
        # 在ROI中心附近添加时间戳标签
        ax_main.text(px, py, f"T={i*5}s", color='black', fontsize=10, ha='center', va='bottom', weight='bold', fontname='Microsoft YaHei')

    # 自动调整布局，Rect 参数防止标题被裁切
    plt.tight_layout(rect=[0, 0, 1, 1])

    output_filename = os.path.join(output_dir, f"data_validation_trajectory_sample_overlay_{sample_idx}.png")
    plt.savefig(output_filename, dpi=300)
    print(f"可视化图像已保存到: {output_filename}")
    plt.close(fig) # 关闭图形，释放内存

if __name__ == '__main__':
    LMDB_PATH = v1_model_config.LMDB_PATH # 训练数据集
    NORMALIZATION_STATS_PATH = "data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/normalization_stats.pkl"
    DEM_PATH = v1_model_config.DEM_PATH # DEM地图路径
    OUTPUT_DIR = "cursor_drafts/" # 保存可视化图片的目录

    # 运行可视化
    visualize_sample_with_roi(LMDB_PATH, NORMALIZATION_STATS_PATH, DEM_PATH, OUTPUT_DIR, sample_idx=4) # 可以更改 sample_idx 查看不同样本 