# -*- coding: utf-8 -*-
"""
创建时间: 2025/07/24
功能: V2意图引导模型的训练脚本。本脚本用于加载数据、初始化IntentGuidedModel模型、
定义损失函数和优化器，并执行训练循环。它将意图预测作为轨迹预测的引导信息。
输入:
  - `configs/main_config.yaml`: 主配置文件。
  - `configs/models/v2_intent_guided_model.yaml`: V2模型特定配置文件。
  - `configs/data_preprocessing.yaml`: 数据预处理配置文件。
  - `data/processed_lmdb_...`: 预处理后的LMDB数据集。
输出:
  - 训练日志: 记录训练和验证损失等信息，输出到 `outputs/` 目录下的时间戳文件夹。
  - 模型检查点: 训练过程中验证损失最佳的模型权重，保存到 `checkpoints/` 目录下。
  - 最终模型和元数据: 训练结束时，最佳模型权重复制到 `models/` 目录下，并生成 `metadata.yaml`。
原理及处理方法:
  - **配置加载:** 使用 `src.utils.config_loader` 加载并合并所有相关配置，包括主配置、数据预处理配置和V2模型配置。
  - **数据加载:** 使用 `src.data.datasets.LMDBDataset` 和 `torch.utils.data.DataLoader` 加载LMDB格式的训练集和验证集。
  - **模型初始化:** 实例化 `src.models.v2_intent_guided.intent_guided_model.IntentGuidedModel`，并根据配置初始化其CNN、RNN、意图分类和轨迹回归头。
  - **损失函数:** 定义意图分类的交叉熵损失和轨迹回归的L1损失。总损失是两者的加权和。
  - **优化器与学习率调度器:** 使用Adam优化器，并可选择学习率调度器进行动态调整。
  - **训练循环:** 实现标准的训练和验证循环，包括前向传播、损失计算、反向传播、参数更新和模型评估。
  - **早停机制:** 根据验证损失实现早停，防止过拟合，提高训练效率。
  - **模型保存:** 保存验证损失最佳的模型检查点。
  - **日志记录:** 使用 `tensorboard.SummaryWriter` 记录训练过程中的各种指标，便于可视化分析。
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from pathlib import Path
import sys
import os
from datetime import datetime
import yaml
from omegaconf import OmegaConf # 导入 OmegaConf
import torch.nn.functional as F # 导入 F 用于交叉熵损失

# 确保src目录在python path中
project_root = Path(__file__).resolve().parents[2]
if str(project_root) not in sys.path:
         sys.path.append(str(project_root))

from src.data.datasets import LMDBDataset
from src.models.v2_intent_guided.intent_guided_model import IntentGuidedModel # 导入V2意图引导模型
from src.engine.trainer import Trainer # 复用Trainer
from src.utils.config_loader import load_config # 导入新的配置加载器
from src.engine.loss import autoregressive_mdn_nll_loss # 导入 GMM NLL 损失

def main(config):
    """
    V2意图引导模型的主训练函数
    """
    print("--- 开始V2意图引导模型训练流程 ---")

    # 创建带有时间戳的运行目录，使用 Path 对象
    current_time_str = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir_path = Path(config.training.log_dir) / f"v2_intent_guided_model_{current_time_str}"
    log_dir_path.mkdir(parents=True, exist_ok=True)
    writer = SummaryWriter(str(log_dir_path))

    # 1. 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 2. 初始化数据集和数据加载器
    print("正在初始化数据集...")
    train_dataset = LMDBDataset(
        config=config, # 传入config对象
        lmdb_type='train' # 指定为训练集
    )
    val_dataset = LMDBDataset(
        config=config, # 传入config对象
        lmdb_type='val' # 指定为验证集
    )
    print(f"  -> 训练集样本数: {len(train_dataset)}")
    print(f"  -> 验证集样本数: {len(val_dataset)}")

    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=config.training.get('num_workers', config.data_preprocessing.num_processing_workers),
        pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=config.training.get('num_workers', config.data_preprocessing.num_processing_workers),
        pin_memory=True
    )
    print("数据加载器创建完毕。")

    # 3. 初始化模型
    print("正在初始化模型...")
    import pickle
    stats_path = Path(config.data_preprocessing.output_path) / "normalization_stats.pkl"
    if not stats_path.exists():
        print(f"错误: 归一化统计文件未找到: {stats_path}。请先运行数据预处理脚本。")
        sys.exit(1)
    with open(stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    
    # 从归一化统计中获取目的地均值和标准差
    dest_mean = torch.tensor([normalization_stats['target_x_mean'], normalization_stats['target_y_mean']], dtype=torch.float32).to(device) 
    dest_std = torch.tensor([normalization_stats['target_x_std'], normalization_stats['target_y_std']], dtype=torch.float32).to(device)  

    # 初始化V2意图引导模型
    model = IntentGuidedModel(config, dest_mean, dest_std).to(device)
    print("模型 'IntentGuidedModel' 初始化完毕。")

    # 4. 初始化优化器
    # 损失函数
    # 对于意图引导模型，损失是意图分类损失（交叉熵）和轨迹MDN负对数似然损失的组合
    # 这里将 criterion 定义为一个字典
    criterion = {
        'trajectory_nll': autoregressive_mdn_nll_loss,
        'intent_ce': F.cross_entropy # 交叉熵用于意图分类
    }

    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=config.training.learning_rate)

    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5, factor=0.5, verbose=True)
    print("优化器和学习率调度器初始化完毕。")

    # 5. 初始化并运行训练器
    print("\n--- 开始训练 ---")
    trainer = Trainer(
        model=model,
        criterion=criterion, # 使用包含GMM NLL和交叉熵的字典
        optimizer=optimizer,
        device=device,
        train_loader=train_loader,
        val_loader=val_loader,
        writer=writer,
        config=config,
        normalization_stats=normalization_stats # 确保传递归一化统计数据
    )
    
    # 手动实现训练循环，包括学习率调度器和早停逻辑
    num_epochs = config.training.num_epochs
    best_val_loss = float('inf')
    epochs_no_improve = 0
    early_stopping_patience = config.training.get('early_stopping_patience', 10)
    model_save_path = Path(config.training.checkpoints_root_dir) / f"v2_intent_guided_model_{current_time_str}" / 'best_model.pth'
    model_save_path.parent.mkdir(parents=True, exist_ok=True)

    for epoch in range(num_epochs):
        print(f"Epoch {epoch+1}/{num_epochs}")
        
        train_loss = trainer.train_one_epoch(epoch)
        val_loss = trainer.validate_one_epoch(epoch)

        if scheduler:
            scheduler.step(val_loss)

        if val_loss < best_val_loss:
            best_val_loss = val_loss
            epochs_no_improve = 0
            torch.save(model.state_dict(), model_save_path)
            print(f"保存最佳模型到 {model_save_path} (验证损失: {best_val_loss:.4f})")
        else:
            epochs_no_improve += 1
            print(f"验证损失未改善，计数: {epochs_no_improve}/{early_stopping_patience}")
            if epochs_no_improve >= early_stopping_patience:
                print(f"早停触发，在 Epoch {epoch+1} 停止训练。")
                break

    writer.close()
    print("--- V2意图引导模型训练完成！---")

    # 训练结束后将最终模型（最佳模型）复制到 models/ 目录下并生成 metadata.yaml
    final_model_dir_name = f"v2_intent_guided_model_{current_time_str}"
    final_model_dir = Path('models') / final_model_dir_name
    final_model_dir.mkdir(parents=True, exist_ok=True)
    final_model_path = final_model_dir / 'best_model.pth'
    
    if model_save_path.exists():
        torch.save(model.state_dict(), final_model_path)
        print(f"最终最佳模型复制到: {final_model_path}")
    else:
        print("警告: 未找到最佳模型检查点，无法复制最终模型。")

    metadata_path = final_model_dir / 'metadata.yaml'
    with open(metadata_path, 'w') as f:
        metadata = {
            'model_version': '2.0.0', # V2模型版本
            'model_type': 'IntentGuidedModel',
            'data_version': config.data_preprocessing.output_path.split('/')[-1],
            'training_config': OmegaConf.to_container(config, resolve=True),
            'best_val_loss': float(f'{best_val_loss:.4f}'),
            'trained_epochs': epoch + 1,
            'git_commit_hash': os.popen('git rev-parse HEAD').read().strip(),
            'author': 'AI Assistant',
            'creation_time': datetime.now().isoformat()
        }
        yaml.dump(metadata, f, default_flow_style=False)
    print(f"模型元数据保存到: {metadata_path}")

if __name__ == '__main__':
    # 确保路径设置正确
    project_root_main = Path(__file__).resolve().parents[2]
    if str(project_root_main) not in sys.path:
         sys.path.append(str(project_root_main))
    
    # 在main函数外部加载配置
    config_path = 'configs/main_config.yaml'
    model_config_path = 'configs/models/v2_intent_guided_model.yaml' # V2模型配置文件路径
    data_preprocessing_config_path = 'configs/data_preprocessing.yaml'
    default_config_path = 'configs/default.yaml'
    
    # 加载并合并所有相关配置
    config = load_config(default_config_path, config_path, data_preprocessing_config_path, model_config_path)

    main(config) # 将 config 作为参数传递给 main 函数 