# -*- coding: utf-8 -*-
"""
创建时间: 2025/07/25
功能: 使用Optuna自动调优PECNet模型的超参数。
输入: 
    - LMDB数据集 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train)
    - LMDB验证集 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/val)
    - 归一化统计文件 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/normalization_stats.pkl)
    - 配置文件 (configs/main_config.yaml, configs/models/pecnet_model.yaml)
输出: 
    - Optuna研究结果，包括最佳超参数和对应的验证损失。
    - （可选）保存最佳模型检查点和TensorBoard日志。
原理:
    1. 定义一个目标函数，该函数接收一组超参数，并执行模型的训练和验证。
    2. 使用Optuna的建议方法在预定义范围内探索超参数。
    3. 利用Optuna的采样器（如TPESampler）和剪枝器（如MedianPruner）来高效地搜索超参数空间。
    4. 目标函数返回验证损失，Optuna的目标是最小化这个损失。
处理方法:
    - 动态加载配置并覆盖Optuna建议的超参数。
    - 初始化LMDBDataset和DataLoader。
    - 初始化PECNet模型、损失函数和优化器。
    - 使用Trainer类进行训练和验证。
    - 记录TensorBoard日志以便可视化训练过程。
作者: AI Assistant
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import time
import pickle
import logging
from datetime import datetime
from tqdm import tqdm
import argparse
from pathlib import Path

import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.pecnet_model.pecnet_model import PECNet
from src.engine.trainer import Trainer
from omegaconf import OmegaConf

import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner

# 配置日志 (保持与训练脚本一致)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout) # 将日志输出到控制台
    ]
)

def objective(trial: optuna.Trial):
    logging.info(f"--- 开始Optuna试验: {trial.number} ---")

    # 1. 加载基础配置
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, default='configs/main_config.yaml')
    parser.add_argument('--model_config_name', type=str, default='pecnet_model.yaml')
    args, _ = parser.parse_known_args()
    base_config = load_config(args.config, os.path.join('configs', 'models', args.model_config_name))

    # 2. 建议超参数并覆盖配置
    config = base_config.copy()
    config.training.learning_rate = trial.suggest_float("learning_rate", 8e-5, 1e-3, log=True) # 学习率下限从 1e-5 提高到 8e-5
    config.training.kl_loss_weight = trial.suggest_float("kl_loss_weight", 1e-4, 1e-2, log=True) # 0.0001 - 0.01
    config.training.endpoint_reconstruction_weight = trial.suggest_float("endpoint_reconstruction_weight", 1.0, 1.5) # 终点权重范围缩小到 1.0 - 1.5
    config.model.dropout_prob = trial.suggest_float("dropout_prob", 0.3, 0.4) # Dropout 上限从 0.6 降低到 0.4
    config.model.hidden_dim = trial.suggest_categorical("hidden_dim", [256, 512]) # 移除128，只选择256或512
    config.model.num_layers = trial.suggest_int("num_layers", 2, 4)

    logging.info("当前试验配置如下:\n" + OmegaConf.to_yaml(config))

    # 3. 数据加载
    train_dataset = LMDBDataset(config, lmdb_type='train')
    val_dataset = LMDBDataset(config, lmdb_type='val')

    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=config.training.num_workers,
        pin_memory=True,
        collate_fn=LMDBDataset.collate_fn
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=config.training.num_workers,
        pin_memory=True,
        collate_fn=LMDBDataset.collate_fn
    )
    logging.info(f"训练集样本数: {len(train_dataset)}, 验证集样本数: {len(val_dataset)}")

    # 4. 模型初始化
    model = PECNet(
        history_feature_dim=config.model.history_feature_dim,
        endpoint_dim=config.model.endpoint_dim,
        env_feature_dim=config.model.env_feature_dim,
        output_len=config.model.output_len,
        hidden_dim=config.model.hidden_dim,
        dropout_prob=config.model.dropout_prob,
        num_layers=config.model.num_layers
    )
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    logging.info(f"使用设备: {device}")

    # 5. 定义损失函数
    reconstruction_criterion = torch.nn.MSELoss(reduction='none')
    def kl_divergence_loss(mean, logvar):
        return -0.5 * torch.sum(1 + logvar - mean.pow(2) - logvar.exp())

    criterions = {
        'reconstruction': reconstruction_criterion,
        'kl_divergence': kl_divergence_loss
    }

    # 6. 优化器和学习率调度
    optimizer = optim.AdamW(model.parameters(), lr=config.training.learning_rate, weight_decay=config.training.get('weight_decay', 0.0))
    scheduler = None
    if config.training.scheduler.enabled:
        scheduler_params = config.training.scheduler_params if config.training.scheduler_params is not None else {}
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 
            mode=scheduler_params.get('mode', 'min'), 
            factor=scheduler_params.get('factor', 0.5), 
            patience=scheduler_params.get('patience', 10), 
            verbose=scheduler_params.get('verbose', True)
        )

    # 7. TensorBoard writer (每个试验有独立的日志目录)
    writer = SummaryWriter(log_dir=os.path.join(config.training.output_root_dir, config.training.log_dir.split('/')[-1]))

    # 8. 加载归一化统计数据
    stats_path = config.data_params.stats_path_v2
    if not os.path.exists(stats_path):
        logging.error(f"错误: 归一化统计文件未找到: {stats_path}。请先运行数据预处理脚本。")
        sys.exit(1)
    with open(stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    logging.info("归一化统计数据加载成功。")

    # 9. 初始化Trainer
    trainer = Trainer(
        model=model,
        criterion=criterions,
        optimizer=optimizer,
        device=device,
        train_loader=train_loader,
        val_loader=val_loader,
        writer=writer,
        config=config,
        normalization_stats=normalization_stats,
        current_trial_number=trial.number
    )

    # 10. 训练循环
    for epoch in range(1, config.training.num_epochs + 1):
        train_loss = trainer.train_one_epoch(epoch)
        val_loss = trainer.validate_one_epoch(epoch)

        if scheduler:
            scheduler.step(val_loss)

        # Optuna 剪枝
        trial.report(val_loss, epoch)
        if trial.should_prune():
            logging.info(f"试验 {trial.number} 在 Epoch {epoch} 被剪枝。")
            raise optuna.exceptions.TrialPruned()

    writer.close()
    logging.info(f"--- 试验 {trial.number} 完成，最佳验证损失: {val_loss:.4f} ---")
    return val_loss

if __name__ == '__main__':
    logging.info("--- 开始PECNet超参数调优流程 (使用Optuna) ---")

    sampler = TPESampler(seed=42)
    pruner = MedianPruner(n_startup_trials=5, n_warmup_steps=10, interval_steps=1)

    study = optuna.create_study(
        direction="minimize", 
        sampler=sampler, 
        pruner=pruner, 
        study_name="PECNet_Hyperparameter_Tuning",
        storage="sqlite:///pecnet_optuna_study.db",
        load_if_exists=True
    )

    logging.info(f"已加载或创建Optuna研究：{study.study_name}，当前已完成 {len(study.trials)} 个试验。")

    try:
        study.optimize(
            objective,
            n_trials=50,
            timeout=None,
            show_progress_bar=True
        )
    except KeyboardInterrupt:
        logging.info("Optuna优化被用户中断。")

    logging.info("--- 超参数调优完成 ---")
    logging.info("最佳试验结果:")
    best_trial = study.best_trial
    logging.info(f"  Value (Best Validation Loss): {best_trial.value:.4f}")
    logging.info("  Params:")
    for key, value in best_trial.params.items():
        logging.info(f"    {key}: {value}")

    logging.info(f"所有试验的日志可在 TensorBoard 中查看：runs/track_model_v2/PECNet_tuning_trial_*") 