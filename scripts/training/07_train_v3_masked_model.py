#!/usr/bin/env python3
"""
创建时间: 2025-07-29
功能: 训练V3版本的带掩码的自回归轨迹预测模型 (MaskedAutoregressiveModel)
输入:
  - configs/main_config.yaml 和 configs/models/v3_masked_autoregressive.yaml 中定义的配置
  - LMDB训练集和验证集：包含预处理后的轨迹、掩码和环境ROI数据
输出:
  - 训练过程中的日志信息
  - 模型检查点文件
  - TensorBoard日志文件
原理:
  - V3模型专门为观测空窗期和观测窗口交替场景设计
  - 使用掩码机制处理间歇性观测数据
  - 结合CNN环境编码器和GRU历史编码器
  - 自回归解码器生成未来轨迹
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from pathlib import Path
import pickle
import logging
from datetime import datetime

# 导入自定义模块
from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.v3_masked_autoregressive.model import MaskedAutoregressiveModel
from src.engine.trainer import Trainer

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('v3_training.log'),
            logging.StreamHandler()
        ]
    )

def main():
    # 设置日志
    setup_logging()
    logging.info("开始V3模型训练 - 使用批次大小32")

    # 1. 加载配置
    config = load_config('configs/main_config.yaml', 'configs/models/v3_masked_autoregressive.yaml')
    logging.info("配置加载完成")

    # 2. 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"使用设备: {device}")

    # 3. 加载数据集
    logging.info("正在加载数据集...")

    # 加载归一化统计数据
    data_dir = Path(config.data_preprocessing.output_path)
    stats_path = data_dir / 'normalization_stats.pkl'

    if not stats_path.exists():
        logging.error(f"归一化统计文件未找到: {stats_path}")
        logging.error("请先运行数据预处理脚本")
        sys.exit(1)

    with open(stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    logging.info("归一化统计数据加载完成")

    # 创建数据集
    train_dataset = LMDBDataset(
        config=config,
        lmdb_type='train',
        return_mask=True  # V3模型需要掩码
    )
    val_dataset = LMDBDataset(
        config=config,
        lmdb_type='val',
        return_mask=True  # V3模型需要掩码
    )

    logging.info(f"训练集样本数: {len(train_dataset)}")
    logging.info(f"验证集样本数: {len(val_dataset)}")

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=config.training.get('num_workers', 4),
        pin_memory=True,
        collate_fn=LMDBDataset.collate_fn
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=config.training.get('num_workers', 4),
        pin_memory=True,
        collate_fn=LMDBDataset.collate_fn
    )

    logging.info("数据加载器创建完成")

    # 4. 初始化模型
    logging.info("正在初始化V3模型...")
    model = MaskedAutoregressiveModel(config.model, normalization_stats).to(device)
    logging.info("V3模型初始化完成")

    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logging.info(f"模型总参数数: {total_params:,}")
    logging.info(f"可训练参数数: {trainable_params:,}")

    # 5. 初始化优化器和调度器
    weight_decay = getattr(config.training, 'weight_decay', 0.0)
    optimizer = optim.Adam(
        model.parameters(),
        lr=config.training.learning_rate,
        weight_decay=weight_decay
    )
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, 'min', patience=10, factor=0.5, verbose=True  # 增加patience
    )
    logging.info("优化器和调度器初始化完成")

    # 6. 设置TensorBoard
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = Path("runs") / f"v3_masked_autoregressive_{timestamp}"
    writer = SummaryWriter(log_dir)
    logging.info(f"TensorBoard日志目录: {log_dir}")

    # 7. 初始化训练器
    trainer = Trainer(
        model=model,
        criterion=nn.MSELoss(),  # V3模型使用MSE损失
        optimizer=optimizer,
        device=device,
        train_loader=train_loader,
        val_loader=val_loader,
        writer=writer,
        config=config,
        normalization_stats=normalization_stats
    )
    logging.info("训练器初始化完成")

    # 8. 开始训练
    logging.info("开始训练V3模型...")

    num_epochs = config.training.num_epochs
    best_val_loss = float('inf')
    patience = config.training.get('early_stopping_patience', 10)
    epochs_no_improve = 0

    # 创建检查点目录
    checkpoint_dir = Path("checkpoints") / "v3_masked_autoregressive"
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    model_save_path = checkpoint_dir / f"best_model_{timestamp}.pth"

    for epoch in range(num_epochs):
        logging.info(f"Epoch {epoch+1}/{num_epochs}")

        # 训练一个epoch
        train_loss = trainer.train_one_epoch(epoch)

        # 验证一个epoch
        val_loss = trainer.validate_one_epoch(epoch)

        # 学习率调度
        scheduler.step(val_loss)

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            epochs_no_improve = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
                'config': config,
                'normalization_stats': normalization_stats
            }, model_save_path)
            logging.info(f"保存最佳模型到 {model_save_path} (验证损失: {best_val_loss:.6f})")
        else:
            epochs_no_improve += 1
            logging.info(f"验证损失未改善，计数: {epochs_no_improve}/{patience}")

            if epochs_no_improve >= patience:
                logging.info(f"早停触发，在 Epoch {epoch+1} 停止训练")
                break

    # 9. 训练完成
    logging.info("V3模型训练完成！")
    logging.info(f"最佳验证损失: {best_val_loss:.6f}")
    logging.info(f"模型保存路径: {model_save_path}")
    logging.info(f"TensorBoard日志: {log_dir}")

    writer.close()

if __name__ == "__main__":
    main()