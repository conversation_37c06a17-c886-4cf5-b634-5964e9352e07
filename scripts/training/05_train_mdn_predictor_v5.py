# -*- coding: utf-8 -*-
"""
创建时间: 2024-07-21 01:15:30+08:00
功能: MDN轨迹预测器V5训练脚本 (纯点级特征 + 注意力 + 相对目标编码)
输入: 
    - LMDB数据集 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train)
    - LMDB验证集 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/val)
    - 归一化统计文件 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/normalization_stats.pkl)
    - 配置文件 (configs/default.yaml)
输出: 
    - 模型检查点 (checkpoints/mdn_predictor_v5_YYYYMMDD_HHMMSS/)
    - TensorBoard日志 (outputs/runs/mdn_predictor_v5_YYYYMMDD_HHMMSS/)
    - 最终模型 (models/mdn_predictor_5.0.0_YYYYMMDD/)
原理:
    1. 加载配置和归一化统计数据。
    2. 初始化LMDB数据集和DataLoader。
    3. 定义MDN_Predictor_V5模型架构 (不使用环境ROI)。
    4. 定义损失函数 (MDN NLL Loss) 和优化器。
    5. 实现训练循环，包括前向传播、损失计算、反向传播和参数更新。
    6. 实现验证循环，评估模型性能。
    7. 使用TensorBoard记录训练指标和损失。
    8. 保存最佳模型检查点，并记录训练总结。
处理方法:
    - 使用PyTorch框架构建模型和训练流程。
    - LMDBDataset用于高效数据加载。
    - 动态加载配置，支持命令行参数覆盖。
    - 采用教师强制进行自回归训练。
    - 使用Adam优化器和学习率调度器 (ReduceLROnPlateau)。
    - 通过 early stopping 防止过拟合。
作者: 郑明灿
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import time
import pickle
import logging
from datetime import datetime
from tqdm import tqdm
import argparse # 导入 argparse

# 将项目根目录添加到Python路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.mdn_predictor_v5 import MDN_Predictor_V5 # 导入V5模型
from src.engine.loss import CustomLoss, autoregressive_mdn_nll_loss, single_point_mdn_nll_loss # 导入MDN损失函数
from src.engine.trainer import Trainer # 导入Trainer类
from omegaconf import OmegaConf # 导入OmegaConf，用于打印omegaconf对象

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')

def main():
    logging.info("--- 开始V5 MDN模型训练流程 ---")

    # 1. 解析命令行参数
    parser = argparse.ArgumentParser(description="MDN轨迹预测器V5训练脚本。")
    parser.add_argument('--config', type=str, default='configs/main_config.yaml',
                        help='主配置文件路径。')
    parser.add_argument('--model_config_name', type=str, required=True,
                        help='模型特定配置文件的名称（位于configs/models/下）。')
    args = parser.parse_args()

    # 2. 加载和合并配置
    model_config_path = os.path.join('configs', 'models', args.model_config_name) # 构建模型配置文件的完整路径
    config = load_config(args.config, model_config_path) # 加载主配置和模型特定配置
    import yaml # 导入yaml，用于打印omegaconf对象
    logging.info("合并后的配置如下:\n" + OmegaConf.to_yaml(config))
    logging.info(f"加载配置完成。数据集路径: {config['data_params']['lmdb_path']}") # 修正数据集路径访问方式

    # 3. 定义路径
    output_root_dir = config['training']['output_root_dir']
    checkpoints_root_dir = config['training']['checkpoints_root_dir']

    # 创建带有时间戳的运行目录
    current_time_str = datetime.now().strftime('%Y%m%d_%H%M%S')
    run_name = f"mdn_predictor_v5_{current_time_str}"
    log_dir = os.path.join(output_root_dir, 'runs', run_name)
    checkpoint_dir = os.path.join(checkpoints_root_dir, run_name)

    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(checkpoint_dir, exist_ok=True)

    writer = SummaryWriter(log_dir)
    logging.info(f"TensorBoard 日志目录: {log_dir}")
    logging.info(f"模型检查点目录: {checkpoint_dir}")

    # 4. 加载归一化统计数据
    stats_path = config['data_params']['stats_path_v2'] # 从模型特定配置中获取统计文件路径
    if not os.path.exists(stats_path):
        logging.error(f"错误: 归一化统计文件未找到: {stats_path}。请先运行数据预处理脚本。")
        sys.exit(1)
    with open(stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    logging.info("归一化统计数据加载成功。")

    # 5. 初始化数据集和DataLoader
    # train_lmdb_path = os.path.join(config['data_params']['lmdb_path'], "train") # 移除此行
    # val_lmdb_path = os.path.join(config['data_params']['lmdb_path'], "val") # 移除此行

    # if not os.path.exists(train_lmdb_path) or not os.path.exists(val_lmdb_path):
    #     logging.error(f"错误: LMDB数据集目录不存在。训练: {train_lmdb_path}, 验证: {val_lmdb_path}")
    #     logging.error("请确保数据预处理脚本已成功运行并生成了数据集。")
    #     sys.exit(1)

    train_dataset = LMDBDataset(config, lmdb_type='train') # 传入完整的config对象和lmdb_type
    val_dataset = LMDBDataset(config, lmdb_type='val')   # 传入完整的config对象和lmdb_type

    train_loader = DataLoader(train_dataset, batch_size=config['training']['batch_size'], shuffle=True, num_workers=config['training']['num_workers'], collate_fn=LMDBDataset.collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=config['training']['batch_size'], shuffle=False, num_workers=config['training']['num_workers'], collate_fn=LMDBDataset.collate_fn)
    logging.info(f"训练集样本数: {len(train_dataset)}, 验证集样本数: {len(val_dataset)}")
    logging.info(f"训练DataLoader batch size: {config['training']['batch_size']}, workers: {config['training']['num_workers']}")

    # 6. 定义模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"使用设备: {device}")
    
    # history_feature_dim = len(config['modeling']['trajectory_features']) # 从 modeling 部分获取特征列表
    history_feature_dim = len(config['data_preprocessing']['history_features']) # 从 data_preprocessing 部分获取历史特征列表

    model = MDN_Predictor_V5(
        history_feature_dim=history_feature_dim,
        rnn_hidden_size=config['modeling']['rnn_hidden_size'],
        num_layers=config['modeling']['num_rnn_layers'], # 假设有 num_rnn_layers 参数
        num_mixture=config['modeling']['num_mixture'],
        goal_embedding_dim=config['modeling']['goal_embedding_dim'],
        dropout_prob=config['modeling']['dropout_prob'], # 假设有 dropout_prob 参数
        normalization_stats=normalization_stats # 传递归一化统计数据给模型
    ).to(device)
    logging.info("MDN_Predictor_V5 模型定义完成。")

    # 6. 定义损失函数和优化器
    # 对于MDN模型，使用字典来存储轨迹和目的地的MDN损失函数
    mdn_criterions = {
        'trajectory_nll': autoregressive_mdn_nll_loss,
        'destination_nll': single_point_mdn_nll_loss
    }
    optimizer = optim.Adam(model.parameters(), lr=config['training']['learning_rate'])
    
    # 7. 训练循环
    num_epochs = config['training']['num_epochs']
    
    # 实例化Trainer
    trainer = Trainer(
        model=model,
        criterion=mdn_criterions, # 传入MDN损失函数字典
        optimizer=optimizer,
        device=device,
        train_loader=train_loader,
        val_loader=val_loader,
        writer=writer,
        normalization_stats=normalization_stats, # 传递归一化统计数据给Trainer
        config=config # 传递完整配置给Trainer，以便其内部访问相关参数
    )

    logging.info(f"开始训练 {num_epochs} 个 epoch...")
    best_val_loss = float('inf')
    epochs_no_improve = 0
    early_stopping_patience = config['training'].get('early_stopping_patience', 10)
    model_save_path = os.path.join(checkpoint_dir, f'best_model.pth') # 最佳模型保存路径

    for epoch in range(num_epochs):
        logging.info(f"Epoch {epoch+1}/{num_epochs}")
        
        # 教师强制比率现在在Trainer内部处理，这里不再需要
        # teacher_forcing_ratio = max(0.0, config['training'].get('teacher_forcing_ratio_start', 1.0) - 
        #                             epoch * config['training'].get('teacher_forcing_ratio_decay', 0.05))
        # logging.info(f"Epoch {epoch+1} 教师强制比率: {teacher_forcing_ratio:.4f}")

        # 训练一个epoch
        train_loss = trainer.train_one_epoch(epoch) # 移除 model_type 参数
        
        # 验证一个epoch
        val_loss = trainer.validate_one_epoch(epoch) # 移除 model_type 参数

        writer.add_scalars('Loss', {'Train': train_loss, 'Validation': val_loss}, epoch)

        # 保存最佳模型逻辑
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            epochs_no_improve = 0
            # 实际保存模型的状态字典
            torch.save(model.state_dict(), model_save_path)
            logging.info(f"保存最佳模型到 {model_save_path} (验证损失: {best_val_loss:.4f})")
        else:
            epochs_no_improve += 1
            logging.info(f"验证损失未改善，计数: {epochs_no_improve}/{early_stopping_patience}")
            if epochs_no_improve >= early_stopping_patience:
                logging.info(f"早停触发，在 Epoch {epoch+1} 停止训练。")
                break

    writer.close()
    logging.info("--- MDN模型训练完成！---")

    # 按照记忆规则，训练结束后将最终模型（最佳模型）复制到 models/ 目录下并生成 metadata.yaml
    final_model_dir_name = f"mdn_predictor_5.0.0_{current_time_str}"
    final_model_dir = os.path.join('models', final_model_dir_name)
    os.makedirs(final_model_dir, exist_ok=True)
    final_model_path = os.path.join(final_model_dir, 'best_model.pth') # 复制最佳模型
    
    # 复制最佳模型文件
    if os.path.exists(model_save_path):
        torch.save(model.state_dict(), final_model_path) # 直接保存当前加载的最佳模型状态
        logging.info(f"最终最佳模型复制到: {final_model_path}")
    else:
        logging.warning("警告: 未找到最佳模型检查点，无法复制最终模型。")

    # 生成 metadata.yaml 文件
    metadata_path = os.path.join(final_model_dir, 'metadata.yaml')
    with open(metadata_path, 'w') as f:
        import yaml
        metadata = {
            'model_version': '5.0.0',
            'model_type': 'MDN_Predictor_V5',
            'data_version': config['data_params']['lmdb_path'].split('/')[-1], # 从模型特定数据路径提取版本
            'training_config': config,
            'best_val_loss': float(f'{best_val_loss:.4f}'), # 记录最终最佳验证损失
            'trained_epochs': epoch + 1, # 记录实际训练的epoch数量
            'git_commit_hash': os.popen('git rev-parse HEAD').read().strip(), # 获取当前git commit hash
            'author': '郑明灿',
            'creation_time': datetime.now().isoformat()
        }
        yaml.dump(metadata, f, default_flow_style=False)
    logging.info(f"模型元数据保存到: {metadata_path}")

if __name__ == '__main__':
    # TODO: 完善 configs/default.yaml 中的 training 和 modeling 配置
    main() 