#!/usr/bin/env python3
"""
V6模型训练脚本 - 充分利用所有特征
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import logging
import time
from tqdm import tqdm

from src.data.datasets import LMDBDataset
from src.models.trajectory_predictor_v6 import TrajectoryPredictorV6
from src.utils.config_loader import load_config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_datasets(config):
    """创建数据集"""
    # 训练集
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    
    # 验证集
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    
    logger.info(f"训练集样本数: {len(train_dataset)}")
    logger.info(f"验证集样本数: {len(val_dataset)}")
    
    return train_dataset, val_dataset

def create_data_loaders(train_dataset, val_dataset, config):
    """创建数据加载器"""
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True,
        collate_fn=LMDBDataset.collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        drop_last=False,
        collate_fn=LMDBDataset.collate_fn
    )
    
    return train_loader, val_loader

def train_epoch(model, train_loader, optimizer, criterion, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    num_batches = len(train_loader)
    
    progress_bar = tqdm(train_loader, desc=f'训练轮次 {epoch}')
    
    for batch_idx, batch in enumerate(progress_bar):
        # 跳过None批次
        if batch is None:
            continue

        # 准备数据
        history_features = batch['history_features'].to(device)
        history_mask = batch['history_mask'].to(device)
        ground_truth_trajectory = batch['ground_truth_trajectory'].to(device)
        ground_truth_destination = batch['ground_truth_destination'].to(device)
        environment_roi = batch['environment_roi'].to(device)
        
        # 前向传播
        optimizer.zero_grad()
        
        predicted_trajectory = model(
            history_features=history_features,
            history_mask=history_mask,
            ground_truth_destination=ground_truth_destination,
            environment_roi=environment_roi
        )
        
        # 计算损失
        loss = criterion(predicted_trajectory, ground_truth_trajectory)
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 更新进度条
        progress_bar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Avg Loss': f'{total_loss/(batch_idx+1):.4f}'
        })
    
    return total_loss / num_batches

def validate_epoch(model, val_loader, criterion, device):
    """验证一个epoch"""
    model.eval()
    total_loss = 0
    total_ade = 0
    total_fde = 0
    num_samples = 0
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc='验证'):
            # 跳过None批次
            if batch is None:
                continue

            # 准备数据
            history_features = batch['history_features'].to(device)
            history_mask = batch['history_mask'].to(device)
            ground_truth_trajectory = batch['ground_truth_trajectory'].to(device)
            ground_truth_destination = batch['ground_truth_destination'].to(device)
            environment_roi = batch['environment_roi'].to(device)
            
            # 前向传播
            predicted_trajectory = model(
                history_features=history_features,
                history_mask=history_mask,
                ground_truth_destination=ground_truth_destination,
                environment_roi=environment_roi
            )
            
            # 计算损失
            loss = criterion(predicted_trajectory, ground_truth_trajectory)
            total_loss += loss.item()
            
            # 计算ADE和FDE
            pred_np = predicted_trajectory.cpu().numpy()
            gt_np = ground_truth_trajectory.cpu().numpy()
            
            batch_size = pred_np.shape[0]
            for i in range(batch_size):
                # ADE: 平均位移误差
                distances = np.sqrt(np.sum((pred_np[i] - gt_np[i]) ** 2, axis=1))
                ade = np.mean(distances)
                total_ade += ade
                
                # FDE: 最终位移误差
                fde = np.sqrt(np.sum((pred_np[i][-1] - gt_np[i][-1]) ** 2))
                total_fde += fde
                
                num_samples += 1
    
    avg_loss = total_loss / len(val_loader)
    avg_ade = total_ade / num_samples
    avg_fde = total_fde / num_samples
    
    return avg_loss, avg_ade, avg_fde

def save_checkpoint(model, optimizer, scheduler, epoch, train_loss, val_loss, val_ade, val_fde, is_best=False):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'train_loss': train_loss,
        'val_loss': val_loss,
        'val_ade': val_ade,
        'val_fde': val_fde
    }
    
    # 创建模型目录
    model_dir = Path("models")
    model_dir.mkdir(exist_ok=True)
    
    # 保存当前epoch
    checkpoint_path = model_dir / f"trajectory_predictor_v6_epoch_{epoch}.pth"
    torch.save(checkpoint, checkpoint_path)
    
    # 保存最佳模型
    if is_best:
        best_path = model_dir / "trajectory_predictor_v6_best.pth"
        torch.save(checkpoint, best_path)
        logger.info(f"保存最佳模型: {best_path}")

def main():
    """主函数"""
    # 配置
    config = load_config('configs/main_config.yaml')
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建模型
    model = TrajectoryPredictorV6(config).to(device)
    
    # 打印模型信息
    model_info = model.get_model_info()
    logger.info("=== V6模型信息 ===")
    for key, value in model_info.items():
        logger.info(f"{key}: {value}")
    
    # 创建数据集和数据加载器
    train_dataset, val_dataset = create_datasets(config)
    train_loader, val_loader = create_data_loaders(train_dataset, val_dataset, config)
    
    # 优化器和调度器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config.training.learning_rate,
        weight_decay=config.training.weight_decay
    )
    
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, verbose=True
    )
    
    # 损失函数
    criterion = nn.MSELoss()
    
    # 训练循环
    logger.info("=== 开始训练V6模型 ===")
    best_val_loss = float('inf')
    
    for epoch in range(1, config.training.num_epochs + 1):
        # 训练
        train_loss = train_epoch(model, train_loader, optimizer, criterion, device, epoch)
        
        # 验证
        val_loss, val_ade, val_fde = validate_epoch(model, val_loader, criterion, device)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 记录结果
        logger.info(f"轮次 {epoch}/{config.training.num_epochs}")
        logger.info(f"  训练损失: {train_loss:.4f}")
        logger.info(f"  验证损失: {val_loss:.4f}")
        logger.info(f"  验证ADE: {val_ade:.4f}")
        logger.info(f"  验证FDE: {val_fde:.4f}")
        logger.info(f"  学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存检查点
        is_best = val_loss < best_val_loss
        if is_best:
            best_val_loss = val_loss
        
        if epoch % 10 == 0 or is_best:
            save_checkpoint(model, optimizer, scheduler, epoch, train_loss, val_loss, val_ade, val_fde, is_best)
    
    logger.info("=== V6模型训练完成 ===")
    logger.info(f"最佳验证损失: {best_val_loss:.4f}")

if __name__ == "__main__":
    import numpy as np
    main()
