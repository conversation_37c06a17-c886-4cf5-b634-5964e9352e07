# -*- coding: utf-8 -*-
"""
创建时间: 2024/07/31
功能: 训练 V1 版本的基于网格分类的轨迹预测模型（`GridClassifierV1`）。本脚本负责模型训练的整个生命周期，包括数据集加载、模型初始化、优化器配置和训练循环管理。
输入:
  - `configs/main_config.yaml` 和 `configs/models/v1_grid_classifier_model.yaml` 中定义的 `config` 对象：包含了模型、数据加载、训练超参数（如批次大小、学习率）和LMDB路径等所有必要的配置信息。
  - LMDB训练集和验证集：通过 `config.data_preprocessing.output_path` 构建的路径指定，包含了预处理后的轨迹和环境ROI数据。
输出:
  - 训练过程中的日志信息（打印到控制台）。
  - 模型检查点文件（通过 `Trainer` 类保存，具体路径由 `config` 中的设置决定，通常存储在 `checkpoints/` 目录下）。
  - 训练过程中可能生成的TensorBoard或其他可视化工具的日志文件（如果 `Trainer` 中配置了相关功能）。
原理:
  - **Python路径管理:** 脚本在执行开始时，动态地将项目根目录添加到 `sys.path`，确保 `src` 目录下的自定义模块（如 `TrajectoryDataset`, `GridClassifierV1`, `Trainer`）能够被正确导入和识别。
  - **设备设置:** 脚本会根据系统环境自动检测并选择可用的计算设备（CUDA GPU或CPU），以优化训练性能。
  - **数据加载:** 实例化 `src.data.datasets.TrajectoryDataset` 来加载LMDB格式的训练和验证数据集。这些数据集通过 `torch.utils.data.DataLoader` 进行封装，以支持高效的批量数据加载、随机打乱（针对训练集）和多进程并行数据预取。
  - **模型构建:** 根据配置参数实例化 `src.models.v1_grid_classifier.end_to_end_model.GridClassifierV1` 模型。模型在初始化时会接收从数据集中获取的目的地归一化均值和标准差，以正确处理目标点的归一化和反归一化。
  - **优化器和调度器:** 使用 `torch.optim.Adam` 作为优化器来更新模型的参数，并基于 `config` 中的学习率进行初始化。可选地，配置 `torch.optim.lr_scheduler.ReduceLROnPlateau` 学习率调度器，以在验证损失连续多个epoch没有改善时自适应地降低学习率，有助于模型的收敛。
  - **训练循环封装:** 通过实例化 `src.engine.trainer.Trainer` 类，将复杂的训练和验证逻辑进行封装。`Trainer` 类负责管理每个 epoch 的训练和验证步骤、损失计算、梯度反向传播、模型状态的保存、以及训练指标的日志记录。
处理方法:
  - 脚本设计为独立可执行的模块，通过 `main()` 函数作为入口点，封装了完整的训练流程。
  - 训练过程中的关键信息和状态更新会通过 `print` 语句输出到控制台，方便用户实时监控。
  - 利用 `Trainer` 类实现训练逻辑的高度解耦，使得模型训练代码结构清晰，易于维护和扩展。
  - 包含了对 `num_workers` 和 `pin_memory` 等 `DataLoader` 参数的配置，以优化数据加载效率。
"""
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
import sys
from pathlib import Path
import torch.nn as nn # 导入 nn 模块
from torch.utils.tensorboard import SummaryWriter # 导入 SummaryWriter
import os # 确保 os 在文件顶部导入
from datetime import datetime # 确保 datetime 在文件顶部导入
import yaml # 导入 yaml 模块
from omegaconf import OmegaConf # 导入 OmegaConf
import logging # 导入 logging 模块

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 确保src目录在python path中
project_root = Path(__file__).resolve().parents[2]
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.data.datasets import LMDBDataset # 将 TrajectoryDataset 替换为 LMDBDataset
from src.models.v1_grid_classifier.end_to_end_model import GridClassifierV1
from src.engine.trainer import Trainer
from src.utils.config_loader import load_config # 导入新的配置加载器


def main(config):
    """
    V1模型的主训练函数
    """
    print("--- 开始V1模型训练流程 ---")

    # 移除 main() 函数内部重复的配置加载，因为它现在将从外部接收 config 对象
    # config_path = 'configs/main_config.yaml' # 假设主配置文件路径
    # model_config_path = 'configs/models/v1_grid_classifier_model.yaml' # V1模型配置文件路径
    # data_preprocessing_config_path = 'configs/data_preprocessing.yaml' # 数据预处理配置文件路径
    
    # config = load_config(config_path, model_config_path, data_preprocessing_config_path)

    # 创建带有时间戳的运行目录，使用 Path 对象
    current_time_str = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir_path = Path(config.training.log_dir) / f"v1_model_{current_time_str}"
    log_dir_path.mkdir(parents=True, exist_ok=True)
    writer = SummaryWriter(str(log_dir_path))

    # 1. 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 2. 初始化数据集和数据加载器
    print("正在初始化数据集...")
    # 从新的config中获取LMDB路径，并使用 LMDBDataset 初始化
    train_dataset = LMDBDataset(
        config=config, # 传入config对象
        lmdb_type='train' # 指定为训练集
    )
    val_dataset = LMDBDataset(
        config=config, # 传入config对象
        lmdb_type='val' # 指定为验证集
    )
    print(f"  -> 训练集样本数: {len(train_dataset)}")
    print(f"  -> 验证集样本数: {len(val_dataset)}")

    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size, # 从 config.training 中获取 batch_size
        shuffle=True,
        num_workers=config.training.get('num_workers', config.data_preprocessing.num_processing_workers), # 优先从 training 获取，否则从 data_preprocessing 获取
        pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size, # 从 config.training 中获取 batch_size
        shuffle=False,
        num_workers=config.training.get('num_workers', config.data_preprocessing.num_processing_workers), # 优先从 training 获取，否则从 data_preprocessing 获取
        pin_memory=True
    )
    print("数据加载器创建完毕。")

    # 3. 初始化模型
    print("正在初始化模型...")
    # 从数据集中获取归一化参数，并传递给模型
    import pickle # 放在这里，确保 os 也被正确导入
    stats_path = Path(config.data_preprocessing.output_path) / "normalization_stats.pkl"
    if not stats_path.exists(): # 使用 Path 对象的 exists() 方法
        print(f"错误: 归一化统计文件未找到: {stats_path}。请先运行数据预处理脚本。")
        sys.exit(1)
    with open(stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    
    # 修改为正确的key: target_y_mean和target_y_std，因为目标是二维的
    dest_mean = torch.tensor([normalization_stats['target_x_mean'], normalization_stats['target_y_mean']], dtype=torch.float32).to(device) 
    dest_std = torch.tensor([normalization_stats['target_x_std'], normalization_stats['target_y_std']], dtype=torch.float32).to(device)  

    # 根据之前对GridClassifierV1的检查，它需要dest_mean和dest_std
    model = GridClassifierV1(config, dest_mean, dest_std).to(device)
    print("模型 'GridClassifierV1' 初始化完毕。")

    # 4. 初始化优化器
    optimizer = optim.Adam(
        model.parameters(), 
        lr=config.training.learning_rate # 从 config.training 中获取 learning_rate
    )
    # 可以选择性地添加学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5, factor=0.5, verbose=True)
    print("优化器和学习率调度器初始化完毕。")

    # 5. 初始化并运行训练器
    print("\n--- 开始训练 ---")
    trainer = Trainer(
        model=model,
        criterion=nn.L1Loss(), # 为V1模型传入L1Loss作为轨迹回归损失
        optimizer=optimizer,
        device=device,
        train_loader=train_loader,
        val_loader=val_loader,
        writer=writer, # 传入 SummaryWriter 实例
        config=config,
        normalization_stats=normalization_stats # 确保传递归一化统计数据
    )
    
    # 手动实现训练循环，包括学习率调度器和早停逻辑
    num_epochs = config.training.num_epochs
    best_val_loss = float('inf')
    epochs_no_improve = 0
    early_stopping_patience = config.training.get('early_stopping_patience', 10)
    model_save_path = Path(config.training.checkpoints_root_dir) / f"v1_model_{current_time_str}" / 'best_model.pth'
    model_save_path.parent.mkdir(parents=True, exist_ok=True)

    for epoch in range(num_epochs):
        print(f"Epoch {epoch+1}/{num_epochs}")
        
        # 训练一个epoch
        train_loss = trainer.train_one_epoch(epoch)
        
        # 验证一个epoch
        val_loss = trainer.validate_one_epoch(epoch)

        # 学习率调度器步进
        if scheduler:
            scheduler.step(val_loss)

        # 保存最佳模型逻辑
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            epochs_no_improve = 0
            torch.save(model.state_dict(), model_save_path)
            print(f"保存最佳模型到 {model_save_path} (验证损失: {best_val_loss:.4f})")
        else:
            epochs_no_improve += 1
            print(f"验证损失未改善，计数: {epochs_no_improve}/{early_stopping_patience}")
            if epochs_no_improve >= early_stopping_patience:
                print(f"早停触发，在 Epoch {epoch+1} 停止训练。")
                break

    writer.close()
    print("--- V1模型训练完成！---")

    # 按照记忆规则，训练结束后将最终模型（最佳模型）复制到 models/ 目录下并生成 metadata.yaml
    final_model_dir_name = f"v1_model_{current_time_str}"
    final_model_dir = Path('models') / final_model_dir_name
    final_model_dir.mkdir(parents=True, exist_ok=True)
    final_model_path = final_model_dir / 'best_model.pth' # 复制最佳模型
    
    # 复制最佳模型文件
    if model_save_path.exists():
        torch.save(model.state_dict(), final_model_path) # 直接保存当前加载的最佳模型状态
        print(f"最终最佳模型复制到: {final_model_path}")
    else:
        print("警告: 未找到最佳模型检查点，无法复制最终模型。")

    # 生成 metadata.yaml 文件
    metadata_path = final_model_dir / 'metadata.yaml'
    with open(metadata_path, 'w') as f:
        metadata = {
            'model_version': '1.0.0',
            'model_type': 'GridClassifierV1',
            'data_version': config.data_preprocessing.output_path.split('/')[-1], # 从数据路径提取版本
            'training_config': OmegaConf.to_container(config, resolve=True), # 将 config 对象转换为纯 Python 字典
            'best_val_loss': float(f'{best_val_loss:.4f}'), # 记录最终最佳验证损失
            'trained_epochs': epoch + 1, # 记录实际训练的epoch数量
            'git_commit_hash': os.popen('git rev-parse HEAD').read().strip(), # 获取当前git commit hash
            'author': 'AI Assistant',
            'creation_time': datetime.now().isoformat()
        }
        yaml.dump(metadata, f, default_flow_style=False)
    print(f"模型元数据保存到: {metadata_path}")

if __name__ == '__main__':
    # 确保路径设置正确
    project_root_main = Path(__file__).resolve().parents[2]
    if str(project_root_main) not in sys.path:
         sys.path.append(str(project_root_main))
    
    # 在main函数外部加载配置
    config_path = 'configs/main_config.yaml' # 假设主配置文件路径
    model_config_path = 'configs/models/v1_grid_classifier_model.yaml' # V1模型配置文件路径
    data_preprocessing_config_path = 'configs/data_preprocessing.yaml' # 数据预处理配置文件路径
    
    # 加载并合并所有相关配置
    config = load_config(config_path, model_config_path, data_preprocessing_config_path)

    main(config) # 将 config 作为参数传递给 main 函数 