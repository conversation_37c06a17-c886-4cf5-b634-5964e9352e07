#!/usr/bin/env python3
"""
V4 Non-Autoregressive Model 训练脚本
一次性生成轨迹预测模型训练
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import pickle
import logging
from datetime import datetime

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.v4_non_autoregressive.model import NonAutoregressiveModel

# 创建__init__.py文件确保模块可导入
v4_init_path = Path(__file__).resolve().parents[2] / 'src' / 'models' / 'v4_non_autoregressive' / '__init__.py'
v4_init_path.parent.mkdir(parents=True, exist_ok=True)
if not v4_init_path.exists():
    v4_init_path.write_text('# V4 Non-Autoregressive Model Package\n')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('v4_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class V4Trainer:
    """V4模型训练器"""
    
    def __init__(self, model, train_loader, val_loader, config, normalization_stats, device):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.config = config
        self.normalization_stats = normalization_stats
        self.device = device
        
        # 优化器
        self.optimizer = optim.Adam(
            model.parameters(),
            lr=config.training.learning_rate,
            weight_decay=config.training.weight_decay
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.training.scheduler.T_max,
            eta_min=config.training.scheduler.eta_min
        )
        
        # 损失函数 (物理空间MSE)
        self.trajectory_criterion = nn.MSELoss()
        self.destination_criterion = nn.MSELoss()
        
        # TensorBoard
        log_dir = f"{config.logging.log_dir}/{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.writer = SummaryWriter(log_dir)
        
        # 训练状态
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.global_step = 0
        
        logger.info("V4训练器初始化完成")
    
    def _denormalize_trajectory(self, normalized_traj, use_target_stats=True):
        """反归一化轨迹到物理坐标空间"""
        if use_target_stats and 'target_mean' in self.normalization_stats:
            x_mean = self.normalization_stats['target_mean']['x']
            x_std = self.normalization_stats['target_std']['x']
            y_mean = self.normalization_stats['target_mean']['y']
            y_std = self.normalization_stats['target_std']['y']
        else:
            x_mean = self.normalization_stats['history_mean']['x']
            x_std = self.normalization_stats['history_std']['x']
            y_mean = self.normalization_stats['history_mean']['y']
            y_std = self.normalization_stats['history_std']['y']
        
        denormalized = normalized_traj.clone()
        denormalized[..., 0] = denormalized[..., 0] * x_std + x_mean
        denormalized[..., 1] = denormalized[..., 1] * y_std + y_mean
        
        return denormalized
    
    def _compute_physical_loss(self, pred_traj, gt_traj, pred_dest, gt_dest):
        """在物理空间计算损失"""
        # 反归一化到物理空间
        pred_traj_physical = self._denormalize_trajectory(pred_traj, use_target_stats=True)
        gt_traj_physical = self._denormalize_trajectory(gt_traj, use_target_stats=True)
        pred_dest_physical = self._denormalize_trajectory(pred_dest.unsqueeze(1), use_target_stats=True).squeeze(1)
        gt_dest_physical = self._denormalize_trajectory(gt_dest.unsqueeze(1), use_target_stats=True).squeeze(1)
        
        # 计算损失
        traj_loss = self.trajectory_criterion(pred_traj_physical, gt_traj_physical)
        dest_loss = self.destination_criterion(pred_dest_physical, gt_dest_physical)
        
        # 加权组合
        total_loss = (self.config.training.trajectory_loss_weight * traj_loss + 
                     self.config.training.destination_loss_weight * dest_loss)
        
        return total_loss, traj_loss, dest_loss
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_traj_loss = 0.0
        total_dest_loss = 0.0
        num_batches = len(self.train_loader)
        
        for batch_idx, batch in enumerate(self.train_loader):
            if batch is None:
                continue
            
            # 数据移到设备
            history_features = batch['history_features'].to(self.device)
            ground_truth_trajectory = batch['ground_truth_trajectory'].to(self.device)
            history_mask = batch['history_mask'].to(self.device)
            environment_roi = batch['environment_roi'].to(self.device)
            
            # 目标点是轨迹的最后一个点
            ground_truth_destination = ground_truth_trajectory[:, -1, :]
            
            # 前向传播
            predicted_trajectory, predicted_destination = self.model(
                history_trajectory=history_features,
                environment_roi=environment_roi,
                history_mask=history_mask,
                ground_truth_trajectory=ground_truth_trajectory  # 训练时可以传入
            )
            
            # 计算损失
            loss, traj_loss, dest_loss = self._compute_physical_loss(
                predicted_trajectory, ground_truth_trajectory,
                predicted_destination, ground_truth_destination
            )
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                self.model.parameters(), 
                self.config.training.gradient_clipping.max_norm
            )
            
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            total_traj_loss += traj_loss.item()
            total_dest_loss += dest_loss.item()
            
            # 记录到TensorBoard
            if batch_idx % 10 == 0:
                self.writer.add_scalar('Train/Loss', loss.item(), self.global_step)
                self.writer.add_scalar('Train/TrajectoryLoss', traj_loss.item(), self.global_step)
                self.writer.add_scalar('Train/DestinationLoss', dest_loss.item(), self.global_step)
                self.writer.add_scalar('Train/LearningRate', self.optimizer.param_groups[0]['lr'], self.global_step)
            
            self.global_step += 1
            
            # 打印进度
            if batch_idx % 10 == 0:
                logger.info(f'Epoch {epoch}, Batch {batch_idx}/{num_batches}, '
                          f'Loss: {loss.item():.2f}, '
                          f'TrajLoss: {traj_loss.item():.2f}, '
                          f'DestLoss: {dest_loss.item():.2f}')
        
        avg_loss = total_loss / num_batches
        avg_traj_loss = total_traj_loss / num_batches
        avg_dest_loss = total_dest_loss / num_batches
        
        logger.info(f'Epoch {epoch} 训练完成 - '
                   f'平均损失: {avg_loss:.2f}, '
                   f'轨迹损失: {avg_traj_loss:.2f}, '
                   f'目标损失: {avg_dest_loss:.2f}')
        
        return avg_loss
    
    def validate_epoch(self, epoch):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        total_traj_loss = 0.0
        total_dest_loss = 0.0
        num_batches = len(self.val_loader)
        
        with torch.no_grad():
            for batch in self.val_loader:
                if batch is None:
                    continue
                
                # 数据移到设备
                history_features = batch['history_features'].to(self.device)
                ground_truth_trajectory = batch['ground_truth_trajectory'].to(self.device)
                history_mask = batch['history_mask'].to(self.device)
                environment_roi = batch['environment_roi'].to(self.device)
                
                # 目标点是轨迹的最后一个点
                ground_truth_destination = ground_truth_trajectory[:, -1, :]
                
                # 前向传播 (验证时不传入真实轨迹)
                predicted_trajectory, predicted_destination = self.model(
                    history_trajectory=history_features,
                    environment_roi=environment_roi,
                    history_mask=history_mask,
                    ground_truth_trajectory=None  # 验证时不使用
                )
                
                # 计算损失
                loss, traj_loss, dest_loss = self._compute_physical_loss(
                    predicted_trajectory, ground_truth_trajectory,
                    predicted_destination, ground_truth_destination
                )
                
                total_loss += loss.item()
                total_traj_loss += traj_loss.item()
                total_dest_loss += dest_loss.item()
        
        avg_loss = total_loss / num_batches
        avg_traj_loss = total_traj_loss / num_batches
        avg_dest_loss = total_dest_loss / num_batches
        
        # 记录到TensorBoard
        self.writer.add_scalar('Val/Loss', avg_loss, epoch)
        self.writer.add_scalar('Val/TrajectoryLoss', avg_traj_loss, epoch)
        self.writer.add_scalar('Val/DestinationLoss', avg_dest_loss, epoch)
        
        logger.info(f'Epoch {epoch} 验证完成 - '
                   f'平均损失: {avg_loss:.2f}, '
                   f'轨迹损失: {avg_traj_loss:.2f}, '
                   f'目标损失: {avg_dest_loss:.2f}')
        
        return avg_loss
    
    def save_checkpoint(self, epoch, val_loss, is_best=False):
        """保存检查点"""
        checkpoint_dir = Path(self.config.checkpointing.save_dir)
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'val_loss': val_loss,
            'config': self.config,
            'normalization_stats': self.normalization_stats
        }
        
        if is_best:
            best_path = checkpoint_dir / f"best_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pth"
            torch.save(checkpoint, best_path)
            logger.info(f"保存最佳模型到 {best_path}")
        
        if self.config.checkpointing.save_last:
            last_path = checkpoint_dir / "last_model.pth"
            torch.save(checkpoint, last_path)
    
    def train(self):
        """主训练循环"""
        logger.info("开始训练V4模型...")
        
        for epoch in range(1, self.config.training.max_epochs + 1):
            logger.info(f"Epoch {epoch}/{self.config.training.max_epochs}")
            
            # 训练
            train_loss = self.train_epoch(epoch)
            
            # 验证
            val_loss = self.validate_epoch(epoch)
            
            # 学习率调度
            self.scheduler.step()
            
            # 检查是否是最佳模型
            is_best = val_loss < self.best_val_loss
            if is_best:
                self.best_val_loss = val_loss
                self.patience_counter = 0
                logger.info(f"新的最佳验证损失: {val_loss:.2f}")
            else:
                self.patience_counter += 1
            
            # 保存检查点
            self.save_checkpoint(epoch, val_loss, is_best)
            
            # 早停检查
            if self.patience_counter >= self.config.training.early_stopping.patience:
                logger.info(f"早停触发，在epoch {epoch}停止训练")
                break
        
        logger.info("训练完成！")
        self.writer.close()

def main():
    """主函数"""
    logger.info("=== V4 Non-Autoregressive Model 训练 ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml', 'configs/models/v4_non_autoregressive.yaml')
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载归一化统计
    normalization_stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    with open(normalization_stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    
    # 创建数据集
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=LMDBDataset.collate_fn,
        num_workers=4
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.validation.batch_size,
        shuffle=False,
        collate_fn=LMDBDataset.collate_fn,
        num_workers=2
    )
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    
    # 创建模型
    model = NonAutoregressiveModel(config.model, normalization_stats).to(device)
    
    # 打印模型信息
    model_info = model.get_model_info()
    logger.info(f"模型信息: {model_info}")
    
    # 创建训练器
    trainer = V4Trainer(model, train_loader, val_loader, config, normalization_stats, device)
    
    # 开始训练
    trainer.train()

if __name__ == "__main__":
    main()
