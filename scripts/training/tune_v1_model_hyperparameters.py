# -*- coding: utf-8 -*-
"""
创建时间: 2024/07/26
功能: 使用 Optuna 框架对 V1 版本的基于网格分类的轨迹预测模型 (GridClassifierV1) 进行超参数优化。
      本脚本通过定义一个目标函数，让 Optuna 自动探索不同的超参数组合，旨在找到使模型在验证集上表现最佳的超参数配置。
输入:
  - `configs/main_config.yaml`: 项目的主配置文件，包含数据路径、通用训练设置等。
  - `configs/models/v1_grid_classifier_model.yaml`: V1 模型特有的配置，定义模型结构和特定参数。
  - LMDB训练集和验证集：由数据预处理脚本生成，路径在 `main_config.yaml` 中定义。
输出:
  - Optuna 优化过程的日志输出（控制台）。
  - Optuna study 数据库文件 (`v1_optuna_study.db`)，记录所有试验结果、超参数组合和对应的性能。
  - 在每个试验中，可能根据 Trainer 的设置保存模型检查点。
原理及处理方法:
  - **Python路径管理:** 脚本在执行开始时，动态地将项目根目录添加到 `sys.path`，确保 `src` 目录下的自定义模块能够被正确导入。
  - **Optuna `objective` 函数:** 定义一个名为 `objective` 的函数，该函数是 Optuna 优化的核心。
    - 在 `objective` 函数内部，通过 `trial.suggest_` 方法（如 `suggest_loguniform`、`suggest_int`、`suggest_categorical` 等）来建议不同的超参数值。这些超参数包括学习率、RNN隐藏层大小、RNN层数、Dropout概率、KL损失权重和高斯混合成分数量。
    - 加载 `main_config.yaml` 和 `v1_grid_classifier_model.yaml`，并将 Optuna 建议的超参数覆盖到配置对象中，形成当前试验的完整配置。
    - 根据更新后的配置，动态初始化 `GridClassifierV1` 模型、AdamW 优化器和相关的损失函数（如 `CombinedLossV1`）。
    - 实例化 `Trainer` 类，传入模型、损失、优化器、数据加载器、配置、归一化统计数据以及当前的 `trial.number`。
    - 运行训练和验证循环，并获取 `Trainer` 返回的验证损失作为 `objective` 函数的优化目标。
    - 使用 Optuna 的回调函数 `callbacks=[OptunaPyTorchLightningPruningCallback(trial, monitor="val_loss")]` (这里简化为手动集成) 实现早停剪枝，如果当前试验的性能在早期就不理想，则提前终止该试验，从而节省计算资源。
  - **Optuna `study` 管理:**
    - 创建一个 Optuna `study` 对象，指定优化方向 (`direction="minimize"`) 和存储数据库的路径 (`storage="sqlite:///v1_optuna_study.db"`)。
    - 使用 `study.optimize()` 方法启动优化过程，指定试验的数量 (`n_trials`)。
    - 在优化结束后，打印最佳试验的超参数和最佳性能。
  - **数据加载与归一化:** 在每个试验中，都会加载LMDB格式的训练和验证数据集，并加载预计算的归一化统计数据，以确保数据处理的一致性。
  - **模型和训练器的动态配置:** 模型的创建、优化器的配置以及 Trainer 的初始化都依赖于从 Optuna `trial` 中获取的超参数，实现了高度的自动化和灵活性。
"""
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard.writer import SummaryWriter
import sys
from pathlib import Path
import os
from datetime import datetime
import logging
import optuna
import yaml # 导入 yaml 模块
from omegaconf import OmegaConf # 导入 OmegaConf
import torch.nn as nn # 新增：导入 nn 模块

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 确保src目录在python path中
project_root = Path(__file__).resolve().parents[2]
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.data.datasets import LMDBDataset
from src.models.v1_grid_classifier.end_to_end_model import GridClassifierV1
from src.engine.trainer import Trainer
from src.utils.config_loader import load_config, update_config
from src.utils.normalization import load_normalization_stats
from src.engine.loss import CustomLoss # 修改：导入 CustomLoss

def objective(trial: optuna.Trial):
    # 1. 加载主配置和模型特定配置
    main_config_path = 'configs/main_config.yaml'
    model_config_path = 'configs/models/v1_grid_classifier_model.yaml'

    config = load_config(main_config_path)
    model_config = load_config(model_config_path)
    update_config(config, model_config)

    # 2. 建议超参数
    # 学习率
    config.training.learning_rate = trial.suggest_loguniform('learning_rate', 1e-5, 1e-3)
    # RNN隐藏层大小
    config.modeling.rnn_hidden_size = trial.suggest_categorical('rnn_hidden_size', [128, 256, 512])
    # RNN层数
    config.modeling.num_rnn_layers = trial.suggest_int('num_rnn_layers', 1, 3)
    # Dropout概率
    config.modeling.dropout_prob = trial.suggest_uniform('dropout_prob', 0.0, 0.5)
    # KL损失权重
    config.training.kl_loss_weight = trial.suggest_loguniform('kl_loss_weight', 1e-4, 0.1)
    # GMM混合成分数量 (从v1_grid_classifier_model.yaml中获取)
    # 注意：这里需要确保模型能够正确接收和使用这个参数
    config.model.num_mixture = trial.suggest_categorical('num_mixture', [3, 5, 7])

    # 早停耐心值 (可以在优化过程中调整)
    config.training.early_stopping_patience = trial.suggest_int('early_stopping_patience', 10, 30)

    # 批次大小已经在 main_config.yaml 中修改为 512，这里不建议再修改，以免复杂化
    # config.training.batch_size = 512

    print(f"--- Optuna Trial {trial.number} 开始，超参数：{trial.params} ---")

    # 创建带有时间戳的运行目录
    current_time_str = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir_path = Path(config.training.log_dir) / f"v1_optuna_trial_{trial.number}_{current_time_str}"
    log_dir_path.mkdir(parents=True, exist_ok=True)
    writer = SummaryWriter(str(log_dir_path))

    # 设备设置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 加载归一化统计数据
    normalization_stats_path = Path(config.data_preprocessing.output_path) / 'normalization_stats.pkl'
    if not normalization_stats_path.exists():
        logging.error(f"错误: 归一化统计文件未找到: {normalization_stats_path}")
        raise FileNotFoundError(f"归一化统计文件未找到: {normalization_stats_path}")
    normalization_stats = load_normalization_stats(str(normalization_stats_path))

    # 初始化数据集和数据加载器
    train_dataset = LMDBDataset(config=config, lmdb_type='train')
    val_dataset = LMDBDataset(config=config, lmdb_type='val')
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=config.training.get('num_workers', config.data_preprocessing.num_processing_workers),
        pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=config.training.get('num_workers', config.data_preprocessing.num_processing_workers),
        pin_memory=True
    )
    print(f"  -> 训练集样本数: {len(train_dataset)}")
    print(f"  -> 验证集样本数: {len(val_dataset)}")

    # 提取并转换为 PyTorch Tensor for GridClassifierV1
    target_x_mean = normalization_stats['target_x_mean']
    target_y_mean = normalization_stats['target_y_mean']
    target_x_std = normalization_stats['target_x_std']
    target_y_std = normalization_stats['target_y_std']

    dest_mean_tensor = torch.tensor([target_x_mean, target_y_mean], dtype=torch.float32, device=device)
    dest_std_tensor = torch.tensor([target_x_std, target_y_std], dtype=torch.float32, device=device)

    # 模型构建
    # 注意：GridClassifierV1 的初始化需要 normalization_stats 中的均值和标准差
    model = GridClassifierV1(config, dest_mean=dest_mean_tensor, dest_std=dest_std_tensor).to(device) # 传入config和均值、标准差张量

    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config.training.learning_rate,
        weight_decay=config.training.weight_decay
    )

    # 损失函数 (根据 Trainer 中的逻辑调整)
    criterion = {
        'intent_loss': nn.CrossEntropyLoss(),
        'trajectory_loss': CustomLoss(loss_type='l1') # 使用L1损失进行轨迹回归
    }

    # 学习率调度器
    scheduler = None
    if config.training.scheduler.enabled:
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode=config.training.scheduler.mode,
            factor=config.training.scheduler.factor,
            patience=config.training.scheduler.patience,
            verbose=config.training.scheduler.verbose
        )

    # 初始化 Trainer
    trainer = Trainer(
        model=model,
        criterion=criterion,
        optimizer=optimizer,
        device=device,
        train_loader=train_loader,
        val_loader=val_loader,
        writer=writer,
        config=config,
        normalization_stats=normalization_stats,
        current_trial_number=trial.number # 传递当前 trial number
    )

    # 训练循环
    # best_val_loss_trial = float('inf') # 不再需要这个变量，使用 trainer.best_val_loss
    for epoch in range(config.training.num_epochs):
        train_loss = trainer.train_one_epoch(epoch)
        val_loss = trainer.validate_one_epoch(epoch)

        if scheduler:
            scheduler.step(val_loss)

        # 更新 Trainer 内部的早停逻辑
        if val_loss < trainer.best_val_loss:
            trainer.best_val_loss = val_loss
            trainer.patience_counter = 0 # 找到了新的最佳损失，重置早停计数器
            trainer.current_best_val_loss_epoch = epoch # 记录最佳损失所在的 epoch
            logging.info(f"保存最佳模型到 {trainer.checkpoint_dir}/best_model.pth (验证损失: {trainer.best_val_loss:.4f})")
            torch.save(trainer.model.state_dict(), os.path.join(trainer.checkpoint_dir, 'best_model.pth'))
        else:
            trainer.patience_counter += 1
            logging.info(f"验证损失未改善，计数: {trainer.patience_counter}/{config.training.early_stopping_patience}")
            if trainer.patience_counter >= config.training.early_stopping_patience:
                logging.info("早停! 验证损失在 " + str(config.training.early_stopping_patience) + " 个 Epoch 内没有改善。")
                break

        # Optuna 剪枝（Early Stopping）
        trial.report(val_loss, epoch)
        if trial.should_prune():
            print(f"Trial {trial.number} 在 Epoch {epoch} 被剪枝。")
            raise optuna.exceptions.TrialPruned()

        # if val_loss < best_val_loss_trial:
        #     best_val_loss_trial = val_loss

    writer.close()
    return trainer.best_val_loss # 返回 Trainer 内部的最佳验证损失

if __name__ == "__main__":
    study_name = "v1_grid_classifier_optimization"
    storage_path = "sqlite:///pecnet_optuna_study.db" # 使用现有数据库文件

    # 创建或加载 study
    study = optuna.create_study(
        study_name=study_name,
        storage=storage_path,
        direction="minimize",
        load_if_exists=True
    )

    print(f"开始 Optuna 优化，Study: {study_name}, 存储: {storage_path}")
    print(f"现有试验数量: {len(study.trials)}")

    # 运行优化，可以根据需要调整 n_trials
    # n_trials 参数表示要运行多少次超参数组合
    # 如果已经有现有试验，会在此基础上继续
    study.optimize(objective, n_trials=5, show_progress_bar=True)

    print("\n--- Optuna 优化完成 ---")
    print("最佳试验数量:", study.best_trial.number)
    print("最佳试验值 (验证损失):", study.best_trial.value)
    print("最佳超参数:")
    for key, value in study.best_trial.params.items():
        print(f"  {key}: {value}")

    # 可以进一步保存最佳模型等，但这里主要关注优化过程 