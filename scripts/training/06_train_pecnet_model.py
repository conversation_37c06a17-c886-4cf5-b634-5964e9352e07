# -*- coding: utf-8 -*-
"""
创建时间: 2025-07-24
功能: PECNet模型训练脚本，用于端点条件轨迹预测。
输入: 
    - LMDB数据集 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train)
    - LMDB验证集 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/val)
    - 归一化统计文件 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/normalization_stats.pkl)
    - 配置文件 (configs/main_config.yaml, configs/models/pecnet_model.yaml)
输出: 
    - 模型检查点 (checkpoints/PECNet_YYYYMMDD_HHMMSS/)
    - TensorBoard日志 (outputs/runs/PECNet_YYYYMMDD_HHMMSS/)
    - 最终模型 (models/PECNet_YYYYMMDD/)
原理:
    1. 加载配置和归一化统计数据。
    2. 初始化LMDB数据集和DataLoader。
    3. 定义PECNet模型架构。
    4. 定义损失函数（重建损失和KL散度损失）和优化器。
    5. 实现训练循环，包括前向传播、损失计算、反向传播和参数更新。
    6. 实现验证循环，评估模型性能。
    7. 使用TensorBoard记录训练指标和损失。
    8. 保存最佳模型检查点，并记录训练总结。
处理方法:
    - 使用PyTorch框架构建模型和训练流程。
    - LMDBDataset用于高效数据加载。
    - 动态加载配置，支持命令行参数覆盖。
    - 训练过程中，端点预测模块使用重参数化技巧进行采样。
    - 使用Adam优化器和学习率调度器。
    - 通过 early stopping 防止过拟合。
作者: AI Assistant
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import time
import pickle
import logging
from datetime import datetime
from tqdm import tqdm
import argparse
from pathlib import Path # 导入Path，用于处理文件路径

# 将项目根目录添加到Python路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.pecnet_model.pecnet_model import PECNet # 导入PECNet模型
from src.engine.trainer import Trainer # 导入Trainer类
from omegaconf import OmegaConf # 导入OmegaConf，用于打印omegaconf对象

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout) # 将日志输出到控制台
    ]
)

def main(args):
    logging.info("--- 开始PECNet模型训练流程 ---")

    # 加载配置
    config = load_config(args.config, os.path.join('configs', 'models', args.model_config_name))
    logging.info("合并后的配置如下:\n" + OmegaConf.to_yaml(config))

    # 设置随机种子 (Trainer中已设置)
    # random_seed = config.training.random_seed
    # torch.manual_seed(random_seed)
    # np.random.seed(random_seed)
    # random.seed(random_seed)

    # 数据加载
    # lmdb_path_train = os.path.join(config.data_preprocessing.output_path, 'train')
    # lmdb_path_val = os.path.join(config.data_preprocessing.output_path, 'val')
    train_dataset = LMDBDataset(config, lmdb_type='train')
    val_dataset = LMDBDataset(config, lmdb_type='val')

    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=config.training.num_workers,
        pin_memory=True,
        collate_fn=LMDBDataset.collate_fn
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=config.training.num_workers,
        pin_memory=True,
        collate_fn=LMDBDataset.collate_fn
    )
    logging.info(f"训练集样本数: {len(train_dataset)}, 验证集样本数: {len(val_dataset)}")

    # 模型初始化
    # environment_roi 的通道数来自 data_preprocessing.yaml 中的 num_land_cover_classes + env_maps数量
    # env_feature_dim 可以在PECNet的__init__中作为参数传入，或者在forward中聚合
    # 这里我们假设 environment_roi 经过一个CNN提取特征后，会得到一个固定维度的向量作为 env_feature_dim
    # 或者简单起见，这里先假设environment_roi_data 会被处理成一个env_feature_dim维度的向量
    # 一个简单的环境特征聚合方法：将环境ROI (batch, T_obs_agg, C_env, H, W) 展平或平均池化到 (batch, T_obs_agg * C_env * H * W) 或 (batch, C_env)
    # 更合理的做法是在PECNet内部或预处理阶段处理环境ROI

    # 从配置中获取模型参数
    history_feature_dim = len(config.data_preprocessing.history_features)
    endpoint_dim = config.model.endpoint_dim
    output_len = config.model.output_len # 240
    hidden_dim = config.model.hidden_dim
    dropout_prob = config.model.dropout_prob if 'dropout_prob' in config.model else 0.3 # 确保有默认值
    num_layers = config.model.num_layers if 'num_layers' in config.model else 1 # 确保有默认值

    # 临时计算 env_feature_dim: 假设我们将 environment_roi (30, 13, 9, 9) 展平为 (30 * 13 * 9 * 9) = 31590
    # 但这对于每个样本来说太大。更合理的是，在模型内部处理环境ROI。
    # 假设我们在PECNet内部会对 environment_roi 进行一个简单的平均池化，将其从 (batch, T_obs_agg, C_env, H, W) 
    # 聚合为 (batch, C_env)。那么 env_feature_dim 就是 C_env，即 13。
    env_feature_dim = config.model.get('env_feature_dim', 13) # 从配置获取，或者默认13

    model = PECNet(
        history_feature_dim=history_feature_dim,
        endpoint_dim=endpoint_dim,
        env_feature_dim=env_feature_dim,
        output_len=output_len,
        hidden_dim=hidden_dim,
        dropout_prob=dropout_prob,
        num_layers=num_layers
    )

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    logging.info(f"使用设备: {device}")

    # 定义损失函数
    # PECNet 损失通常包括重建损失 (Reconstruction Loss) 和 KL 散度损失 (KL Divergence Loss)
    # 重建损失 (L1 Loss for trajectory and endpoint)
    reconstruction_criterion = torch.nn.MSELoss() # 可以使用MSELoss或L1Loss

    # KL散度损失辅助函数
    def kl_divergence_loss(mean, logvar):
        # KL散度 = 0.5 * sum(exp(logvar) + mean^2 - 1 - logvar)
        return -0.5 * torch.sum(1 + logvar - mean.pow(2) - logvar.exp())

    criterions = {
        'reconstruction': reconstruction_criterion,
        'kl_divergence': kl_divergence_loss
    }

    # 优化器和学习率调度
    optimizer = optim.AdamW(model.parameters(), lr=config.training.learning_rate, weight_decay=config.training.get('weight_decay', 0.0))
    
    scheduler = None
    if config.training.scheduler.enabled:
        scheduler_params = config.training.scheduler_params if config.training.scheduler_params is not None else {}
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, 
            mode=scheduler_params.get('mode', 'min'), 
            factor=scheduler_params.get('factor', 0.5), 
            patience=scheduler_params.get('patience', 10), 
            verbose=scheduler_params.get('verbose', True)
        )

    # TensorBoard writer
    run_name = f"PECNet_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    log_dir = os.path.join(config.training.log_dir, run_name) # 正确路径应该是 runs/track_model_v2/...
    writer = SummaryWriter(log_dir=log_dir)
    logging.info(f"TensorBoard 日志目录: {log_dir}")

    # 加载归一化统计数据 (PECNet 的 Trainer 需要)
    stats_path = config.data_params.stats_path_v2
    if not os.path.exists(stats_path):
        logging.error(f"错误: 归一化统计文件未找到: {stats_path}。请先运行数据预处理脚本。")
        sys.exit(1)
    with open(stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    logging.info("归一化统计数据加载成功。")

    # 初始化Trainer
    trainer = Trainer(
        model=model,
        criterion=criterions, # 传入损失函数字典
        optimizer=optimizer,
        device=device,
        train_loader=train_loader,
        val_loader=val_loader,
        writer=writer,
        config=config,
        normalization_stats=normalization_stats # 传入归一化统计数据
        # 移除 scheduler 参数，Trainer 不直接接受它
    )

    # 训练循环
    best_val_loss = float('inf')
    epochs_no_improve = 0
    early_stopping_patience = config.training.early_stopping_patience

    num_epochs = config.training.num_epochs
    val_interval = config.training.val_interval
    save_interval = config.training.save_interval

    for epoch in range(1, num_epochs + 1):
        logging.info(f"Epoch {epoch}/{num_epochs}")
        # 训练
        train_loss = trainer.train_one_epoch(epoch) # 移除 train_loader
        writer.add_scalar('Loss/Train', train_loss, epoch)

        # 验证
        if epoch % val_interval == 0:
            val_loss = trainer.validate_one_epoch(epoch) # 移除 val_loader
            writer.add_scalar('Loss/Validation', val_loss, epoch)

            # 学习率调度
            if scheduler:
                current_lr = optimizer.param_groups[0]['lr']
                logging.info(f"学习率调整：当前学习率：{current_lr:.6f}")
                scheduler.step(val_loss)

            # 早停逻辑
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                epochs_no_improve = 0
                # 保存最佳模型
                model_save_path = os.path.join(config.training.checkpoint_dir, config.model.name, "best_model.pth")
                os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
                torch.save(model.state_dict(), model_save_path)
                logging.info(f"保存最佳模型到 {model_save_path} (验证损失: {best_val_loss:.4f})")
            else:
                epochs_no_improve += 1
                logging.info(f"验证损失未改善，计数: {epochs_no_improve}/{early_stopping_patience}")
                if epochs_no_improve >= early_stopping_patience:
                    logging.info(f"早停! 验证损失在 {early_stopping_patience} 个 Epoch 内没有改善。")
                    break

        # 定期保存模型
        if epoch % save_interval == 0:
            model_save_path = os.path.join(config.training.checkpoint_dir, config.model.name, f"model_epoch_{epoch}.pth")
            os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
            torch.save(model.state_dict(), model_save_path)
            logging.info(f"保存模型到 {model_save_path}")

    writer.close()
    logging.info("--- 任务 train 完成 ---")

if __name__ == '__main__':
    import argparse
    import datetime # 将 torch.datetime 修改为 datetime

    parser = argparse.ArgumentParser(description='Train PECNet model.')
    parser.add_argument('--config', type=str, default='configs/main_config.yaml', help='Path to the main config file.')
    parser.add_argument('--model_config_name', type=str, default='pecnet_model.yaml', help='Name of the model-specific config file.')
    args = parser.parse_args()

    # 检查并创建必要的目录
    config = load_config(args.config, os.path.join('configs', 'models', args.model_config_name))
    os.makedirs(config.training.checkpoint_dir, exist_ok=True)
    os.makedirs(config.training.log_dir, exist_ok=True)

    main(args) 