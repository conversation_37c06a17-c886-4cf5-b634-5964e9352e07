#!/usr/bin/env python3
"""
V5模型训练脚本 - 使用改进的目标引导和注意力机制
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import logging
import time
from datetime import datetime
import numpy as np

from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.data.datasets import LMDBDataset
from src.utils.config_loader import load_config
from src.utils.metrics import calculate_ade, calculate_fde

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'training_v5_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_model(config):
    """创建V5模型"""
    model = TrajectoryPredictorV5(config)
    
    # 打印模型信息
    model_info = model.get_model_info()
    logger.info("=== V5模型信息 ===")
    for key, value in model_info.items():
        logger.info(f"{key}: {value}")
    
    return model

def create_datasets(config):
    """创建数据集"""
    # 训练集
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)

    # 验证集
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)

    logger.info(f"训练集样本数: {len(train_dataset)}")
    logger.info(f"验证集样本数: {len(val_dataset)}")

    return train_dataset, val_dataset

def create_data_loaders(train_dataset, val_dataset, config):
    """创建数据加载器"""
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True,
        collate_fn=LMDBDataset.collate_fn
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        drop_last=False,
        collate_fn=LMDBDataset.collate_fn
    )

    return train_loader, val_loader

def train_epoch(model, train_loader, optimizer, criterion, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    num_batches = 0
    
    for batch_idx, batch in enumerate(train_loader):
        # 移动数据到设备
        history_features = batch['history_features'].to(device)
        history_mask = batch['history_mask'].to(device)
        ground_truth_trajectory = batch['ground_truth_trajectory'].to(device)
        ground_truth_destination = batch['ground_truth_destination'].to(device)
        environment_roi = batch['environment_roi'].to(device)
        
        # 前向传播
        optimizer.zero_grad()
        predicted_trajectory = model(
            history_features=history_features,
            history_mask=history_mask,
            ground_truth_destination=ground_truth_destination,
            environment_roi=environment_roi
        )
        
        # 计算损失
        loss = criterion(predicted_trajectory, ground_truth_trajectory)
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        num_batches += 1
        
        # 打印进度
        if batch_idx % 10 == 0:
            logger.info(f'Epoch {epoch}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.6f}')
    
    avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
    return avg_loss

def validate_epoch(model, val_loader, criterion, device):
    """验证一个epoch"""
    model.eval()
    total_loss = 0.0
    total_ade = 0.0
    total_fde = 0.0
    num_batches = 0
    
    with torch.no_grad():
        for batch in val_loader:
            # 移动数据到设备
            history_features = batch['history_features'].to(device)
            history_mask = batch['history_mask'].to(device)
            ground_truth_trajectory = batch['ground_truth_trajectory'].to(device)
            ground_truth_destination = batch['ground_truth_destination'].to(device)
            environment_roi = batch['environment_roi'].to(device)
            
            # 前向传播
            predicted_trajectory = model(
                history_features=history_features,
                history_mask=history_mask,
                ground_truth_destination=ground_truth_destination,
                environment_roi=environment_roi
            )
            
            # 计算损失
            loss = criterion(predicted_trajectory, ground_truth_trajectory)
            total_loss += loss.item()
            
            # 计算指标
            pred_np = predicted_trajectory.cpu().numpy()
            gt_np = ground_truth_trajectory.cpu().numpy()
            
            batch_ade = calculate_ade(pred_np, gt_np)
            batch_fde = calculate_fde(pred_np, gt_np)
            
            total_ade += batch_ade
            total_fde += batch_fde
            num_batches += 1
    
    avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
    avg_ade = total_ade / num_batches if num_batches > 0 else 0.0
    avg_fde = total_fde / num_batches if num_batches > 0 else 0.0
    
    return avg_loss, avg_ade, avg_fde

def main():
    """主训练函数"""
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建模型
    model = create_model(config)
    model.to(device)
    
    # 创建数据集
    train_dataset, val_dataset = create_datasets(config)
    train_loader, val_loader = create_data_loaders(train_dataset, val_dataset, config)
    
    # 创建优化器和损失函数
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config.model.learning_rate,
        weight_decay=config.model.weight_decay
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, verbose=True
    )
    
    criterion = nn.MSELoss()
    
    # 训练循环
    num_epochs = config.training.num_epochs
    best_val_loss = float('inf')
    best_model_path = f"models/trajectory_predictor_v5_best.pth"
    
    # 确保模型目录存在
    os.makedirs("models", exist_ok=True)
    
    logger.info("=== 开始训练V5模型 ===")
    
    for epoch in range(1, num_epochs + 1):
        start_time = time.time()
        
        # 训练
        train_loss = train_epoch(model, train_loader, optimizer, criterion, device, epoch)
        
        # 验证
        val_loss, val_ade, val_fde = validate_epoch(model, val_loader, criterion, device)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        epoch_time = time.time() - start_time
        
        logger.info(f"Epoch {epoch}/{num_epochs}")
        logger.info(f"  训练损失: {train_loss:.6f}")
        logger.info(f"  验证损失: {val_loss:.6f}")
        logger.info(f"  验证ADE: {val_ade:.4f}")
        logger.info(f"  验证FDE: {val_fde:.4f}")
        logger.info(f"  用时: {epoch_time:.2f}秒")
        logger.info(f"  当前学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
                'val_ade': val_ade,
                'val_fde': val_fde,
                'config': config
            }, best_model_path)
            logger.info(f"保存最佳模型到: {best_model_path}")
        
        # 定期保存检查点
        if epoch % 10 == 0:
            checkpoint_path = f"models/trajectory_predictor_v5_epoch_{epoch}.pth"
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
                'config': config
            }, checkpoint_path)
            logger.info(f"保存检查点到: {checkpoint_path}")
    
    logger.info("=== 训练完成 ===")
    logger.info(f"最佳验证损失: {best_val_loss:.6f}")

if __name__ == "__main__":
    main()
