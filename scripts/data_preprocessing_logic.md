# 数据预处理逻辑说明

**创建时间**: 2025-07-20 14:23:27+08:00
**作者**: 郑明灿
**功能**: 本文档详细说明了 `scripts/data_prep/preprocess_main.py` 脚本的数据预处理逻辑、步骤、关键技术点以及输入输出。
**输入**: 原始轨迹CSV文件 (`data/trajectories_with_env/` 目录) 和环境地理信息数据 (DEM, 地表覆盖, 坡度, 坡向，位于 `environment/` 目录)。
**输出**: 归一化后的LMDB数据库，包含训练集和验证集 (默认输出到 `data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/` 目录)。

## 1. 概述

`scripts/data_prep/preprocess_main.py` 脚本旨在将原始的车辆轨迹数据和环境地理信息数据转化为深度学习模型可直接使用的规范化样本。
整个过程包括运动学特征计算、环境信息提取与编码、样本滑动窗口生成、特征归一化以及数据存储到LMDB数据库等步骤。

## 2. 关键概念

*   **历史轨迹**: 指车辆在过去一段时间内的运动轨迹，经过聚合处理后形成单点特征向量。
*   **未来轨迹**: 指模型需要预测的车辆未来一段时间内的运动轨迹。
*   **真实目的地**: 未来轨迹的终点，即车辆最终到达的位置。
*   **环境ROI (Region of Interest)**: 以当前车辆位置为中心，裁剪出的固定大小 (例如 128 米) 的环境地图区域，并缩放到固定像素尺寸 (例如 9x9 像素)。
    **重要更新**: 环境 ROI 现在是**序列化的**，即每个历史步长都有对应的 ROI 图像，而不是聚合为单个值。
*   **超级窗口 (Super Window)**: 为了提高效率，对于每个轨迹文件，会预先加载一个包含整条轨迹及其边缘缓冲区的环境地图大区域，后续的 ROI 裁剪都在这个“超级窗口”内进行。
*   **LMDB (Lightning Memory-Mapped Database)**: 一个高性能的键值存储数据库，用于存储处理后的样本，支持高效的随机访问和多进程读取。

## 3. 预处理流程图

```mermaid
graph TD
    A[开始: 参数解析与路径设置] --> B(收集轨迹CSV文件);
    B --> C{是否计算归一化统计量?};
    C -- 是 --> D[从文件子集计算归一化统计量 - Welford 在线算法];
    D --> E[保存归一化统计量到 .pkl 文件];
    E --> F[加载预计算的归一化统计量];
    F --> G[打开LMDB环境 - 训练集和验证集];
    G --> H{遍历每个轨迹CSV文件};
    H -- 对每个文件 --> I[加载原始轨迹并计算运动学特征];
    I --> J[根据轨迹地理范围加载环境超级窗口 - DEM, 地表覆盖, 坡度, 坡向];
    J --> K[滑动窗口提取观测-预测样本];
    K -- 对每个样本 --> L[聚合历史特征为单点向量];
    L --> M[提取并处理环境ROI序列为9x9像素 - 包含DEM, 坡度, 坡向三角编码, 地表覆盖One-Hot];
    M --> N[归一化样本特征 - 跳过One-Hot和三角编码特征，环境ROI序列不进行归一化];
    N --> O{随机分配到训练集或验证集LMDB};
    O -- 写入 --> P[写入样本到LMDB数据库];
    H -- 完成所有文件 --> Q[提交LMDB事务并关闭环境];
    Q --> R[结束: 预处理完成];

    style A fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style B fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style C fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style D fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style E fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style F fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style G fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style H fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style I fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style J fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style K fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style L fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style M fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style N fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style O fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style P fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style Q fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    style R fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;

    classDef default fill:#ffffff,stroke:#333,stroke-width:2px,color:#000,font-family:Microsoft YaHei,font-size:20px;
    linkStyle default stroke:#333,stroke-width:2px;
```

## 4. 详细步骤与逻辑

### 4.1 参数解析与路径设置

脚本通过 `argparse` 解析命令行参数。**请注意，大多数参数的默认值现在通过 `configs/data_preprocessing.yaml` 进行管理，命令行参数主要用于临时覆盖。** 它设置输入（轨迹文件目录、环境文件路径）和输出（LMDB数据库目录）的根目录。支持强制覆盖已有输出目录。

### 4.2 归一化统计量计算 (Welford 在线算法)

*   **目的**: 为了后续对数据进行 Z-score 归一化（使数据均值为0，标准差为1），需要预先计算所有特征的均值和标准差。
*   **方法**: 采用 Welford 在线算法。该算法无需一次性加载所有数据，而是逐步更新均值和方差，非常适合大规模数据集。
*   **过程**:
    1.  从所有轨迹文件中随机抽取一个子集 (`configs/data_preprocessing.yaml` 中的 `stats_files_sample_size` 参数控制数量，默认100)。
    2.  利用多进程并行处理这些文件。
    3.  对于每个文件，加载轨迹并计算运动学特征。
    4.  滑动窗口提取“观测-预测对”。
    5.  **聚合历史特征**: 对每个滑动窗口内的历史轨迹数据进行聚合：
        *   数值型特征 (x, y, 速度, 加速度, 曲率) 取均值。
        *   航向 (heading) 取起始点到结束点的矢量方向。
    6.  **重要更新**: 环境 ROI 现在是**序列化的原始像素数据** (`(C, H, W)` 格式的 NumPy 数组)，不再作为聚合特征计算平均值。因此，不会将 `dem_agg`, `slope_agg`, `aspect_sin_agg`, `aspect_cos_agg` 累加到 Welford 累加器中。
    7.  对离散型或已在 [-1, 1] 区间内的特征 (如地表覆盖 One-Hot 编码、坡向正弦/余弦) 不计算归一化统计量。
    8.  汇总所有进程的累加器，并最终计算出全局的均值和标准差。
*   **输出**: `normalization_stats.pkl` 文件，包含所有需要归一化特征的均值和标准差。

### 4.3 环境信息提取与编码

*   **超级窗口加载**: 对于每个轨迹文件，根据其地理范围，额外增加 `super_window_margin_m` (默认 200 米) 的边距，从原始 DEM、地表覆盖、坡度、坡向数据中裁剪出相应的“超级窗口”。这避免了对每个小样本重复加载地图数据。
*   **环境 ROI 提取与处理 (`get_environment_roi`)**:
    1.  以当前轨迹点的 x, y 坐标为中心，从“超级窗口”中裁剪出一个物理尺寸为 `map_roi_size_m` (默认 128 米) 的矩形区域。
    2.  将这个裁剪出的区域统一缩放到 **9x9 像素**。
    3.  **特征编码**:
        *   **DEM**: 直接缩放。
        *   **坡度 (Slope)**: 直接缩放。
        *   **坡向 (Aspect)**: 原始角度值转换为其 **正弦 (sin) 和 余弦 (cos)** 值，以解决角度的周期性不连续问题。
        *   **地表覆盖 (Land Cover)**: 对预定义的 `landcover_classes` (例如 [10, 20, 30, 40, 50, 60, 80, 90, 255]) 进行 **One-Hot 编码**，每个类别对应一个二进制通道。
    4.  **插值方法**:
        *   对 DEM、坡度、坡向的正弦/余弦等**数值型特征**使用**双线性插值** (`scipy.ndimage.zoom` 的 `order=1`) 进行缩放。
        *   对地表覆盖的 **One-Hot 编码通道**使用**最近邻插值** (`scipy.ndimage.zoom` 的 `order=0`)，以保持其离散性。
    5.  **输出**: 一个多通道的 NumPy 数组，形状为 `(num_channels, 9, 9)`，其中 `num_channels = 1 (DEM) + 1 (Slope) + 2 (Sin/Cos Aspect) + num_landcover_classes`。

### 4.4 运动学特征计算 (`calculate_kinematic_features`)

*   对每个原始轨迹点，计算其瞬时速度 (velocity_x, velocity_y)、加速度 (acceleration_x, acceleration_y)、航向 (heading) 和曲率 (curvature)。这些特征在滑动窗口聚合时使用。

### 4.5 样本生成与归一化

*   **滑动窗口**: 脚本在每个轨迹文件上使用滑动窗口 (`observation_horizon_s` 历史，`prediction_horizon_s` 未来) 来生成观测-预测样本。
*   **历史特征聚合**: 每个样本的“历史轨迹”部分被聚合为一个单点特征向量 (形状 `(1, 9)`)，包含 x, y, 速度, 加速度, 航向, 曲率, 地表覆盖众数等。
*   **未来轨迹与目的地**: “未来轨迹”是 `prediction_horizon_s` 时长内的 x, y 坐标序列；“目的地”是未来轨迹的最后一个点。
*   **归一化**: 加载预计算的 `normalization_stats.pkl`，对以下特征进行 Z-score 归一化：
    *   历史轨迹特征中的 x, y, 速度, 加速度, 航向, 曲率。
    *   真实未来轨迹和真实目的地的 x, y 坐标。
    *   **重要更新**: 环境 ROI 序列（包括 DEM, 坡度, 坡向）现在作为原始像素数据存储在样本中，**不进行 Z-score 归一化**。模型可以直接处理这些原始像素值。
    *   One-Hot 编码的特征跳过归一化。

### 4.6 数据存储到 LMDB

*   将归一化后的每个样本 (一个Python字典) 使用 `pickle` 序列化，然后存储到 LMDB 数据库中。
*   **训练集/验证集划分**: 每个样本会根据 `val_split_ratio` (默认 0.2) 随机分配到训练集 LMDB (`train.lmdb`) 或验证集 LMDB (`val.lmdb`)。
*   **批次提交**: 为了控制内存使用和提高写入效率，每处理 `commit_batch_size` (默认 10) 个文件后，会提交一次 LMDB 事务。

### 4.6 特征计算详解

数据预处理过程中涉及的特征主要分为两类：**运动学特征**和**环境特征**。

#### 4.6.1 运动学特征 (`src/data/trajectory_processor.py`)

这些特征是在原始轨迹数据（CSV 文件）加载后，在 `calculate_kinematic_features` 函数中逐点计算的。它假设时间步长是等间隔的（通过 `diff()` 操作隐式实现）。

*   **`x`, `y` (坐标)**:
    *   **来源**: 原始 CSV 文件中的 `x` 和 `y` 列。
    *   **计算**: 无需计算，直接读取。但在使用前会转换为数值类型，并使用 `fillna(method='ffill').fillna(method='bfill')` 来填充任何 NaN 值，确保数据连续性。

*   **`velocity_x` (东西向速度)**:
    *   **计算**: 当前点 `x` 坐标与前一个点 `x` 坐标的差值。
    *   **公式**: \(v_x(t) = x(t) - x(t-1)\)
    *   **代码**: `df['velocity_x'] = df['x'].diff().fillna(0)`。对于第一个点，由于没有前一个点，通常填充为 0。

*   **`velocity_y` (南北向速度)**:
    *   **计算**: 当前点 `y` 坐标与前一个点 `y` 坐标的差值。
    *   **公式**: \(v_y(t) = y(t) - y(t-1)\)
    *   **代码**: `df['velocity_y'] = df['y'].diff().fillna(0)`。对于第一个点，通常填充为 0。

*   **`acceleration_x` (东西向加速度)**:
    *   **计算**: 当前点 `velocity_x` 与前一个点 `velocity_x` 的差值。
    *   **公式**: \(a_x(t) = v_x(t) - v_x(t-1)\)
    *   **代码**: `df['acceleration_x'] = df['velocity_x'].diff().fillna(0)`。对于第一个点，通常填充为 0。

*   **`acceleration_y` (南北向加速度)**:
    *   **计算**: 当前点 `velocity_y` 与前一个点 `velocity_y` 的差值。
    *   **公式**: \(a_y(t) = v_y(t) - v_y(t-1)\)
    *   **代码**: `df['acceleration_y'] = df['velocity_y'].diff().fillna(0)`。对于第一个点，通常填充为 0。

*   **`heading` (瞬时航向，弧度)**:
    *   **计算**: 基于当前点的 `velocity_x` 和 `velocity_y` 计算的反正切值，表示速度向量的方向。
    *   **公式**: \(\text{heading}(t) = \text{arctan2}(v_y(t), v_x(t))\)
    *   **代码**: `df['heading'] = np.arctan2(df['velocity_y'], df['velocity_x']).fillna(0)`.
    *   **连续性处理**: `np.unwrap(df['heading'])` 用于处理角度从 360 度到 0 度（或 \(-\pi\) 到 \(\pi\)）的突变，使其在数值上连续。

*   **`heading_sin` (航向正弦)**:
    *   **计算**: 瞬时航向 `heading` 的正弦值。
    *   **公式**: \(\sin(\text{heading}(t))\)
    *   **代码**: `df['heading_sin'] = np.sin(df['heading'])`。

*   **`heading_cos` (航向余弦)**:
    *   **计算**: 瞬时航向 `heading` 的余弦值。
    *   **公式**: \(\cos(\text{heading}(t))\)
    *   **代码**: `df['heading_cos'] = np.cos(df['heading'])`。
    *   **目的**: 使用正弦和余弦分量可以避免角度的周期性问题，更适合神经网络处理。

*   **`curvature` (曲率)**:
    *   **计算**: 轨迹弯曲的程度。通常通过航向变化率除以速度来近似。
    *   **公式**: \(\text{curvature}(t) = \\frac{\text{heading}(t) - \text{heading}(t-1)}{\text{speed}(t)}\)
    *   **其中速度 `speed`**: \(\text{speed}(t) = \sqrt{v_x(t)^2 + v_y(t)^2}\)
    *   **代码**:
        ```python
        speed = np.sqrt(df['velocity_x']**2 + df['velocity_y']**2)
        df['curvature'] = np.where(speed > 1e-6, df['heading'].diff().fillna(0) / speed, 0)
        ```
        这里 `np.where(speed > 1e-6, ..., 0)` 用于避免速度接近零时除以零的问题，此时曲率被设为 0。

#### 4.6.2 聚合特征 (`src/scripts/01_preprocess_data_v1.py`)

在 `_create_lmdb_dataset_worker` 和 `preprocess_and_save_to_lmdb` 函数中，对原始轨迹数据进行了滑动窗口采样和特征聚合。这里的特征是针对一个聚合窗口（而不是单个时间步）计算的。

*   **`x_agg`, `y_agg` (聚合坐标)**:
    *   **计算**: 在每个聚合窗口 (`points_in_agg_window`) 内，`x` 和 `y` 坐标的平均值。
    *   **公式**: \(\text{avg}(x)\), \(\text{avg}(y)\)
    *   **代码**: `temp_agg_features['x'] = obs_agg_window_slice['x'].mean()`

*   **`velocity_x_agg`, `velocity_y_agg`, `acceleration_x_agg`, `acceleration_y_agg`, `curvature_agg`**:
    *   **计算**: 在每个聚合窗口内，相应运动学特征的平均值。
    *   **代码**: `temp_agg_features['velocity_x'] = obs_agg_window_slice['velocity_x'].mean()`，以此类推。

*   **`heading_sin_agg`, `heading_cos_agg` (聚合航向)**:
    *   **计算**: 聚合窗口内的起始点和结束点之间的方向。
    *   **公式**:
        *   \(\Delta x = \text{end\_x} - \text{start\_x}\)
        *   \(\Delta y = \text{end\_y} - \text{start\_y}\)
        *   \(\text{agg\_heading\_rad} = \text{arctan2}(\Delta y, \Delta x)\)
        *   \(\text{heading\_sin\_agg} = \sin(\text{agg\_heading\_rad})\)
        *   \(\text{heading\_cos\_agg} = \cos(\text{agg\_heading\_rad})\)
    *   **目的**: 这种聚合航向更能代表整个窗口内的运动趋势，而不是瞬时波动。

#### 4.6.3 环境特征 (`src/data/environment_processor.py` 和 `scripts/data_prep/preprocess_main.py`)

这些特征是在 `_load_super_window` 加载 GIS 栅格数据后，通过 `get_environment_roi` 函数提取的。

*   **`dem_agg`, `slope_agg`, `aspect_sin_agg`, `aspect_cos_agg`**: (已废弃，现在使用 `environment_roi_sequence`)
    *   **重要更新**: 这些聚合的环境特征（例如 DEM 平均值、坡度平均值等）在最新版本中已不再作为历史特征的一部分进行聚合。取而代之的是，原始的 9x9 像素环境 ROI 序列（包含 DEM、坡度、坡向的通道）直接存储在每个样本中，无需归一化。

*   **`lc_class_X` (地表覆盖类别 One-Hot 编码)**:
    *   **来源**: 从 `data/environment/landcover_aligned.tif` 等地表覆盖栅格文件中提取。
    *   **计算**: 在每个聚合窗口内，找出地表覆盖类别的**众数 (mode)**。然后，将这个众数转换为 One-Hot 编码的向量。例如，如果众数是类别 50（建筑用地），那么 `lc_class_50` 为 1.0，其他 `lc_class_X` 为 0.0。
    *   **代码**:
        ```python
        if 'land_cover_mode' in obs_agg_window_slice.columns and not obs_agg_window_slice['land_cover_mode'].empty:
            mode_lc = obs_agg_window_slice['land_cover_mode'].mode()
            if not mode_lc.empty:
                mode_lc_val = mode_lc[0]
                for cls in landcover_classes:
                    temp_agg_features[f'lc_class_{cls}'] = 1.0 if mode_lc_val == cls else 0.0
        else: # 如果缺少land_cover_mode列，则所有类别设为0
            for cls in landcover_classes:
                temp_agg_features[f'lc_class_{cls}'] = 0.0
        ```
    *   **目的**: 将离散的类别信息转化为模型可处理的数值形式。

#### 4.6.4 目标点特征

*   **`ground_truth_destination` (真实目的地)**:
    *   **来源**: 未来轨迹 `future_original_slice` 的最后一个点的原始 `x` 和 `y` 坐标。
    *   **计算**: 直接提取。
    *   **代码**: `ground_truth_destination_raw = np.array([future_original_slice.iloc[-1]['x'], future_original_slice.iloc[-1]['y']], dtype=np.float32)`

*   **`ground_truth_trajectory` (真实未来轨迹)**:
    *   **来源**: 未来轨迹 `future_original_slice` 经过聚合后的 `x` 和 `y` 坐标序列。
    *   **计算**: 与历史轨迹聚合类似，对未来轨迹的 `x` 和 `y` 坐标进行平均聚合。
    *   **代码**:
        ```python
        aggregated_future_trajectory.append([
            pred_agg_window_slice['x'].mean(),
            pred_agg_window_slice['y'].mean()
        ])
        ```

## 5. 脚本运行与参数

可以通过以下命令运行脚本:

```bash
conda activate wargame
python scripts/data_prep/preprocess_main.py [参数]
```

**常用参数说明**:
**请注意：以下参数的默认值和推荐值在 `configs/data_preprocessing.yaml` 中配置。命令行参数将覆盖配置文件中的值。**

*   `--base_dir`: 数据存储的根目录 (默认 `data`)。在 `configs/data_preprocessing.yaml` 中配置为 `base_data_path`。
*   `--trajectory_dir`: 原始轨迹CSV文件目录，相对于 `base_dir` (默认 `trajectories_with_env`)。在 `configs/data_preprocessing.yaml` 中配置为 `trajectory_path`。
*   `--output_dir`: 输出LMDB目录，相对于 `base_dir` (默认 `processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq`)。**建议根据生成的数据特性更改此名称，例如 `processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq_full`。** 在 `configs/data_preprocessing.yaml` 中配置为 `output_path`。
*   `--environment`: 环境数据类型，`real` (真实环境) 或 `flat` (全零环境，用于调试，默认 `real`)。在 `configs/data_preprocessing.yaml` 中配置为 `environment_type`。
*   `--force`: 如果输出目录已存在，则强制覆盖 (会删除原有内容，请谨慎使用)。在 `configs/data_preprocessing.yaml` 中配置为 `clear_existing_data`。
*   `--num_files`: 要处理的轨迹文件数量 (默认 `0` 表示处理所有文件)。**如果只处理部分文件，数据集大小会相应减小。** 在 `configs/data_preprocessing.yaml` 中配置为 `num_files_to_process`。
*   `--num_workers`: 用于并行处理的进程数 (默认 CPU 核心数的一半)。在 `configs/data_preprocessing.yaml` 中配置为 `num_processing_workers`。
*   `--val_split_ratio`: 用于验证集的样本比例 (默认 `0.2`)。在 `configs/data_preprocessing.yaml` 中配置为 `val_split_ratio`。
*   `--debug_one_file`: 只处理一个文件进行调试并输出详细日志 (优先级高于 `num_files`)。**如果启用此标志，只会处理第一个文件。** 在 `configs/data_preprocessing.yaml` 中配置为 `debug_one_file`。
*   `--stats_files_sample_size`: 用于计算归一化统计量的文件样本数量 (默认 `100`)。在 `configs/data_preprocessing.yaml` 中配置为 `stats_files_sample_size`。
*   `--commit_batch_size`: 每处理N个文件后提交一次LMDB事务 (默认 `10`)。在 `configs/data_preprocessing.yaml` 中配置为 `commit_batch_size`。

## 6. 数据集大小问题排查

如果处理后的数据集大小比预期小，请重点检查以下两点:

1.  **配置文件参数**:
    *   在 `configs/data_preprocessing.yaml` 中，确保 `num_files_to_process` 设置为 `0` (或不设置)，以处理所有文件。
    *   确保 `debug_one_file` 标志没有被设置为 `True`。
2.  **原始轨迹文件长度和采样率**:
    *   每个样本需要 10 秒观测数据和 4 秒预测数据。如果原始 CSV 文件长度不足 (即点数少于 `(10 + 4) * trajectory_resample_rate_hz` )，则无法生成样本。
    *   `configs/data_preprocessing.yaml` 中的 `trajectory_resample_rate_hz` 必须与您实际轨迹数据的采样率一致。如果不一致，会错误计算所需的点数，导致大量样本被跳过。

通过检查上述内容，您可以更好地理解和控制数据预处理过程。 