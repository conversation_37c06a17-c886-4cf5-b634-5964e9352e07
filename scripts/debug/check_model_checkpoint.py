import torch
import os

checkpoint_path = 'checkpoints/v1/V1_CNN_RNN_Fusion_Batch256_LR0.0001_20250722_133435/best_model.pth'

if not os.path.exists(checkpoint_path):
    print(f"错误：找不到模型检查点文件: {checkpoint_path}")
else:
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        print(f"模型检查点文件 {checkpoint_path} 中的键：")
        for key in checkpoint.keys():
            print(f"  - {key}")
    except Exception as e:
        print(f"加载模型检查点时发生错误: {e}") 