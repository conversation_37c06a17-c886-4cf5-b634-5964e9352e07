import lmdb
import argparse

def count_lmdb_samples(lmdb_path):
    """
    计算LMDB数据库中的样本总数。
    """
    count = 0
    try:
        env = lmdb.open(lmdb_path, readonly=True, lock=False, readahead=False, meminit=False)
        with env.begin(write=False) as txn:
            cursor = txn.cursor()
            for key, _ in cursor:
                count += 1
        print(f"LMDB数据库 '{lmdb_path}' 中包含 {count} 个样本。")
    except lmdb.Error as e:
        print(f"读取LMDB数据库时出错: {e}")
    except Exception as e:
        print(f"计数过程中出现错误: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="计算LMDB数据库中的样本总数。")
    parser.add_argument('--lmdb_dir', type=str, default='data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train',
                        help='要检查的LMDB数据库的路径，例如 data/processed_lmdb/train。')
    args = parser.parse_args()
    count_lmdb_samples(args.lmdb_dir) 