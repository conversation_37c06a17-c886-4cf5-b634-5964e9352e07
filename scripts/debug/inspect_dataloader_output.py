import torch
from torch.utils.data import DataLoader
from pathlib import Path
import sys

# 将项目根目录添加到sys.path
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

from src.data.datasets import LMDBDataset
from src.data.collate import custom_collate_fn
from src.utils.config_loader import load_config

def inspect_dataloader_batch(config):
    """
    初始化 DataLoader 并检查第一个批次的形状。
    """
    print("--- 正在初始化 DataLoader 以进行检查 ---")
    
    # 使用验证集进行检查
    try:
        dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    except Exception as e:
        print(f"初始化 LMDBDataset 时出错: {e}")
        print("请确保你已经成功运行了 V3 版本的数据预处理脚本。")
        return
    
    if len(dataset) == 0:
        print("错误: 数据集为空。无法检查 DataLoader 输出。")
        return

    # 实例化 DataLoader
    data_loader = DataLoader(
        dataset,
        batch_size=config.training.batch_size_val,
        shuffle=False,  # 检查时无需打乱
        num_workers=0,  # 使用0个工作进程以便于调试
        collate_fn=custom_collate_fn
    )

    print(f"数据集大小: {len(dataset)} 个样本")
    print(f"批处理大小: {config.training.batch_size_val}")
    print("--- 正在获取第一个批次 ---")

    try:
        first_batch = next(iter(data_loader))
        
        print("\n--- 正在检查第一个批次的形状 ---")
        for key, value in first_batch.items():
            if isinstance(value, torch.Tensor):
                print(f"键: '{key}', 类型: Tensor, 形状: {value.shape}")
            elif isinstance(value, list):
                # 对于像 original_history_points 这样的可变长度项
                print(f"键: '{key}', 类型: List, 长度: {len(value)}")
                if value and isinstance(value[0], torch.Tensor):
                    print(f"  - 第一个元素的形状: {value[0].shape}")
            else:
                print(f"键: '{key}', 类型: {type(value)}")
        
        print("\n--- 'environment_roi' 的详细检查 ---")
        if 'environment_roi' in first_batch:
            env_roi = first_batch['environment_roi']
            print(f"'environment_roi' 的形状: {env_roi.shape}")
            
            # 从配置中计算预期的形状
            roi_grid_size = config.data_preprocessing.environment.roi_size_m // config.data_preprocessing.environment.grid_resolution_m
            expected_shape_per_sample = (
                config.data_preprocessing.masking.max_history_len_s,
                config.data_preprocessing.environment.num_features,
                roi_grid_size,
                roi_grid_size
            )
            
            expected_batch_shape = (
                config.training.batch_size_val, 
                *expected_shape_per_sample
            )
            
            print(f"每个样本的预期形状: {expected_shape_per_sample}")
            print(f"预期的批处理形状: {expected_batch_shape}")
            
            if env_roi.dim() == 5 and env_roi.shape == expected_batch_shape:
                 print("✅ 'environment_roi' 的维度(5D)和形状符合预期。")
            else:
                 print(f"🔥 'environment_roi' 的维度是 {env_roi.dim()} (预期为5D)，形状是 {env_roi.shape} (预期为 {expected_batch_shape})。")

    except Exception as e:
        print(f"\n--- 在获取/检查批处理时发生错误 ---")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("--- 开始数据加载器输出检查脚本 ---")
    # 加载配置
    try:
        cfg = load_config(
            'configs/main_config.yaml', 
            'configs/data_preprocessing.yaml',
            'configs/models/v3_masked_autoregressive_model.yaml'
        )
        inspect_dataloader_batch(cfg)
    except FileNotFoundError as e:
        print(f"错误: 找不到配置文件: {e}")
    except Exception as e:
        print(f"加载配置或运行时发生未知错误: {e}") 