import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import logging
import pandas as pd
import random

# --- 增强版字体设置，优先使用Linux下的中文字体 ---
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

from src.utils.config_loader import load_config
from src.utils.normalization import load_normalization_stats
from scripts.preprocessing.sample_generator import generate_samples_for_file

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def visualize_single_sample_with_gaps(sample, stats, save_path):
    """
    可视化单个生成样本的结构，并明确绘制出观测间歇。
    """
    def denormalize(coords, key_prefix):
        mean_x = stats[f'{key_prefix}_mean']['x']
        std_x = stats[f'{key_prefix}_std']['x']
        mean_y = stats[f'{key_prefix}_mean']['y']
        std_y = stats[f'{key_prefix}_std']['y']
        
        real_x = coords[..., 0] * std_x + mean_x
        real_y = coords[..., 1] * std_y + mean_y
        return np.stack([real_x, real_y], axis=-1)

    mask = sample['history_mask']
    future_norm = sample['ground_truth_trajectory']
    history_points = sample['original_history_points']
    future_real = denormalize(future_norm, 'target')

    fig, ax = plt.subplots(figsize=(18, 10))

    # --- 修正后的绘图逻辑 V3 (最终版) ---
    history_cursor = 0
    is_first_gap = True

    # 1. 找到所有观测段(mask==1)的起止位置
    last_obs_idx_in_mask = np.where(mask == 1)[0][-1] if 1 in mask else -1
    
    if last_obs_idx_in_mask != -1 and history_points.size > 0:
        effective_mask = mask[:last_obs_idx_in_mask + 1]
        d = np.diff(effective_mask, prepend=0, append=0)
        seg_starts = np.where(d == 1)[0]
        seg_ends = np.where(d == -1)[0]

        # 2. 遍历所有观测段并绘制
        for i in range(len(seg_starts)):
            # 提取当前观测段的数据
            start_idx = seg_starts[i]
            end_idx = seg_ends[i]
            segment_len = end_idx - start_idx
            segment_data = history_points[history_cursor : history_cursor + segment_len]

            if segment_data.size > 0:
                # 绘制观测段
                label = '观测窗口' if i == 0 else ""
                ax.plot(segment_data[:, 0], segment_data[:, 1], 'g-', linewidth=4, alpha=0.8, label=label)

                # 如果这不是第一个观测段，绘制内部间歇连线
                if i > 0:
                    prev_segment_end_point = history_points[history_cursor - 1]
                    current_segment_start_point = segment_data[0]
                    gap_connection = np.array([prev_segment_end_point, current_segment_start_point])
                    gap_label = '观测间歇' if is_first_gap else ""
                    logging.info(f"绘制内部间歇连线: 从 {prev_segment_end_point} 到 {current_segment_start_point}")
                    # 使用高对比度、清晰的线型
                    ax.plot(gap_connection[:, 0], gap_connection[:, 1], color='#FF00FF', linestyle='--', linewidth=2.5, label=gap_label)
                    is_first_gap = False
                
                history_cursor += segment_len
    
    # 绘制未来真实轨迹
    ax.plot(future_real[:, 0], future_real[:, 1], 'b-', linewidth=3, label='未来真实轨迹 (标签)')

    # --- 最终绘制间歇连线，确保其在最顶层 ---
    # 重新计算内部间歇并绘制
    if last_obs_idx_in_mask != -1 and history_points.size > 0:
        history_cursor = 0
        is_first_gap = True # 重置
        for i in range(len(seg_starts)):
            segment_len = seg_ends[i] - seg_starts[i]
            if i > 0:
                prev_segment_end_point = history_points[history_cursor - 1]
                current_segment_start_point = history_points[history_cursor]
                gap_connection = np.array([prev_segment_end_point, current_segment_start_point])
                gap_duration_points = seg_starts[i] - seg_ends[i-1]
                logging.info(f"绘制【内部间歇】- 时长: {gap_duration_points}秒, 起点: {prev_segment_end_point}, 终点: {current_segment_start_point}")
                gap_label = '观测间歇' if is_first_gap else ""
                ax.plot(gap_connection[:, 0], gap_connection[:, 1], color='#FF00FF', linestyle='--', linewidth=2.5, label=gap_label, zorder=15)
                # 绘制高亮标记点
                ax.scatter(prev_segment_end_point[0], prev_segment_end_point[1], c='yellow', s=120, marker='o', zorder=16, label='间歇转折点' if is_first_gap else "", edgecolor='black', alpha=0.8)
                ax.scatter(current_segment_start_point[0], current_segment_start_point[1], c='yellow', s=120, marker='o', zorder=16, edgecolor='black', alpha=0.8)
                is_first_gap = False
            history_cursor += segment_len

    # 绘制连接历史和未来的最后间歇
    if history_points.size > 0 and future_real.size > 0:
        last_history_point = history_points[-1]
        first_future_point = future_real[0]
        # 从样本中获取真实间歇时长 (需要修改sample_generator)
        # 暂时在日志中标记为未知
        logging.info(f"绘制【历史->未来间歇】- 起点: {last_history_point}, 终点: {first_future_point}")
        gap_connection = np.array([last_history_point, first_future_point])
        gap_label = '观测间歇' if 'is_first_gap' not in locals() or is_first_gap else ""
        ax.plot(gap_connection[:, 0], gap_connection[:, 1], color='#FF00FF', linestyle='--', linewidth=2.5, label=gap_label, zorder=15)
        # 绘制高亮标记点
        ax.scatter(last_history_point[0], last_history_point[1], c='yellow', s=120, marker='o', zorder=16, label='间歇转折点' if 'is_first_gap' not in locals() or is_first_gap else "", edgecolor='black', alpha=0.8)
        ax.scatter(first_future_point[0], first_future_point[1], c='yellow', s=120, marker='o', zorder=16, edgecolor='black', alpha=0.8)


    # 标记关键点 (zorder设高，确保在最顶层)
    if len(history_points) > 0:
        ax.scatter(history_points[0, 0], history_points[0, 1], c='cyan', s=200, marker='s', label='历史起点', zorder=20, edgecolor='black')
        ax.scatter(history_points[-1, 0], history_points[-1, 1], c='orange', s=250, marker='X', label='预测事件点', zorder=20, edgecolor='black')
    
    ax.scatter(future_real[0, 0], future_real[0, 1], c='magenta', s=200, marker='>', label='预测起点', zorder=20, edgecolor='black')
    ax.scatter(future_real[-1, 0], future_real[-1, 1], c='red', s=250, marker='*', label='未来终点', zorder=20, edgecolor='black')
    
    # 整理图例
    handles, labels = ax.get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    # 过滤掉重复的'间歇转折点'标签
    final_labels = {}
    for label, handle in by_label.items():
        if label not in final_labels:
            final_labels[label] = handle
    ax.legend(final_labels.values(), final_labels.keys(), fontsize=16)
    
    # --- 最终的、强制性的间歇重绘，确保可见 ---
    # 历史内部间歇
    if 'seg_starts' in locals() and len(seg_starts) > 1:
         history_cursor = 0
         for i in range(len(seg_starts)):
            segment_len = seg_ends[i] - seg_starts[i]
            if i > 0:
                prev_segment_end_point = history_points[history_cursor - 1]
                current_segment_start_point = history_points[history_cursor]
                gap_connection = np.array([prev_segment_end_point, current_segment_start_point])
                ax.plot(gap_connection[:, 0], gap_connection[:, 1], color='red', linestyle='-', linewidth=5, zorder=99)
            history_cursor += segment_len
    # 历史->未来间歇
    if history_points.size > 0 and future_real.size > 0:
        last_history_point = history_points[-1]
        first_future_point = future_real[0]
        gap_connection = np.array([last_history_point, first_future_point])
        ax.plot(gap_connection[:, 0], gap_connection[:, 1], color='red', linestyle='-', linewidth=5, zorder=99)

    ax.set_title(f"增强版样本逻辑检查 - 文件: {Path(sample['file_path']).name}", fontsize=22)
    ax.set_xlabel("X 坐标 (米)", fontsize=20)
    ax.set_ylabel("Y 坐标 (米)", fontsize=20)
    ax.grid(True, linestyle='--', alpha=0.6)
    ax.set_aspect('equal', adjustable='box')
    ax.tick_params(labelsize=18)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150)
    logging.info(f"可视化图表已保存至: {save_path}")
    plt.show()

def main():
    """主函数"""
    logging.info("--- 开始检查新的样本生成逻辑 (增强版可视化) ---")
    
    config = load_config('configs/main_config.yaml', 'configs/data_preprocessing.yaml')
    stats_path = Path("data/processed_lmdb_obs_5min_pred_40min_v3_with_mask/normalization_stats.pkl")
    if not stats_path.exists():
        logging.error(f"归一化文件未找到: {stats_path}")
        return
    stats = load_normalization_stats(stats_path)
    
    data_dir = Path(config.data_preprocessing.trajectory.path)
    all_files = list(data_dir.glob("*.csv"))
    if not all_files:
        logging.error(f"在 {data_dir} 中未找到任何 .csv 轨迹文件。")
        return
        
    random.shuffle(all_files)
    
    # --- 优化后的样本查找逻辑 V2 ---
    sample_to_check = None
    last_good_sample = None
    logging.info("正在查找包含内部观测间歇的样本以进行清晰的可视化...")
    
    for file in all_files:
        logging.info(f"正在分析文件: {file.name}...")
        generated_samples = generate_samples_for_file(file, config, stats)
        
        if not generated_samples:
            logging.warning(f"文件 {file.name} 未能生成任何样本，继续尝试...")
            continue

        if not last_good_sample:
            last_good_sample = random.choice(generated_samples)

        # 优先寻找包含内部间歇的样本
        for s in generated_samples:
            mask = s['history_mask']
            last_obs_idx_in_mask = np.where(mask == 1)[0][-1] if 1 in mask else -1
            if last_obs_idx_in_mask == -1:
                continue
            effective_mask = mask[:last_obs_idx_in_mask + 1]
            d = np.diff(effective_mask, prepend=0, append=0)
            seg_starts = np.where(d == 1)[0]
            
            # 如果观测段数量大于1，说明有内部间歇
            if len(seg_starts) > 1:
                sample_to_check = s
                logging.info(f"成功在文件 {file.name} 中找到一个包含内部观测间歇的样本！")
                break
        if sample_to_check:
            break

    if not sample_to_check:
        logging.warning("遍历了多个文件后，未能找到包含内部间歇的样本。将使用一个随机样本进行展示。")
        sample_to_check = last_good_sample
        if not sample_to_check:
             logging.error("没有任何可供展示的样本。")
             return

    logging.info("\n--- 正在检查选定样本的结构 ---")
    for key, value in sample_to_check.items():
        if isinstance(value, np.ndarray):
            print(f"  - 键: '{key}', 类型: ndarray, 形状: {value.shape}, 数据类型: {value.dtype}")
        else:
            print(f"  - 键: '{key}', 类型: {type(value).__name__}, 值: {value}")
            
    output_dir = Path("outputs/debug_visualization")
    output_dir.mkdir(parents=True, exist_ok=True)
    save_path = output_dir / "enhanced_sample_logic_check.png"
    
    logging.info(f"\n--- 正在生成增强版可视化图表... ---")
    visualize_single_sample_with_gaps(sample_to_check, stats, save_path)

if __name__ == "__main__":
    main() 