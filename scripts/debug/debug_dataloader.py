#!/usr/bin/env python3
"""
调试数据加载器
"""
import lmdb
import pickle
import torch
import numpy as np
from src.configs.v1_grid_classifier_config import config
from src.data.datasets import TrajectoryDataset

def debug_raw_lmdb():
    """检查原始LMDB数据"""
    print("=== 原始LMDB数据检查 ===")
    env = lmdb.open('data/preprocessed/train.lmdb', readonly=True)
    with env.begin() as txn:
        cursor = txn.cursor()
        
        samples_by_traj = {}
        for key, value in cursor:
            key_str = key.decode('ascii')
            traj_id = key_str.split('_')[0]
            if traj_id not in samples_by_traj:
                data = pickle.loads(value)
                dest = data['final_destination']
                samples_by_traj[traj_id] = dest
                print(f'轨迹{traj_id}原始终点: {dest}')
            if len(samples_by_traj) >= 8:
                break
    
    return samples_by_traj

def debug_dataset_processing():
    """检查Dataset类的处理过程"""
    print("\n=== Dataset处理过程检查 ===")
    
    # 创建数据集
    dataset = TrajectoryDataset(config.LMDB_PATH, config)
    
    # 检查归一化参数
    print(f"dest_mean: {dataset.dest_mean}")
    print(f"dest_std: {dataset.dest_std}")
    
    # 手动检查几个样本
    print("\n检查Dataset返回的样本:")
    for i in range(5):
        sample = dataset[i]
        dest_gt = sample['dest_gt']
        intent_label = sample['intent_label_gt']
        print(f"样本{i}: dest_gt={dest_gt}, intent_label={intent_label}")
    
    # 手动模拟处理过程
    print("\n=== 手动模拟处理过程 ===")
    
    # 直接从LMDB读取同样的样本
    env = lmdb.open(config.LMDB_PATH, readonly=True)
    with env.begin() as txn:
        keys = [key for key, _ in txn.cursor()]
        
        for i in range(5):
            raw_data = pickle.loads(txn.get(keys[i]))
            raw_dest = raw_data['final_destination']
            key_str = keys[i].decode('ascii')
            
            print(f"\n--- 样本 {i} (key: {key_str}) ---")
            print(f"原始final_destination: {raw_dest}")
            
            # 手动执行归一化
            destination_tensor = torch.from_numpy(raw_dest).float()
            norm_destination = (destination_tensor - dataset.dest_mean) / dataset.dest_std
            print(f"归一化后dest_gt: {norm_destination}")
            
            # 手动计算intent_label
            grid_size = config.INTENT_GRID_SIZE
            map_bounds = config.MAP_WORLD_BOUNDS
            
            x_span = map_bounds[2] - map_bounds[0]
            y_span = map_bounds[3] - map_bounds[1]
            
            grid_x = np.floor((raw_dest[0] - map_bounds[0]) / x_span * grid_size[0])
            grid_y = np.floor((raw_dest[1] - map_bounds[1]) / y_span * grid_size[1])
            
            grid_x = np.clip(grid_x, 0, grid_size[0] - 1)
            grid_y = np.clip(grid_y, 0, grid_size[1] - 1)
            
            intent_label = grid_y * grid_size[0] + grid_x
            print(f"计算的grid坐标: ({grid_x}, {grid_y})")
            print(f"计算的intent_label: {intent_label}")

def main():
    raw_data = debug_raw_lmdb()
    debug_dataset_processing()

if __name__ == '__main__':
    main() 