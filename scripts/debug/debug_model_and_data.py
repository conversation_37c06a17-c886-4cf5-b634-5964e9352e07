#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：检查V1模型的数据和结构问题
创建时间: 2024/07/16
功能: 深入分析数据样本、模型结构和训练过程中的问题
"""

import torch
import torch.nn as nn
import pickle
import numpy as np
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 导入我们的模块
from src.configs.v1_grid_classifier_config import config
from src.data.datasets import TrajectoryDataset
from src.models.v1_grid_classifier.end_to_end_model import GridClassifierV1

def load_normalization_stats():
    """加载归一化统计数据"""
    stats_path = 'data/preprocessed/normalization_stats.pkl'
    with open(stats_path, 'rb') as f:
        stats = pickle.load(f)
    return stats

def analyze_data_samples():
    """分析数据样本"""
    print("="*60)
    print("🔍 数据样本分析")
    print("="*60)
    
    # 加载数据集
    train_dataset = TrajectoryDataset(lmdb_path=config.LMDB_PATH, config=config)
    
    print(f"数据集大小: {len(train_dataset)}")
    
    # 检查前5个样本
    for i in range(min(5, len(train_dataset))):
        sample = train_dataset[i]
        print(f"\n--- 样本 {i} ---")
        print(f"history_traj shape: {sample['history_traj'].shape}")
        print(f"future_traj_gt shape: {sample['future_traj_gt'].shape}")
        print(f"roi shape: {sample['roi'].shape}")
        print(f"dest_gt shape: {sample['dest_gt'].shape}")
        print(f"intent_label_gt: {sample['intent_label_gt']}")
        
        # 检查数据范围
        print(f"history_traj范围: [{sample['history_traj'].min():.3f}, {sample['history_traj'].max():.3f}]")
        print(f"future_traj_gt范围: [{sample['future_traj_gt'].min():.3f}, {sample['future_traj_gt'].max():.3f}]")
        print(f"dest_gt值: {sample['dest_gt']}")
        print(f"roi范围: [{sample['roi'].min():.3f}, {sample['roi'].max():.3f}]")
        
        # 检查是否包含NaN或Inf
        for key, tensor in sample.items():
            if torch.isnan(tensor).any():
                print(f"⚠️ {key} 包含NaN值!")
            if torch.isinf(tensor).any():
                print(f"⚠️ {key} 包含Inf值!")

def analyze_model_structure():
    """分析模型结构"""
    print("\n" + "="*60)
    print("🏗️ 模型结构分析")
    print("="*60)
    
    # 加载归一化参数
    stats = load_normalization_stats()
    dest_mean = torch.tensor(stats['mean'][:2], dtype=torch.float32)
    dest_std = torch.tensor(stats['std'][:2], dtype=torch.float32)
    
    # 创建模型
    model = GridClassifierV1(config, dest_mean, dest_std)
    
    print(f"模型参数总数: {sum(p.numel() for p in model.parameters()):,}")
    print(f"可训练参数数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 检查各个组件的输出尺寸
    print("\n--- 组件输出尺寸测试 ---")
    
    # 创建虚拟输入
    batch_size = 2
    history_traj = torch.randn(batch_size, config.HISTORY_LEN, config.TRAJ_INPUT_DIM)
    roi = torch.randn(batch_size, config.ENV_ENCODER_INPUT_CHANNELS, config.ROI_PIXEL_SIZE, config.ROI_PIXEL_SIZE)
    dest_gt = torch.randn(batch_size, 2)
    
    print(f"输入尺寸:")
    print(f"  history_traj: {history_traj.shape}")
    print(f"  roi: {roi.shape}")
    print(f"  dest_gt: {dest_gt.shape}")
    
    model.eval()
    with torch.no_grad():
        # 测试轨迹编码器
        traj_features = model.traj_encoder(history_traj)
        print(f"轨迹编码器输出: {traj_features.shape}")
        
        # 测试环境编码器
        env_features = model.env_encoder(roi)
        env_embedding = torch.flatten(env_features, 1)
        print(f"环境编码器输出: {env_features.shape}")
        print(f"环境编码器展平后: {env_embedding.shape}")
        
        # 测试融合层
        traj_embedding = traj_features[:, -1, :]
        fused_embedding = model.fusion_mlp(torch.cat([traj_embedding, env_embedding], dim=1))
        print(f"融合层输出: {fused_embedding.shape}")
        
        # 测试意图分类
        intent_logits = model.intent_classifier(fused_embedding)
        print(f"意图分类输出: {intent_logits.shape}")
        
        # 测试完整前向传播
        output = model(history_traj, roi, dest_gt=dest_gt)
        print(f"GMM参数输出: {output['gmm_params_flat'].shape}")
        print(f"预测目标坐标: {output['predicted_goal_coords'].shape}")

def test_data_flow():
    """测试数据流和损失计算"""
    print("\n" + "="*60)
    print("🔄 数据流测试")
    print("="*60)
    
    # 加载数据
    train_dataset = TrajectoryDataset(lmdb_path=config.LMDB_PATH, config=config)
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=False)
    
    # 加载模型
    stats = load_normalization_stats()
    dest_mean = torch.tensor(stats['mean'][:2], dtype=torch.float32)
    dest_std = torch.tensor(stats['std'][:2], dtype=torch.float32)
    model = GridClassifierV1(config, dest_mean, dest_std)
    
    # 获取一个批次
    batch = next(iter(train_loader))
    
    print("批次数据:")
    for key, value in batch.items():
        print(f"  {key}: {value.shape}")
        if torch.isnan(value).any():
            print(f"    ⚠️ {key} 包含NaN!")
        if torch.isinf(value).any():
            print(f"    ⚠️ {key} 包含Inf!")
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        try:
            output = model(
                batch['history_traj'], 
                batch['roi'], 
                dest_gt=batch['dest_gt']
            )
            print("\n前向传播成功!")
            print(f"输出keys: {output.keys()}")
            for key, value in output.items():
                print(f"  {key}: {value.shape}")
                if torch.isnan(value).any():
                    print(f"    ⚠️ {key} 包含NaN!")
        except Exception as e:
            print(f"\n❌ 前向传播失败: {e}")
            import traceback
            traceback.print_exc()

def analyze_training_logic():
    """分析训练逻辑中的问题"""
    print("\n" + "="*60)
    print("🎯 训练逻辑分析")
    print("="*60)
    
    # 模拟训练器的损失计算过程
    from src.engine.trainer import Trainer
    
    train_dataset = TrajectoryDataset(lmdb_path=config.LMDB_PATH, config=config)
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=False)
    
    stats = load_normalization_stats()
    dest_mean = torch.tensor(stats['mean'][:2], dtype=torch.float32)
    dest_std = torch.tensor(stats['std'][:2], dtype=torch.float32)
    model = GridClassifierV1(config, dest_mean, dest_std)
    
    # 创建临时训练器实例
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    trainer = Trainer(model, config, train_loader, train_loader, optimizer, device='cpu')
    
    # 获取一个批次
    batch = next(iter(train_loader))
    
    try:
        # 测试损失计算
        model.train()
        output = model(
            batch['history_traj'], 
            batch['roi'], 
            dest_gt=batch['dest_gt']
        )
        
        # 计算意图损失
        intent_loss = trainer.intent_loss_fn(output['intent_logits'], batch['intent_label_gt'])
        print(f"意图分类损失: {intent_loss.item():.4f}")
        
        # 计算轨迹损失
        gt_displacements = trainer._calculate_displacements(batch['history_traj'], batch['future_traj_gt'])
        gt_displacements_flat = gt_displacements.view(-1, 2)
        
        print(f"真实位移shape: {gt_displacements.shape}")
        print(f"真实位移展平后: {gt_displacements_flat.shape}")
        print(f"GMM参数shape: {output['gmm_params_flat'].shape}")
        
        pi, mu, sigma, rho = model.parse_gmm_params(output['gmm_params_flat'])
        print(f"解析后的GMM参数:")
        print(f"  pi: {pi.shape}, 范围: [{pi.min():.3f}, {pi.max():.3f}]")
        print(f"  mu: {mu.shape}, 范围: [{mu.min():.3f}, {mu.max():.3f}]")
        print(f"  sigma: {sigma.shape}, 范围: [{sigma.min():.3f}, {sigma.max():.3f}]")
        print(f"  rho: {rho.shape}, 范围: [{rho.min():.3f}, {rho.max():.3f}]")
        
        # 检查参数合理性
        if (pi < 0).any() or (pi > 1).any():
            print("⚠️ pi参数超出[0,1]范围!")
        if (sigma <= 0).any():
            print("⚠️ sigma参数不为正!")
        if (rho.abs() >= 1).any():
            print("⚠️ rho参数绝对值>=1!")
        
        # 计算轨迹损失
        traj_loss = trainer.mdn_nll_loss(pi, mu, sigma, rho, gt_displacements_flat)
        print(f"轨迹GMM损失: {traj_loss.item():.4f}")
        
        total_loss = config.LAMBDA_INTENT * intent_loss + config.LAMBDA_TRAJ * traj_loss
        print(f"总损失: {total_loss.item():.4f}")
        
    except Exception as e:
        print(f"❌ 损失计算失败: {e}")
        import traceback
        traceback.print_exc()

def visualize_model_architecture():
    """可视化模型架构"""
    print("\n" + "="*60)
    print("📊 模型架构可视化")
    print("="*60)
    
    try:
        # 使用Mermaid图表来展示模型架构
        mermaid_graph = """
        graph TD
            A[历史轨迹<br/>B×H×8] --> B[轨迹编码器<br/>Transformer]
            C[环境ROI<br/>B×C×64×64] --> D[环境编码器<br/>CNN]
            
            B --> E[轨迹特征<br/>B×H×256]
            D --> F[环境特征<br/>B×2048]
            
            E --> G[最后时间步<br/>B×256]
            G --> H[特征融合<br/>MLP]
            F --> H
            
            H --> I[融合特征<br/>B×512]
            I --> J[意图分类<br/>Linear]
            J --> K[意图logits<br/>B×网格数]
            
            E --> L[完整历史<br/>B×H×256]
            M[目标点<br/>B×2] --> N[目标嵌入<br/>Linear]
            N --> O[目标特征<br/>B×1×256]
            
            L --> P[记忆拼接<br/>B×(H+1)×256]
            O --> P
            
            Q[可学习查询<br/>B×F×256] --> R[轨迹解码器<br/>Transformer]
            P --> R
            
            R --> S[解码输出<br/>B×F×256]
            S --> T[GMM头<br/>Linear]
            T --> U[GMM参数<br/>B×F×(K×6)]
        """
        
        print("模型架构流程图 (Mermaid格式):")
        print(mermaid_graph)
        
    except Exception as e:
        print(f"可视化失败: {e}")

def main():
    """主函数"""
    print("🚀 开始深度调试分析")
    
    try:
        # 1. 分析数据样本
        analyze_data_samples()
        
        # 2. 分析模型结构
        analyze_model_structure()
        
        # 3. 测试数据流
        test_data_flow()
        
        # 4. 分析训练逻辑
        analyze_training_logic()
        
        # 5. 可视化模型架构
        visualize_model_architecture()
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎯 调试分析完成!")

if __name__ == '__main__':
    main() 