# 创建时间: 2025-07-20 14:20:23+08:00
# 功能: 读取LMDB数据库中的预处理样本并打印其内容，用于验证数据处理的正确性。
# 输入: LMDB数据库路径 (train.lmdb或val.lmdb)，样本数量
# 输出: 打印到控制台的样本数据结构和部分内容
# 原理: 通过lmdb库打开数据库环境，使用pickle加载存储的序列化样本数据。
# 处理方法: 遍历LMDB数据库，选择指定数量的样本进行打印。

import lmdb
import pickle
import argparse
from pathlib import Path
import numpy as np

def inspect_lmdb_samples(lmdb_path: str, num_samples: int = 5):
    """
    从LMDB数据库中读取并打印指定数量的样本。
    """
    lmdb_env = None
    try:
        lmdb_env = lmdb.open(lmdb_path, readonly=True, lock=False)
        with lmdb_env.begin() as txn:
            cursor = txn.cursor()
            count = 0
            print(f"\n--- 正在从LMDB数据库 '{lmdb_path}' 读取 {num_samples} 个样本 ---")
            for key, value in cursor:
                if count >= num_samples:
                    break
                
                try:
                    sample = pickle.loads(value)
                    print(f"\n--- 样本 {count + 1} (Key: {key.decode('ascii')}) ---")
                    
                    # 打印历史轨迹特征
                    print("\n历史轨迹 (history_features):")
                    # 修正：现在我们知道历史轨迹存储在 'history_features' 中
                    if 'history_features' in sample and sample['history_features'] is not None:
                        history_data = sample['history_features']
                        if history_data.size > 0: # 检查数组是否为空
                            print(f"  形状: {history_data.shape}")
                            # 假设特征顺序与 history_features_list + land_cover_one_hot_features_list 一致
                            # 由于 inspect_lmdb_samples 是一个独立的脚本，它没有直接访问 history_features_list
                            # 我们只打印数值的前几行，不打印列名
                            print(f"  前5行数据:\n{history_data[:5] if history_data.ndim > 1 else history_data[:5].reshape(1, -1)}") 
                        else:
                            print("  历史轨迹数据为空。")
                    else:
                        print("  未找到历史轨迹数据 (键为 'history_features')。")

                    # 打印环境ROI (9x9像素，多通道)
                    print("\n环境ROI (environment_roi):")
                    if 'environment_roi' in sample and sample['environment_roi'] is not None:
                        print(f"  形状: {sample['environment_roi'].shape}")
                        # 打印每个通道的均值和标准差，方便快速检查
                        for i, channel_data in enumerate(sample['environment_roi']):
                            print(f"  通道 {i} (均值: {np.mean(channel_data):.4f}, 标准差: {np.std(channel_data):.4f})")
                        print(f"  前两个通道的左上角 2x2 像素:\n{sample['environment_roi'][:, :2, :2]}")
                    else:
                        print("  无环境ROI数据。")

                    # 打印真实未来轨迹
                    print("\n真实未来轨迹 (ground_truth_trajectory):")
                    if 'ground_truth_trajectory' in sample and sample['ground_truth_trajectory'] is not None:
                        print(f"  形状: {sample['ground_truth_trajectory'].shape}")
                        print(f"  起始点: {sample['ground_truth_trajectory'][0]}")
                        print(f"  结束点: {sample['ground_truth_trajectory'][-1]}")
                    else:
                        print("  无真实未来轨迹数据。")

                    # 打印真实目的地
                    print("\n真实目的地 (ground_truth_destination):")
                    if 'ground_truth_destination' in sample and sample['ground_truth_destination'] is not None:
                        print(f"  值: {sample['ground_truth_destination']}")
                    else:
                        print("  无真实目的地数据。")
                    
                    # 打印文件ID和时间戳
                    print("\n其他信息:")
                    # 检查并打印 file_id 和 timestamp_ms，如果它们存在的话
                    print(f"  文件ID: {sample.get('file_id', '未提供')}")
                    print(f"  时间戳(ms): {sample.get('timestamp_ms', '未提供')}")

                    print("-" * 40)
                    count += 1
                except Exception as e:
                    print(f"警告: 读取或解析样本时出错 (Key: {key.decode('ascii')}): {e}")
                    import traceback
                    traceback.print_exc() # 打印完整的堆栈跟踪，帮助调试
                    continue
    except lmdb.Error as e:
        print(f"错误: 无法打开LMDB环境 '{lmdb_path}': {e}")
    finally:
        if lmdb_env:
            lmdb_env.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="从LMDB数据库中读取并打印预处理样本。")
    parser.add_argument('--lmdb_dir', type=str, default='data/processed_lmdb/train', 
                        help='LMDB数据库的路径，例如 data/processed_lmdb/train 或 data/processed_lmdb/val。')
    parser.add_argument('--num_samples', type=int, default=5, 
                        help='要打印的样本数量。')
    
    args = parser.parse_args()
    inspect_lmdb_samples(args.lmdb_dir, args.num_samples) 