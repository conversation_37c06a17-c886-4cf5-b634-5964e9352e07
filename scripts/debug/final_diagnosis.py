#!/usr/bin/env python3
"""
最终诊断脚本 - 找出所有问题
"""
import torch
import torch.nn as nn
import numpy as np
import pickle
from torch.utils.data import DataLoader
from src.configs.v1_grid_classifier_config import config
from src.data.datasets import TrajectoryDataset
from src.models.v1_grid_classifier.end_to_end_model import GridClassifierV1
from src.engine.trainer import Trainer

def diagnose_intent_loss():
    """诊断意图分类损失为0的问题"""
    print("🔍 诊断意图分类损失问题")
    print("="*50)
    
    # 加载数据
    train_dataset = TrajectoryDataset(config.LMDB_PATH, config)
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=False)
    
    # 检查意图标签分布
    all_intent_labels = []
    for i in range(min(100, len(train_dataset))):
        sample = train_dataset[i]
        all_intent_labels.append(sample['intent_label_gt'].item())
    
    unique_labels = set(all_intent_labels)
    print(f"前100个样本中的独特意图标签: {len(unique_labels)}")
    print(f"标签范围: {min(all_intent_labels)} ~ {max(all_intent_labels)}")
    print(f"网格总数: {config.INTENT_GRID_SIZE[0] * config.INTENT_GRID_SIZE[1]}")
    
    if len(unique_labels) == 1:
        print("⚠️ 所有样本都有相同的意图标签！这会导致分类损失为0")
        return False
    
    return True

def diagnose_negative_loss():
    """诊断负数损失问题"""
    print("\n🔍 诊断负数损失问题")
    print("="*50)
    
    # 创建模型和数据
    stats = load_normalization_stats()
    dest_mean = torch.tensor(stats['mean'][:2], dtype=torch.float32)
    dest_std = torch.tensor(stats['std'][:2], dtype=torch.float32)
    model = GridClassifierV1(config, dest_mean, dest_std)
    
    train_dataset = TrajectoryDataset(config.LMDB_PATH, config)
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=False)
    
    # 创建临时训练器
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    trainer = Trainer(model, config, train_loader, train_loader, optimizer, device='cpu')
    
    # 获取一个批次并计算损失
    batch = next(iter(train_loader))
    
    model.train()
    output = model(batch['history_traj'], batch['roi'], dest_gt=batch['dest_gt'])
    
    # 检查意图损失
    intent_loss = trainer.intent_loss_fn(output['intent_logits'], batch['intent_label_gt'])
    print(f"意图分类损失: {intent_loss.item():.6f}")
    
    # 检查轨迹损失计算的每一步
    gt_displacements = trainer._calculate_displacements(batch['history_traj'], batch['future_traj_gt'])
    gt_displacements_flat = gt_displacements.view(-1, 2)
    
    print(f"真实位移统计:")
    print(f"  形状: {gt_displacements_flat.shape}")
    print(f"  范围: [{gt_displacements_flat.min():.3f}, {gt_displacements_flat.max():.3f}]")
    print(f"  均值: {gt_displacements_flat.mean(dim=0)}")
    print(f"  标准差: {gt_displacements_flat.std(dim=0)}")
    
    # 解析GMM参数
    pi, mu, sigma, rho = model.parse_gmm_params(output['gmm_params_flat'])
    
    print(f"\nGMM参数统计:")
    print(f"  pi 形状: {pi.shape}, 范围: [{pi.min():.3f}, {pi.max():.3f}]")
    print(f"  mu 形状: {mu.shape}, 范围: [{mu.min():.3f}, {mu.max():.3f}]")
    print(f"  sigma 形状: {sigma.shape}, 范围: [{sigma.min():.3f}, {sigma.max():.3f}]")
    print(f"  rho 形状: {rho.shape}, 范围: [{rho.min():.3f}, {rho.max():.3f}]")
    
    # 检查参数合理性
    issues = []
    if (pi <= 0).any():
        issues.append("pi参数包含非正值")
    if (sigma <= 0).any():
        issues.append("sigma参数包含非正值")
    if (rho.abs() >= 1).any():
        issues.append("rho参数绝对值>=1")
    
    if issues:
        print(f"⚠️ 参数问题: {', '.join(issues)}")
    
    # 检测MDN损失计算
    try:
        traj_loss = trainer.mdn_nll_loss(pi, mu, sigma, rho, gt_displacements_flat)
        print(f"轨迹GMM损失: {traj_loss.item():.6f}")
        
        if traj_loss.item() < 0:
            print("⚠️ 轨迹损失为负数！这表明MDN损失函数有问题")
            return False
            
    except Exception as e:
        print(f"❌ MDN损失计算失败: {e}")
        return False
    
    return True

def diagnose_evaluation_metrics():
    """诊断评估指标异常问题"""
    print("\n🔍 诊断评估指标问题")
    print("="*50)
    
    # 检查反归一化
    stats = load_normalization_stats()
    dest_mean = torch.tensor(stats['mean'][:2], dtype=torch.float32)
    dest_std = torch.tensor(stats['std'][:2], dtype=torch.float32)
    
    print(f"归一化参数:")
    print(f"  dest_mean: {dest_mean}")
    print(f"  dest_std: {dest_std}")
    
    # 模拟归一化和反归一化
    original_coords = torch.tensor([[-970103.6, 8889068.0]], dtype=torch.float32)
    normalized = (original_coords - dest_mean) / dest_std
    denormalized = normalized * dest_std + dest_mean
    
    print(f"\n反归一化测试:")
    print(f"  原始坐标: {original_coords}")
    print(f"  归一化后: {normalized}")
    print(f"  反归一化: {denormalized}")
    print(f"  误差: {torch.abs(original_coords - denormalized).max():.6f}")
    
    # 检查数据集的反归一化函数
    train_dataset = TrajectoryDataset(config.LMDB_PATH, config)
    
    test_normalized = torch.tensor([[0.2712, 1.5303]], dtype=torch.float32)
    denorm_result = train_dataset.denormalize_coords(test_normalized)
    print(f"\n数据集反归一化测试:")
    print(f"  输入: {test_normalized}")
    print(f"  输出: {denorm_result}")

def load_normalization_stats():
    """加载归一化统计数据"""
    with open('data/preprocessed/normalization_stats.pkl', 'rb') as f:
        return pickle.load(f)

def check_data_range():
    """检查数据范围"""
    print("\n🔍 检查数据范围")
    print("="*50)
    
    train_dataset = TrajectoryDataset(config.LMDB_PATH, config)
    
    # 检查多个样本的数据范围
    all_history = []
    all_future = []
    all_dest = []
    
    for i in range(min(50, len(train_dataset))):
        sample = train_dataset[i]
        all_history.append(sample['history_traj'])
        all_future.append(sample['future_traj_gt'])
        all_dest.append(sample['dest_gt'])
    
    history_tensor = torch.stack(all_history)
    future_tensor = torch.stack(all_future)
    dest_tensor = torch.stack(all_dest)
    
    print(f"历史轨迹数据范围:")
    print(f"  坐标列 [0,1]: [{history_tensor[:,:,:2].min():.3f}, {history_tensor[:,:,:2].max():.3f}]")
    print(f"  速度列 [2,3]: [{history_tensor[:,:,2:4].min():.3f}, {history_tensor[:,:,2:4].max():.3f}]")
    print(f"  加速度列 [4,5]: [{history_tensor[:,:,4:6].min():.3f}, {history_tensor[:,:,4:6].max():.3f}]")
    
    print(f"\n未来轨迹数据范围:")
    print(f"  坐标列 [0,1]: [{future_tensor[:,:,:2].min():.3f}, {future_tensor[:,:,:2].max():.3f}]")
    
    print(f"\n目标点数据范围:")
    print(f"  dest_gt: [{dest_tensor.min():.3f}, {dest_tensor.max():.3f}]")

def main():
    """主诊断函数"""
    print("🚨 开始最终诊断")
    print("="*60)
    
    all_ok = True
    
    # 诊断各个方面
    if not diagnose_intent_loss():
        all_ok = False
    
    if not diagnose_negative_loss():
        all_ok = False
    
    diagnose_evaluation_metrics()
    check_data_range()
    
    print("\n" + "="*60)
    if all_ok:
        print("✅ 所有检查通过，可以开始训练")
    else:
        print("❌ 发现严重问题，需要修复后再训练")
    
    return all_ok

if __name__ == '__main__':
    main() 