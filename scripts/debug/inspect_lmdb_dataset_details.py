
import lmdb
import pickle
import argparse
import sys
from pathlib import Path
import numpy as np

# 确保src目录在python path中
project_root = Path(__file__).resolve().parents[2]
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.utils.config_loader import load_config

def inspect_lmdb_sample_details(lmdb_path, num_samples_to_inspect=1):
    """
    加载并详细检查LMDB数据库中的样本，包括数据结构和特征维度。
    """
    print(f"正在检查LMDB数据库: {lmdb_path}")
    try:
        env = lmdb.open(lmdb_path, readonly=True, lock=False, readahead=False, meminit=False)
        with env.begin(write=False) as txn:
            cursor = txn.cursor()
            found_samples = 0
            for key, value in cursor:
                if found_samples >= num_samples_to_inspect:
                    break
                
                print(f"\n--- 样本 {found_samples + 1} ({key.decode()}) ---")
                data_sample = pickle.loads(value)
                
                if isinstance(data_sample, dict):
                    print("样本内容是一个字典。以下是键、值的类型、维度和数据类型：")
                    for k, v in data_sample.items():
                        if isinstance(v, np.ndarray):
                            print(f"  - 键: '{k}', 类型: {type(v)}, 维度: {v.shape}, 数据类型: {v.dtype}")
                            if v.size > 0: # 检查数组是否为空
                                print(f"    部分值: {v.flatten()[:5]}...") # 打印前5个展平的值
                                if v.ndim == 2 and v.shape[0] > 0: # 对于二维数组，打印前几行
                                    print(f"    前3行数据:\n{v[:3, :]}")
                                elif v.ndim == 4 and v.shape[0] > 0: # 对于环境ROI，打印第一个样本的第一个通道的子集
                                    print(f"    第一个时间步的第一个通道的ROI (5x5):\n{v[0, 0, :5, :5]}")
                                elif v.ndim == 1 and v.shape[0] > 0: # 对于一维数组，打印全部
                                    print(f"    所有值: {v}")
                            else:
                                print(f"    数组为空。")
                        else:
                            print(f"  - 键: '{k}', 类型: {type(v)}, 值: {v}")
                else:
                    print(f"样本内容不是一个字典，其类型是: {type(data_sample)}")
                found_samples += 1
            
            if found_samples == 0:
                print(f"LMDB数据库 '{lmdb_path}' 为空或未找到样本。")
        print("\n==============================\n检查完成。")
    except lmdb.Error as e:
        print(f"读取LMDB数据库时出错: {e}")
    except Exception as e:
        print(f"检查过程中出现错误: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="加载并详细检查LMDB数据库的样本结构和特征维度。")
    parser.add_argument('--lmdb_dir', type=str, 
                        help='要检查的LMDB数据库的路径，例如 data/processed_lmdb/train。如果未提供，将从配置文件加载。')
    parser.add_argument('--config', type=str, default='configs/main_config.yaml', 
                        help='主配置文件路径。')
    parser.add_argument('--data_config', type=str, default='configs/data_preprocessing.yaml', 
                        help='数据预处理配置文件路径。')

    args = parser.parse_args()

    if args.lmdb_dir:
        target_lmdb_path = args.lmdb_dir
    else:
        # 从配置文件加载路径
        config = load_config(args.config, args.data_config)
        target_lmdb_path = config.data_preprocessing.output_path + '/train'
        print(f"从配置文件加载LMDB路径: {target_lmdb_path}")

    inspect_lmdb_sample_details(target_lmdb_path) 