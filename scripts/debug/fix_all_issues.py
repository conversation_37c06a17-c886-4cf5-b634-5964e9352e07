#!/usr/bin/env python3
"""
修复所有核心问题
"""
import pandas as pd
import numpy as np
from src.configs.v1_grid_classifier_config import config

def analyze_data_distribution():
    """分析数据分布以找出问题"""
    print("🔍 分析数据分布")
    print("="*50)
    
    df = pd.read_csv('data/aggregated_features/aggregated_features.csv')
    
    # 分析轨迹终点分布
    endpoints = df.groupby('trajectory_id')[['center_x', 'center_y']].last()
    
    print(f"所有轨迹终点:")
    for i, (idx, row) in enumerate(endpoints.iterrows()):
        print(f"轨迹{idx}: ({row.center_x:.1f}, {row.center_y:.1f})")
    
    # 计算终点的分布范围
    x_range = endpoints.center_x.max() - endpoints.center_x.min()
    y_range = endpoints.center_y.max() - endpoints.center_y.min()
    
    print(f"\n终点分布范围:")
    print(f"X范围: {x_range:.1f}m")
    print(f"Y范围: {y_range:.1f}m")
    
    # 检查当前网格设置
    map_bounds = config.MAP_WORLD_BOUNDS
    grid_size = config.INTENT_GRID_SIZE
    
    x_span = map_bounds[2] - map_bounds[0]
    y_span = map_bounds[3] - map_bounds[1]
    
    grid_cell_x = x_span / grid_size[0]
    grid_cell_y = y_span / grid_size[1]
    
    print(f"\n当前网格设置:")
    print(f"地图范围: {map_bounds}")
    print(f"网格尺寸: {grid_size}")
    print(f"每个网格大小: {grid_cell_x:.1f}m x {grid_cell_y:.1f}m")
    
    # 计算每个终点落在哪个网格
    print(f"\n终点网格分布:")
    for i, (idx, row) in enumerate(endpoints.iterrows()):
        grid_x = int((row.center_x - map_bounds[0]) / x_span * grid_size[0])
        grid_y = int((row.center_y - map_bounds[1]) / y_span * grid_size[1])
        grid_x = np.clip(grid_x, 0, grid_size[0] - 1)
        grid_y = np.clip(grid_y, 0, grid_size[1] - 1)
        intent_label = grid_y * grid_size[0] + grid_x
        print(f"轨迹{idx}: 网格({grid_x}, {grid_y}) = 标签{intent_label}")
    
    return endpoints

def suggest_fixes(endpoints):
    """建议修复方案"""
    print(f"\n🔧 修复建议")
    print("="*50)
    
    # 建议1: 调整网格大小
    x_min, x_max = endpoints.center_x.min(), endpoints.center_x.max()
    y_min, y_max = endpoints.center_y.min(), endpoints.center_y.max()
    
    # 添加一些边距
    margin = 5000  # 5km边距
    suggested_bounds = (x_min - margin, y_min - margin, x_max + margin, y_max + margin)
    
    print(f"建议1: 调整地图边界")
    print(f"  当前边界: {config.MAP_WORLD_BOUNDS}")
    print(f"  建议边界: {suggested_bounds}")
    
    # 建议2: 调整网格大小
    data_x_span = x_max - x_min + 2 * margin
    data_y_span = y_max - y_min + 2 * margin
    
    # 选择合适的网格数，使每个网格大约1-2km
    target_cell_size = 2000  # 2km per cell
    suggested_grid_x = max(10, int(data_x_span / target_cell_size))
    suggested_grid_y = max(10, int(data_y_span / target_cell_size))
    
    print(f"\n建议2: 调整网格大小")
    print(f"  当前网格: {config.INTENT_GRID_SIZE}")
    print(f"  建议网格: ({suggested_grid_x}, {suggested_grid_y})")
    print(f"  建议网格大小: {data_x_span/suggested_grid_x:.0f}m x {data_y_span/suggested_grid_y:.0f}m")
    
    # 建议3: 检查轨迹本身的范围
    print(f"\n建议3: 轨迹范围检查")
    df = pd.read_csv('data/aggregated_features/aggregated_features.csv')
    
    all_x_range = df.center_x.max() - df.center_x.min()
    all_y_range = df.center_y.max() - df.center_y.min()
    
    print(f"  所有轨迹点范围: {all_x_range:.1f}m x {all_y_range:.1f}m")
    
    # 检查轨迹长度
    traj_lengths = df.groupby('trajectory_id').size()
    print(f"  轨迹长度范围: {traj_lengths.min()} - {traj_lengths.max()}点")
    print(f"  平均轨迹长度: {traj_lengths.mean():.1f}点")
    
    return suggested_bounds, (suggested_grid_x, suggested_grid_y)

def generate_fixed_config(suggested_bounds, suggested_grid):
    """生成修复后的配置"""
    print(f"\n📝 生成修复配置")
    print("="*50)
    
    fixed_config = f"""# 修复后的配置建议

# 修复地图边界 - 基于实际数据范围
MAP_WORLD_BOUNDS = {suggested_bounds}

# 修复网格大小 - 确保有足够的类别多样性
INTENT_GRID_SIZE = {suggested_grid}

# 其他建议:
# 1. 考虑将轨迹生成改为预测绝对坐标而不是位移
# 2. 调整归一化策略，仅对坐标进行归一化，保持其他特征原始尺度
# 3. 增加数据增强，人工增加轨迹的多样性
"""
    
    print(fixed_config)
    
    # 保存到文件
    with open('suggested_config_fix.txt', 'w', encoding='utf-8') as f:
        f.write(fixed_config)
    
    print("修复建议已保存到 suggested_config_fix.txt")

def main():
    print("🔧 开始分析和修复")
    print("="*60)
    
    endpoints = analyze_data_distribution()
    suggested_bounds, suggested_grid = suggest_fixes(endpoints)
    generate_fixed_config(suggested_bounds, suggested_grid)
    
    print(f"\n✅ 分析完成！请查看修复建议。")

if __name__ == '__main__':
    main() 