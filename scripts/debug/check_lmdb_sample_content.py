# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.data.datasets import LMDBDataset
from src.utils.config_loader import load_config
import lmdb
import pickle
import torch
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import logging
from omegaconf import OmegaConf
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def plot_trajectory(ax, traj, color, label, marker='o', markersize=3, alpha=0.7):
    """
    绘制二维轨迹。
    """
    ax.plot(traj[:, 0], traj[:, 1], color=color, marker=marker, markersize=markersize, label=label, alpha=alpha)

def plot_trajectory_3d(ax, traj, color, label, marker='o', markersize=3, alpha=0.7):
    """
    绘制三维轨迹（XY和时间）。
    """
    t = np.arange(traj.shape[0])
    ax.plot(traj[:, 0], traj[:, 1], t, color=color, marker=marker, markersize=markersize, label=label, alpha=alpha)

def visualize_environment_roi(ax, env_roi_data, title="环境ROI数据可视化"):
    """
    可视化环境ROI数据，假设是CWH格式。
    """
    if env_roi_data.ndim == 4: # 处理 (T, C, H, W) 形状
        # 选择第一个时间步进行可视化
        env_roi_data = env_roi_data[0]
        title = title + " (第一个时间步)"

    if env_roi_data.ndim == 3:
        # 假设是CWH，取第一个通道或平均所有通道进行可视化
        if env_roi_data.shape[0] > 1:
            # 如果有多个通道，可以考虑平均或选择一个通道
            img = np.mean(env_roi_data, axis=0) # 平均所有通道
            # img = env_roi_data[0] # 或者选择第一个通道
        else:
            img = env_roi_data[0] # 只有一个通道
    else:
        img = env_roi_data # 已经是2D，或处理为2D

    im = ax.imshow(img, cmap='terrain') # 使用terrain配色
    plt.colorbar(im, ax=ax, label='值')
    ax.set_title(title)
    ax.set_xlabel('X像素')
    ax.set_ylabel('Y像素')

def main():
    logging.info("--- 开始检查LMDB样本内容 ---")

    # 1. 加载配置
    config_path = 'configs/main_config.yaml'
    model_config_path = 'configs/models/pecnet_model.yaml'
    config = load_config(config_path, model_config_path)

    # 获取LMDB路径和归一化统计文件路径
    lmdb_path = config.data_params.lmdb_path
    stats_path = config.data_params.stats_path_v2

    if not os.path.exists(lmdb_path):
        logging.error(f"错误: LMDB路径未找到: {lmdb_path}")
        sys.exit(1)
    if not os.path.exists(stats_path):
        logging.error(f"错误: 归一化统计文件未找到: {stats_path}")
        sys.exit(1)

    # 2. 加载归一化统计数据
    with open(stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    logging.info("归一化统计数据加载成功。")

    # 3. 创建LMDBDataset实例 (使用train类型)
    train_dataset = LMDBDataset(config, lmdb_type='train')
    logging.info(f"LMDBDataset 初始化: {train_dataset.lmdb_path} 包含 {len(train_dataset)} 个样本。")

    if len(train_dataset) == 0:
        logging.warning("数据集中没有样本，无法检查。请确保LMDB已正确生成。")
        sys.exit(0)

    # 4. 获取第一个样本
    sample_index = 2640  # 可以更改为其他索引
    try:
        sample = train_dataset[sample_index]
        logging.info(f"成功加载索引 {sample_index} 的样本。")
    except Exception as e:
        logging.error(f"加载样本 {sample_index} 时发生错误: {e}")
        sys.exit(1)

    # 5. 打印样本内容摘要
    logging.info("\n--- 样本内容摘要 ---")
    for key, value in sample.items():
        if isinstance(value, torch.Tensor):
            logging.info(f"键: {key}, 类型: {type(value)}, 形状: {value.shape}, Dtype: {value.dtype}, 是否归一化: {key.startswith('ground_truth') or key.startswith('history')}")
            if value.numel() > 0: # 避免空张量调用min/max/mean
                logging.info(f"  值范围: [{value.min():.4f}, {value.max():.4f}], 均值: {value.mean():.4f}")
            else:
                logging.info(f"  张量为空。")
        elif isinstance(value, np.ndarray):
            logging.info(f"键: {key}, 类型: {type(value)}, 形状: {value.shape}, Dtype: {value.dtype}")
            if value.size > 0: # 避免空数组调用min/max/mean
                logging.info(f"  值范围: [{value.min():.4f}, {value.max():.4f}], 均值: {value.mean():.4f}")
            else:
                logging.info(f"  数组为空。")
        else:
            logging.info(f"键: {key}, 类型: {type(value)}, 值: {value}")

    # 6. 可视化轨迹和环境ROI
    logging.info("\n--- 正在生成可视化结果 ---\n")
    fig = plt.figure(figsize=(18, 6))

    # 历史轨迹和真实未来轨迹 (2D)
    ax1 = fig.add_subplot(1, 3, 1)
    history_data = sample['history'].numpy() # 转换为numpy
    ground_truth_trajectory = sample['ground_truth_trajectory'].numpy() # 转换为numpy
    ground_truth_destination = sample['ground_truth_destination'].numpy() # 转换为numpy

    # 仅绘制XY坐标 (历史特征中的前两列是x, y)
    plot_trajectory(ax1, history_data[:, :2], 'blue', '历史轨迹', marker='o')
    plot_trajectory(ax1, ground_truth_trajectory[:, :2], 'green', '真实未来轨迹', marker='x')
    ax1.plot(ground_truth_destination[0], ground_truth_destination[1], 'rx', markersize=10, label='真实目的地')
    ax1.set_title('历史与真实轨迹 (XY)')
    ax1.set_xlabel('X坐标')
    ax1.set_ylabel('Y坐标')
    ax1.axis('equal')
    ax1.legend()
    ax1.grid(True)

    # 历史轨迹和真实未来轨迹 (3D - XY和时间)
    ax2 = fig.add_subplot(1, 3, 2, projection='3d')
    plot_trajectory_3d(ax2, history_data[:, :2], 'blue', '历史轨迹') # 历史轨迹只取XY，时间轴由函数生成
    plot_trajectory_3d(ax2, ground_truth_trajectory[:, :2], 'green', '真实未来轨迹') # 真实轨迹只取XY，时间轴由函数生成
    ax2.scatter(ground_truth_destination[0], ground_truth_destination[1], len(history_data) + len(ground_truth_trajectory) -1, color='red', marker='X', s=100, label='真实目的地')
    ax2.set_title('历史与真实轨迹 (XY + 时间)')
    ax2.set_xlabel('X坐标')
    ax2.set_ylabel('Y坐标')
    ax2.set_zlabel('时间步')
    ax2.legend()
    ax2.grid(True)

    # 环境ROI数据可视化
    ax3 = fig.add_subplot(1, 3, 3)
    environment_roi_data = sample['environment_roi'].numpy() # 转换为numpy
    visualize_environment_roi(ax3, environment_roi_data)

    plt.tight_layout()
    # 保存可视化结果
    output_viz_dir = "outputs/data_validation"
    os.makedirs(output_viz_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"data_validation_sample_{sample_index}_{timestamp}.png"
    filepath = os.path.join(output_viz_dir, filename)
    plt.savefig(filepath)
    logging.info(f"可视化结果已保存到: {filepath}")
    plt.close(fig)

    logging.info("--- LMDB样本内容检查完成 ---")

if __name__ == '__main__':
    main() 