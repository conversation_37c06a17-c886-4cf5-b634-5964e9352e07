import lmdb
import pickle
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import random
import logging
from scipy.stats import mode
import rasterio
from rasterio.windows import Window
from pathlib import Path

# --- 配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei'] # 使用一个更通用的中文字体
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 14

# --- 从 create_dataset_v2.py 复制的、精确的辅助函数 ---
HISTORY_FEATURES = [
    "x", "y", "velocity_x", "velocity_y", "acceleration_x", "acceleration_y", 
    "heading_change_rate", "cos_heading", "sin_heading",
    "displacement_x", "displacement_y", "tortuosity",
    "dem_mean", "land_cover_mode", "slope_mean", "aspect_mean"
]

def calculate_kinematic_features_for_inspection(df: pd.DataFrame) -> pd.DataFrame:
    df_kinematic = df.copy()
    df_kinematic['timestamp_dt'] = pd.to_datetime(df_kinematic['timestamp'], unit='s')
    df_kinematic = df_kinematic.set_index('timestamp_dt')
    resample_interval = "100ms"
    df_resampled = df_kinematic.resample(resample_interval).asfreq().interpolate(method='linear')
    dt_s = df_resampled.index.to_series().diff().dt.total_seconds().fillna(0)
    dt_s.replace(0, 1e-6, inplace=True)
    df_resampled['velocity_x'] = df_resampled['x'].diff().fillna(0) / dt_s
    df_resampled['velocity_y'] = df_resampled['y'].diff().fillna(0) / dt_s
    df_resampled['acceleration_x'] = df_resampled['velocity_x'].diff().fillna(0) / dt_s
    df_resampled['acceleration_y'] = df_resampled['velocity_y'].diff().fillna(0) / dt_s
    df_resampled['heading_rad'] = np.deg2rad(df_resampled['heading_deg'])
    df_resampled['cos_heading'] = np.cos(df_resampled['heading_rad'])
    df_resampled['sin_heading'] = np.sin(df_resampled['heading_rad'])
    df_resampled['heading_change_rate'] = df_resampled['heading_rad'].diff().fillna(0) / dt_s
    df_resampled.replace([np.inf, -np.inf], 0, inplace=True)
    return df_resampled.reset_index()

def _load_super_window_for_inspection(
    traj_df: pd.DataFrame, base_dir: Path, margin_m: float = 500.0
) -> tuple:
    env_dir = base_dir / "environment"
    dem_path = env_dir / "dem_aligned.tif"
    land_cover_path = env_dir / "landcover_aligned.tif"
    slope_path = env_dir / "slope_aligned.tif"
    aspect_path = env_dir / "aspect_aligned.tif"
    
    with rasterio.open(dem_path) as dem_src:
        dem_transform = dem_src.transform
    x_min, x_max = traj_df['x'].min() - margin_m, traj_df['x'].max() + margin_m
    y_min, y_max = traj_df['y'].min() - margin_m, traj_df['y'].max() + margin_m
    super_window_bounds = rasterio.windows.from_bounds(
        left=x_min, bottom=y_min, right=x_max, top=y_max, transform=dem_transform
    )
    super_window_transform = rasterio.windows.transform(super_window_bounds, dem_transform)
    
    with rasterio.open(dem_path) as src:
        dem_super_window = src.read(1, window=super_window_bounds)
    
    with rasterio.open(land_cover_path) as src:
        land_cover_super_window = src.read(1, window=super_window_bounds)
            
    with rasterio.open(slope_path) as src:
        slope_super_window = src.read(1, window=super_window_bounds)
            
    with rasterio.open(aspect_path) as src:
        aspect_super_window = src.read(1, window=super_window_bounds)
            
    return dem_super_window, land_cover_super_window, slope_super_window, aspect_super_window, super_window_transform

def manually_aggregate_features(obs_df_raw: pd.DataFrame, base_dir: Path) -> dict:
    """
    真正精确复刻 create_dataset_v2.py 的聚合逻辑
    """
    if obs_df_raw.empty:
        return {}
    
    # 1. 加载环境数据 Super Window
    dem_sw, lc_sw, slope_sw, aspect_sw, sw_transform = _load_super_window_for_inspection(obs_df_raw, base_dir)

    # 2. 重新计算运动学特征并重采样到10Hz
    obs_df_resampled = calculate_kinematic_features_for_inspection(obs_df_raw)

    # 3. 为每个重采样后的点采样环境特征
    xs, ys = obs_df_resampled['x'].values, obs_df_resampled['y'].values
    rows, cols = rasterio.transform.rowcol(sw_transform, xs, ys)
    rows = np.clip(rows, 0, dem_sw.shape[0] - 1)
    cols = np.clip(cols, 0, dem_sw.shape[1] - 1)
    
    obs_with_env = obs_df_resampled.copy()
    obs_with_env['dem'] = dem_sw[rows, cols]
    obs_with_env['land_cover'] = lc_sw[rows, cols]
    obs_with_env['slope'] = slope_sw[rows, cols]
    obs_with_env['aspect'] = aspect_sw[rows, cols]
    
    # 4. 计算形状特征 (基于重采样后的数据)
    start_x, end_x = obs_with_env['x'].iloc[0], obs_with_env['x'].iloc[-1]
    start_y, end_y = obs_with_env['y'].iloc[0], obs_with_env['y'].iloc[-1]
    disp_x, disp_y = end_x - start_x, end_y - start_y
    path_length = np.sum(np.sqrt(obs_with_env['x'].diff().pow(2) + obs_with_env['y'].diff().pow(2)))
    straight_dist = np.sqrt(disp_x**2 + disp_y**2)
    tortuosity = path_length / straight_dist if straight_dist > 1e-6 else 1.0

    # 5. 聚合所有特征
    agg_rules = {
        'x': 'mean', 'y': 'mean', 'velocity_x': 'mean', 'velocity_y': 'mean',
        'acceleration_x': 'mean', 'acceleration_y': 'mean', 'heading_change_rate': 'mean',
        'cos_heading': 'mean', 'sin_heading': 'mean', 'dem': 'mean', 
        'slope': 'mean', 'aspect': 'mean',
        'land_cover': lambda x: mode(x, keepdims=True)[0][0]
    }
    
    # 使用包含环境信息的完整数据帧进行聚合
    aggregated_features = obs_with_env[list(agg_rules.keys())].mean().to_dict()
    aggregated_features['land_cover_mode'] = agg_rules['land_cover'](obs_with_env['land_cover'])
    if 'land_cover' in aggregated_features: del aggregated_features['land_cover']
    
    aggregated_features.update({
        "displacement_x": disp_x, "displacement_y": disp_y, "tortuosity": tortuosity
    })

    # 重命名
    for key in ['dem', 'slope', 'aspect']:
        if key in aggregated_features:
            aggregated_features[f"{key}_mean"] = aggregated_features.pop(key)
            
    final_features = {f: aggregated_features.get(f, 0.0) for f in HISTORY_FEATURES}
    return final_features


def analyze_sample(db_path, output_dir="cursor_drafts", base_dir_path: Path = Path('.')):
    logging.info(f"正在打开LMDB数据库: {db_path}")
    env = lmdb.open(db_path, readonly=True, lock=False, readahead=False, meminit=False)

    with env.begin(write=False) as txn:
        keys = [key for key, _ in txn.cursor()]
        if not keys:
            logging.error("错误：数据库为空，无法进行分析。")
            return
        
        random_key = random.choice(keys)
        logging.info(f"随机选择样本进行分析，Key: {random_key.decode()}")
        
        value = txn.get(random_key)
        sample = pickle.loads(value)

        # 1. 提取样本元数据
        source_file = sample['source_file']
        start_time = sample['start_time']
        obs_horizon_s = 300  # 5分钟
        pred_horizon_s = 2100 # 35分钟 -> 新版是2100s = 35分钟
        
        obs_end_time = start_time + obs_horizon_s
        full_end_time = start_time + obs_horizon_s + pred_horizon_s

        logging.info("\n--- 样本元数据 ---")
        logging.info(f"UID: {sample['uid']}")
        logging.info(f"源文件: {source_file}")
        logging.info(f"轨迹ID: {sample['trajectory_id']}")
        logging.info(f"观测窗口: {start_time} -> {obs_end_time}")
        
        # 2. 读取原始数据
        if not os.path.exists(source_file):
            logging.error(f"错误: 找不到源文件 {source_file}")
            return
            
        logging.info(f"\n正在读取原始轨迹文件: {source_file}...")
        original_df = pd.read_csv(source_file)
        
        # --- 关键修复 ---
        # 原始列名是 'timestamp_ms'，需要将其转换为秒单位的 'timestamp' 列
        if 'timestamp_ms' not in original_df.columns:
            logging.error(f"错误: 源文件 {source_file} 中缺少 'timestamp_ms' 列。")
            return
        original_df['timestamp'] = original_df['timestamp_ms'] / 1000.0
        # --- 修复结束 ---

        original_df['timestamp'] = pd.to_numeric(original_df['timestamp'])
        
        # 筛选出观测窗口和完整窗口的原始数据
        obs_original_df = original_df[(original_df['timestamp'] >= start_time) & (original_df['timestamp'] < obs_end_time)].copy()
        full_window_original_df = original_df[(original_df['timestamp'] >= start_time) & (original_df['timestamp'] < full_end_time)].copy()

        if full_window_original_df.empty:
            logging.error("错误：在指定时间窗口内未找到任何原始数据。")
            return

        # 3. 准备处理后的数据
        history_vec = sample['history_traj']
        future_points = sample['future_traj']
        
        history_df = pd.DataFrame([history_vec], columns=HISTORY_FEATURES)
        future_df = pd.DataFrame(future_points, columns=['x', 'y'])

        # --- 可视化对比 ---
        logging.info("\n正在生成可视化对比图...")
        fig, ax = plt.subplots(figsize=(12, 12))
        
        ax.plot(full_window_original_df['x'], full_window_original_df['y'], 'k-', linewidth=0.7, label='原始轨迹 (40分钟)')
        ax.plot(obs_original_df['x'], obs_original_df['y'], 'b-', linewidth=1.5, label='原始观测轨迹 (5分钟)')
        
        # 绘制未来预测点
        ax.plot(future_df['x'], future_df['y'], 'gD', markerfacecolor='lightgreen', markersize=6, label='未来轨迹点 (35分钟)')
        
        # 绘制聚合后的历史观测点中心
        history_center_x = history_df['x'].iloc[0]
        history_center_y = history_df['y'].iloc[0]
        ax.plot(history_center_x, history_center_y, 'm*', markersize=25, label='观测窗口聚合中心')
        
        ax.set_title(f'样本 {sample["uid"]} vs 原始轨迹', fontsize=22)
        ax.set_xlabel('X 坐标 (m)', fontsize=20)
        ax.set_ylabel('Y 坐标 (m)', fontsize=20)
        ax.legend(fontsize=16)
        ax.grid(True)
        ax.set_aspect('equal', adjustable='box')
        
        output_image_path = os.path.join(output_dir, f"data_validation_{sample['uid']}.png")
        plt.savefig(output_image_path, dpi=150)
        logging.info(f"可视化图已保存到: {output_image_path}")
        plt.close(fig)

        # --- 量化分析 ---
        logging.info("\n--- 量化分析 (整个5分钟观测窗口) ---")
        
        # 使用精确复刻的函数进行手动计算
        manual_aggs = manually_aggregate_features(obs_original_df, base_dir_path)
        
        comparison_data = {
            "特征": HISTORY_FEATURES,
            "手动计算值": [manual_aggs.get(f, 0.0) for f in HISTORY_FEATURES],
            "样本存储值": history_vec,
        }
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df['差值'] = comparison_df['手动计算值'] - comparison_df['样本存储值']
        
        pd.set_option('display.float_format', '{:.6f}'.format)
        logging.info("特征聚合结果对比:\n" + comparison_df.to_string())

if __name__ == "__main__":
    # 检查训练集或验证集
    db_type = 'train' # 'val'
    db_path = f'data/processed_lmdb_v2/{db_type}'
    output_dir = 'cursor_drafts'
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    if not os.path.exists(db_path):
        logging.error(f"数据库路径不存在: {db_path}")
    else:
        analyze_sample(db_path, output_dir) 