import lmdb
import pickle
import numpy as np
import os
from pathlib import Path
import random

def inspect_single_sample(sample: dict):
    """打印单个样本的详细信息"""
    print("\n" + "="*50)
    print("样本内容详细检查:")
    print("="*50)
    
    keys_to_check = [
        'history_features', 'history_mask', 'ground_truth_trajectory', 
        'original_history_points', 'environment_roi'
    ]
    
    for key in keys_to_check:
        if key not in sample:
            print(f"❌ 错误: 样本中缺少关键字段 '{key}'")
            continue
            
        data = sample[key]
        print(f"\n--- 字段: {key} ---")
        print(f"  - 类型: {type(data)}")
        if isinstance(data, np.ndarray):
            print(f"  - 数据类型 (dtype): {data.dtype}")
            print(f"  - 形状 (Shape): {data.shape}")
            print(f"  - 最小值: {data.min():.4f}, 最大值: {data.max():.4f}, 均值: {data.mean():.4f}")
            # 打印少量样本数据
            print("  - 数据预览 (前5个点):")
            print(data[:5])
        else:
            print(f"  - 内容: {data}")

    # --- 核心逻辑验证 ---
    print("\n" + "="*50)
    print("核心逻辑验证:")
    print("="*50)
    if 'history_mask' in sample and 'original_history_points' in sample:
        num_ones_in_mask = np.sum(sample['history_mask'])
        len_original_history = len(sample['original_history_points'])
        
        print(f"  - history_mask中'1'的数量: {num_ones_in_mask}")
        print(f"  - original_history_points的长度: {len_original_history}")
        
        if int(num_ones_in_mask) == len_original_history:
            print("  - ✅ 验证通过: 掩码中的有效点数与原始历史轨迹长度一致！")
        else:
            print(f"  - ❌ 验证失败: 掩码中的有效点数({num_ones_in_mask})与原始历史轨迹长度({len_original_history})不一致！")
    else:
        print("  - 无法执行核心逻辑验证，缺少'history_mask'或'original_history_points'。")


def inspect_lmdb_dataset(lmdb_path: Path):
    """打开LMDB数据库并检查一个随机样本。"""
    if not lmdb_path.exists():
        print(f"错误: 数据库路径不存在: {lmdb_path}")
        return

    print(f"正在检查LMDB数据库: {lmdb_path}")
    env = lmdb.open(str(lmdb_path), readonly=True, lock=False, readahead=False, meminit=False)
    
    with env.begin(write=False) as txn:
        num_samples = txn.stat()['entries']
        print(f"数据库中总样本数: {num_samples}")
        
        if num_samples == 0:
            print("数据库为空，无法检查。")
            env.close()
            return

        # 随机选择一个key
        keys = [key for key, _ in txn.cursor()]
        random_key = random.choice(keys)
        
        print(f"\n随机选择样本 (Key: {random_key.decode()})进行检查...")
        
        byteflow = txn.get(random_key)
        sample_data = pickle.loads(byteflow)
        
        inspect_single_sample(sample_data)

    env.close()

if __name__ == "__main__":
    project_root = Path(__file__).resolve().parents[2]
    # 我们检查验证集，因为它通常数据量较小，检查速度更快
    db_path = project_root / "data" / "processed_lmdb_obs_5min_pred_40min_v3_with_mask" / "val"
    
    inspect_lmdb_dataset(db_path) 