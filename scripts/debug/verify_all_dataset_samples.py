import lmdb
import pickle
import numpy as np
from pathlib import Path
from tqdm import tqdm

def verify_dataset_integrity(lmdb_path: Path):
    """
    遍历指定LMDB数据库中的每一个样本，验证'environment_roi'字段的形状。
    """
    if not lmdb_path.exists():
        print(f"❌ 错误: 数据库路径不存在: {lmdb_path}")
        return False

    print(f"\n" + "="*60)
    print(f"🔍 开始全面审计数据库: {lmdb_path.name}")
    print("="*60)
    
    env = lmdb.open(str(lmdb_path), readonly=True, lock=False, readahead=False, meminit=False)
    
    is_valid = True
    bad_samples = []

    with env.begin(write=False) as txn:
        num_samples = txn.stat()['entries']
        print(f"  数据库中总样本数: {num_samples}")
        
        if num_samples == 0:
            print("  ⚠️  数据库为空。")
            env.close()
            return True

        with txn.cursor() as cursor:
            for key, value in tqdm(cursor, total=num_samples, desc="  审计进度"):
                try:
                    sample_data = pickle.loads(value)
                    
                    # 检查 'environment_roi' 是否存在
                    if 'environment_roi' not in sample_data:
                        is_valid = False
                        bad_samples.append((key.decode(), "缺少 'environment_roi' 字段"))
                        continue

                    # 检查形状
                    roi_shape = sample_data['environment_roi'].shape
                    expected_shape_prefix = (3600, 13, 9, 9)
                    
                    if roi_shape != expected_shape_prefix:
                        is_valid = False
                        bad_samples.append((key.decode(), f"形状不匹配！期望 {expected_shape_prefix}, 实际 {roi_shape}"))

                except Exception as e:
                    is_valid = False
                    bad_samples.append((key.decode(), f"样本无法被正确解析: {e}"))

    env.close()

    if is_valid:
        print(f"  ✅ 审计通过: 数据库 '{lmdb_path.name}' 中的所有样本都符合标准！")
    else:
        print(f"  ❌ 审计失败: 在数据库 '{lmdb_path.name}' 中发现以下问题样本:")
        for key, reason in bad_samples:
            print(f"    - Key: {key}, 原因: {reason}")
    
    return is_valid

if __name__ == "__main__":
    project_root = Path(__file__).resolve().parents[2]
    base_db_path = project_root / "data" / "processed_lmdb_obs_5min_pred_40min_v3_with_mask"
    
    train_db_path = base_db_path / "train"
    val_db_path = base_db_path / "val"
    
    # 依次审计训练集和验证集
    is_train_ok = verify_dataset_integrity(train_db_path)
    is_val_ok = verify_dataset_integrity(val_db_path)
    
    print("\n" + "="*60)
    if is_train_ok and is_val_ok:
        print("🎉 最终结论: 所有数据集都已通过完整性审计！")
    else:
        print("🔥 最终结论: 数据集存在严重问题，需要重新生成！")
    print("="*60) 