import lmdb
import pickle
import os

lmdb_path = 'data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train'

if not os.path.exists(lmdb_path):
    print(f"错误：找不到LMDB数据库路径: {lmdb_path}")
else:
    try:
        env = lmdb.open(lmdb_path, readonly=True, lock=False, readahead=False, meminit=False)
        with env.begin(write=False) as txn:
            # 获取第一个键
            cursor = txn.cursor()
            if cursor.first():
                key = cursor.key()
                value = cursor.value()
                sample = pickle.loads(value)
                print(f"LMDB样本 (来自 {lmdb_path}) 中的键名：")
                for k in sample.keys():
                    print(f"  - {k}")
            else:
                print(f"LMDB数据库 {lmdb_path} 中没有样本。")
        env.close()
    except Exception as e:
        print(f"加载或读取LMDB时发生错误: {e}") 