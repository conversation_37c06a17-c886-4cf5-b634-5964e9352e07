import lmdb
import pickle
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import random
import logging
from scipy.stats import mode
import rasterio
from rasterio.windows import Window
from pathlib import Path

# --- 配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 14

# --- 从 create_dataset_v2.py 精确复制的函数和常量 ---
STEP_FEATURES = [
    "x_mean", "y_mean", "velocity_x_mean", "velocity_y_mean", 
    "acceleration_x_mean", "acceleration_y_mean", "heading_change_rate_mean",
    "cos_heading_mean", "sin_heading_mean",
    "dem_mean", "land_cover_mode", "slope_mean", 
    "cos_aspect_mean", "sin_aspect_mean"
]

def calculate_kinematic_features(df: pd.DataFrame) -> pd.DataFrame:
    df_kinematic = df.copy()
    if df_kinematic.empty or 'timestamp' not in df_kinematic.columns:
        return df_kinematic
    df_kinematic.set_index(pd.to_datetime(df_kinematic['timestamp'], unit='s'), inplace=True)
    dt_s = df_kinematic.index.to_series().diff().dt.total_seconds().fillna(0)
    dt_s.replace(0, 1e-6, inplace=True)
    df['velocity_x'] = df['x'].diff().fillna(0) / dt_s.values
    df['velocity_y'] = df['y'].diff().fillna(0) / dt_s.values
    df['acceleration_x'] = df['velocity_x'].diff().fillna(0) / dt_s.values
    df['acceleration_y'] = df['velocity_y'].diff().fillna(0) / dt_s.values
    df['heading_rad'] = np.deg2rad(df['heading_deg'])
    df['cos_heading'] = np.cos(df['heading_rad'])
    df['sin_heading'] = np.sin(df['heading_rad'])
    df['heading_change_rate'] = df['heading_rad'].diff().fillna(0) / dt_s.values
    df.replace([np.inf, -np.inf], 0, inplace=True)
    df.fillna(0, inplace=True)
    return df

def manually_aggregate_features_for_step(step_df: pd.DataFrame, sw_data: dict) -> dict:
    if step_df.empty:
        return {}
    
    dem_sw, lc_sw, slope_sw, aspect_sw, sw_transform = sw_data.values()
    
    step_with_kinematics = calculate_kinematic_features(step_df.copy())
    
    xs, ys = step_with_kinematics['x'].values, step_with_kinematics['y'].values
    rows, cols = rasterio.transform.rowcol(sw_transform, xs, ys)
    rows = np.clip(rows, 0, dem_sw.shape[0] - 1)
    cols = np.clip(cols, 0, dem_sw.shape[1] - 1)
    
    step_with_env = step_with_kinematics.copy()
    step_with_env['dem'] = dem_sw[rows, cols]
    step_with_env['land_cover'] = lc_sw[rows, cols]
    step_with_env['slope'] = slope_sw[rows, cols]
    step_with_env['aspect'] = aspect_sw[rows, cols]

    mean_cols = [
        'x', 'y', 'velocity_x', 'velocity_y', 'acceleration_x', 'acceleration_y',
        'heading_change_rate', 'cos_heading', 'sin_heading',
        'dem', 'slope', 'aspect'
    ]
    aggregated = step_with_env[mean_cols].mean().to_dict()
    if not step_with_env['land_cover'].empty:
        aggregated['land_cover'] = mode(step_with_env['land_cover'], keepdims=True)[0][0]
    else:
        aggregated['land_cover'] = -1

    aspect_deg = aggregated.pop('aspect')
    aspect_rad = np.deg2rad(aspect_deg)
    aggregated['cos_aspect'] = np.cos(aspect_rad)
    aggregated['sin_aspect'] = np.sin(aspect_rad)
    
    renamed = {f"{k}_mean": v for k, v in aggregated.items() if k not in ['land_cover', 'cos_aspect', 'sin_aspect']}
    renamed['land_cover_mode'] = aggregated['land_cover']
    renamed['cos_aspect_mean'] = aggregated['cos_aspect']
    renamed['sin_aspect_mean'] = aggregated['sin_aspect']
    
    return {f: renamed.get(f, 0.0) for f in STEP_FEATURES}

def analyze_sample(db_path, output_dir="cursor_drafts", base_dir: Path = Path('.')):
    logging.info(f"正在打开LMDB数据库: {db_path}")
    env = lmdb.open(db_path, readonly=True, lock=False, readahead=False, meminit=False)

    with env.begin(write=False) as txn:
        keys = [key for key, _ in txn.cursor()]
        if not keys:
            logging.error("错误：数据库为空。")
            return
        
        random_key = random.choice(keys)
        logging.info(f"随机选择样本进行分析，Key: {random_key.decode()}")
        
        sample = pickle.loads(txn.get(random_key))

        source_file = Path(sample['source_file'])
        start_time = sample['start_time']
        obs_horizon_s = 300
        pred_horizon_s = 2400
        
        logging.info(f"\n正在读取原始轨迹文件: {source_file}...")
        original_df = pd.read_csv(source_file)
        original_df['timestamp'] = original_df['timestamp_ms'] / 1000.0
        
        full_window_df = original_df[
            (original_df['timestamp'] >= start_time) & 
            (original_df['timestamp'] < start_time + obs_horizon_s + pred_horizon_s)
        ].copy()
        
        obs_df = full_window_df[full_window_df['timestamp'] < start_time + obs_horizon_s]

        # --- 可视化 ---
        logging.info("\n正在生成可视化对比图...")
        fig = plt.figure(figsize=(18, 12))
        gs = fig.add_gridspec(3, 4)
        ax_main = fig.add_subplot(gs[:, :-1])
        ax_roi1 = fig.add_subplot(gs[0, -1])
        ax_roi2 = fig.add_subplot(gs[1, -1])
        ax_roi3 = fig.add_subplot(gs[2, -1])
        
        # 主轨迹图
        ax_main.plot(full_window_df['x'], full_window_df['y'], 'lightgray', linewidth=1, label='完整原始轨迹')
        ax_main.plot(obs_df['x'], obs_df['y'], 'cornflowerblue', linewidth=1.5, label='历史(5分钟)原始轨迹')
        
        history_states = pd.DataFrame(sample['history_states'], columns=STEP_FEATURES)
        ax_main.plot(history_states['x_mean'], history_states['y_mean'], 'ro-', markersize=4, label='历史聚合点 (30个)')
        
        future_traj = sample['future_traj']
        ax_main.plot(future_traj[:, 0], future_traj[:, 1], 'go', markersize=3, label='未来预测点 (240个)')
        ax_main.set_title(f'样本 {sample["uid"]} 可视化', fontsize=18)
        ax_main.set_xlabel('X 坐标 (m)')
        ax_main.set_ylabel('Y 坐标 (m)')
        ax_main.legend()
        ax_main.grid(True)
        ax_main.set_aspect('equal', adjustable='box')

        # ROI子图
        roi_indices = [0, 14, 29]
        roi_axes = [ax_roi1, ax_roi2, ax_roi3]
        for i, (idx, ax) in enumerate(zip(roi_indices, roi_axes)):
            roi = sample['history_rois'][idx]
            # DEM是第一个通道
            ax.imshow(roi[0], cmap='terrain')
            ax.set_title(f'ROI @ 步长 {idx+1}')
            ax.set_xticks([])
            ax.set_yticks([])

        plt.tight_layout()
        output_image_path = os.path.join(output_dir, f"data_validation_seq_{sample['uid']}.png")
        plt.savefig(output_image_path, dpi=150)
        logging.info(f"可视化图已保存到: {output_image_path}")
        plt.close(fig)

        # --- 量化分析 (仅第一个10秒窗口) ---
        logging.info("\n--- 量化分析 (第一个10秒窗口) ---")
        
        step1_df = obs_df[(obs_df['timestamp'] >= start_time) & (obs_df['timestamp'] < start_time + 10)]
        
        # 加载Super Window
        env_dir = source_file.parents[1] / "environment"
        dem_path = env_dir / "dem_aligned.tif"
        with rasterio.open(dem_path) as dem_src:
            dem_transform = dem_src.transform
        margin_m = 500
        x_min, x_max = full_window_df['x'].min() - margin_m, full_window_df['x'].max() + margin_m
        y_min, y_max = full_window_df['y'].min() - margin_m, full_window_df['y'].max() + margin_m
        sw_bounds = rasterio.windows.from_bounds(left=x_min, bottom=y_min, right=x_max, top=y_max, transform=dem_transform)
        sw_transform = rasterio.windows.transform(sw_bounds, dem_transform)

        sw_data = {}
        with rasterio.open(dem_path) as src: sw_data['dem'] = src.read(1, window=sw_bounds)
        with rasterio.open(env_dir / "landcover_aligned.tif") as src: sw_data['lc'] = src.read(1, window=sw_bounds)
        with rasterio.open(env_dir / "slope_aligned.tif") as src: sw_data['slope'] = src.read(1, window=sw_bounds)
        with rasterio.open(env_dir / "aspect_aligned.tif") as src: sw_data['aspect'] = src.read(1, window=sw_bounds)
        sw_data['transform'] = sw_transform
        
        manual_aggs = manually_aggregate_features_for_step(step1_df, sw_data)
        
        comparison_data = {
            "特征": STEP_FEATURES,
            "手动计算值": [manual_aggs.get(f, 0.0) for f in STEP_FEATURES],
            "样本存储值": sample['history_states'][0],
        }
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df['差值'] = comparison_df['手动计算值'] - comparison_df['样本存储值']
        
        pd.set_option('display.float_format', '{:.6f}'.format)
        logging.info("第一个时间步特征聚合结果对比:\n" + comparison_df.to_string())

if __name__ == "__main__":
    db_path = 'data/processed_lmdb_v2/train'
    output_dir = 'cursor_drafts'
    
    if not os.path.exists(db_path):
        logging.error(f"数据库路径不存在: {db_path}")
    else:
        analyze_sample(db_path, output_dir) 