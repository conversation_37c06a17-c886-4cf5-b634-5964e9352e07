"""
创建时间: 2025-07-29
功能: 检查并验证V3带掩码数据集的单个样本，确保其符合设计预期。
"""
import sys
import torch
from pathlib import Path
import numpy as np

# 将项目根目录添加到Python路径
sys.path.append(str(Path(__file__).resolve().parents[2]))

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset

def inspect_sample(sample):
    """打印单个样本的详细信息"""
    print("="*50)
    print("Inspecting a V3 Dataset Sample")
    print("="*50)

    for key, value in sample.items():
        if isinstance(value, torch.Tensor):
            print(f"Key: '{key}'")
            print(f"  - Shape: {value.shape}")
            print(f"  - Dtype: {value.dtype}")
            print(f"  - Min value: {value.min().item():.4f}")
            print(f"  - Max value: {value.max().item():.4f}")
            print(f"  - Mean value: {value.mean().item():.4f}")
            
            # 特别关注 history_mask
            if key == 'history_mask':
                print(f"  - Mask Content (first 30 steps): \n{value.numpy()[:30]}")
                print(f"  - Number of observed points (mask=1): {int(value.sum().item())}")

        elif isinstance(value, np.ndarray):
             print(f"Key: '{key}' (numpy array)")
             print(f"  - Shape: {value.shape}")
             print(f"  - Dtype: {value.dtype}")

        else:
            print(f"Key: '{key}': {value}")
    
    # 验证 history_features 中被掩码的部分是否为0
    history_features = sample['history_features']
    history_mask = sample['history_mask']
    masked_steps_data = history_features[history_mask == 0]
    
    if torch.all(masked_steps_data == 0):
        print("\nVerification PASSED: All masked steps in 'history_features' are correctly zeroed out.")
    else:
        print("\nVerification FAILED: Some masked steps in 'history_features' are NOT zero.")

    print("="*50)


def main():
    # 加载配置
    project_root = Path(__file__).resolve().parents[2]
    main_config_path = project_root / 'configs' / 'main_config.yaml'
    # 确保我们加载的是V3的数据配置
    data_config_path = project_root / 'configs' / 'data_preprocessing.yaml' 
    config = load_config(str(main_config_path), str(data_config_path))
    
    # 强制使用V3数据路径
    config.data_preprocessing.output_path = "data/processed_lmdb_obs_5min_pred_40min_v3_with_mask"
    
    # 实例化数据集，使用正确的初始化方式
    print(f"Loading validation dataset from base path: {config.data_preprocessing.output_path}")
    
    dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    
    if len(dataset) == 0:
        print("错误：数据集为空！")
        return

    # 随机选择一个样本进行检查
    sample_idx = np.random.randint(0, len(dataset))
    print(f"Randomly selected sample index: {sample_idx}")
    sample = dataset[sample_idx]
    
    # 打印样本信息
    inspect_sample(sample)

if __name__ == "__main__":
    main() 