import lmdb
import pickle
import os
from pathlib import Path
import random

def inspect_lmdb_keys(lmdb_path):
    """
    打开指定的LMDB数据库，随机读取一个样本，并打印出其包含的所有键。
    """
    print(f"正在检查 LMDB 数据库: {lmdb_path}")
    if not os.path.exists(lmdb_path):
        print(f"错误: 数据库路径不存在: {lmdb_path}")
        return

    env = lmdb.open(str(lmdb_path), readonly=True, lock=False, readahead=False, meminit=False)
    with env.begin(write=False) as txn:
        num_samples = txn.stat()['entries']
        print(f"数据库中总样本数: {num_samples}")
        if num_samples == 0:
            print("数据库为空，无法检查。")
            env.close()
            return

        # 获取所有键
        all_keys = [key for key, _ in txn.cursor()]
        
        # 随机选择一个键
        random_key = random.choice(all_keys)
        
        print(f"随机选择一个样本进行检查 (Key: {random_key.decode()})")
        
        value = txn.get(random_key)
        if value:
            data = pickle.loads(value)
            print("样本中包含的键 (Keys):")
            for key in data.keys():
                print(f"- {key}")
        else:
            print("错误: 未能获取样本值。")

    env.close()

if __name__ == "__main__":
    # 直接硬编码V3验证集的路径
    project_root = Path(__file__).resolve().parent.parent.parent
    v3_val_db_path = project_root / "data" / "processed_lmdb_obs_5min_pred_40min_v3_with_mask" / "val"
    inspect_lmdb_keys(v3_val_db_path) 