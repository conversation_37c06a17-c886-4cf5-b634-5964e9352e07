import rasterio
import numpy as np
import collections

def inspect_tiff_categories(tiff_path):
    """
    读取TIFF文件，并打印出其中所有唯一的像素值及其出现次数。
    """
    try:
        with rasterio.open(tiff_path) as src:
            data = src.read(1)
            unique_values, counts = np.unique(data, return_counts=True)
            
            print(f"文件 '{tiff_path}' 中的唯一类别值及其数量:")
            
            # 使用Counter进行排序和格式化输出
            value_counts = collections.Counter(dict(zip(unique_values, counts)))
            
            # 忽略背景值（通常是0或一个非常大的nodata值）后进行排序
            if 0 in value_counts:
                print(f"  - 背景/Nodata (值=0): {value_counts[0]} 像素")
                del value_counts[0]

            print("\n有效地理类别值:")
            if not value_counts:
                print("  未找到有效的地理类别值。")
                return

            sorted_values = sorted(value_counts.items(), key=lambda item: item[0])
            
            for value, count in sorted_values:
                print(f"  - 类别代码 {value}: {count} 像素")

    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    landcover_map_path = "environment/landcover_aligned.tif"
    inspect_tiff_categories(landcover_map_path) 