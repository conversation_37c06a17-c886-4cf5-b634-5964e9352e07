# @Time    : 2024/7/22
# <AUTHOR> AI Assistant
# @File    : test_model_and_dataloader.py
# @Description : 该脚本用于测试数据加载器(TrajectoryDataset)和模型(EndToEndModel)是否能协同工作。
#              它会加载数据，取一个批次，通过模型进行前向传播，并检查输出形状是否正确。

import sys
import os
import yaml
# 将项目根目录添加到Python路径中，以便正确导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import torch
from torch.utils.data import DataLoader
from src.data.datasets import LMDBDataset # 更新导入路径
from src.models.end_to_end_model import EndToEndModel # 更新导入路径，假设CNN_RNN_Model是EndToEndModel的早期版本

def main():
    """
    主测试函数。
    """
    # 1. 加载配置文件
    # 使用主配置文件和模型配置文件
    main_config_path = 'configs/main_config.yaml'
    model_config_path = 'configs/models/end_to_end_model.yaml' # 使用模型特定配置

    print(f"正在加载主配置文件: {main_config_path}")
    with open(main_config_path, 'r') as f:
        main_config = yaml.safe_load(f)
    print("主配置加载成功。")

    print(f"正在加载模型配置文件: {model_config_path}")
    with open(model_config_path, 'r') as f:
        model_config = yaml.safe_load(f)
    print("模型配置加载成功。")

    # 将模型配置合并到主配置中，以便Trainer可以访问所有参数
    # 注意：这里需要一个更健壮的配置合并逻辑，但为简单起见，暂时直接传入model_config
    # 理想情况是使用 config_loader.py 进行合并
    config = {**main_config, **model_config} # 简单的合并，实际应使用config_loader

    # 2. 实例化数据集
    print("\n正在实例化 LMDBDataset...")
    try:
        # 从合并后的配置中获取LMDB路径
        dataset = LMDBDataset(config['data']['lmdb_path'])
        print(f"数据集实例化成功。共找到 {len(dataset)} 个样本。")
    except Exception as e:
        print(f"数据集实例化失败: {e}")
        return

    # 3. 创建 DataLoader
    batch_size = 4
    data_loader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    print(f"\nDataLoader 创建成功，批次大小为: {batch_size}")

    # 4. 实例化模型
    print("\n正在实例化 EndToEndModel...")
    try:
        # 模型现在直接接收模型配置
        model = EndToEndModel(model_config['model'])
        model.eval()  # 设置为评估模式
        print("模型实例化成功。")
    except Exception as e:
        print(f"模型实例化失败: {e}")
        return

    # 5. 取一个批次的数据并进行测试
    print("\n正在从 DataLoader 中获取一个批次的数据...")
    try:
        batch_data = next(iter(data_loader))
        obs_trajectory = batch_data['history'] # 使用新的键名
        env_map = batch_data['environment_roi'] # 使用新的键名
        ground_truth_trajectory = batch_data['ground_truth_trajectory'] # 真实轨迹，用于验证形状

        print("成功获取一个批次的数据。")
        print(f"  - 观测轨迹形状: {obs_trajectory.shape}")
        print(f"  - 环境地图形状: {env_map.shape}")
        print(f"  - 真实轨迹形状: {ground_truth_trajectory.shape}")

    except Exception as e:
        print(f"从 DataLoader 获取数据失败: {e}")
        return

    # 6. 将数据输入模型并检查输出
    print("\n正在将数据输入模型进行前向传播...")
    try:
        with torch.no_grad(): # 在评估模式下，不计算梯度
            # 模型现在接收 trajectory_sequence 和 environment_roi
            predictions_output = model(obs_trajectory, env_map, ground_truth_trajectory=ground_truth_trajectory) # 传入GT用于完整测试
        
        predicted_trajectory = predictions_output['predicted_trajectory'] # 从字典中获取预测轨迹
        destination_params = predictions_output['destination_params'] # 获取目的地预测参数

        print("模型前向传播成功。")
        print(f"  - 预测轨迹形状: {predicted_trajectory.shape}")
        print(f"  - 目的地参数形状: {destination_params.shape}")

        # 验证输出形状
        expected_traj_shape = (batch_size, model_config['data']['pred_len'], 2)
        # 目的地参数的形状取决于num_components和output_dim, e.g., (batch_size, num_components * 6)
        expected_dest_params_dim = model_config['model']['destination_predictor']['num_components'] * \
                                   (2 * model_config['model']['destination_predictor']['output_dim'] + 2)
        expected_dest_params_shape = (batch_size, expected_dest_params_dim)

        assert predicted_trajectory.shape == expected_traj_shape, \
            f"预测轨迹形状不匹配! 期望: {expected_traj_shape}, 得到: {predicted_trajectory.shape}"
        assert destination_params.shape == expected_dest_params_shape, \
            f"目的地参数形状不匹配! 期望: {expected_dest_params_shape}, 得到: {destination_params.shape}"
        print(f"\n测试通过！模型输出形状符合预期。")

    except Exception as e:
        print(f"模型前向传播失败或形状验证失败: {e}")

if __name__ == '__main__':
    main() 