# -*- coding: utf-8 -*-
"""
创建时间: 2024年7月22日
功能: 检查 normalization_stats.pkl 中 history_mean 的所有键。
"""

import pickle
import os

def inspect_history_keys(stats_path):
    if not os.path.exists(stats_path):
        print(f"错误: 归一化统计文件未找到: {stats_path}")
        return

    with open(stats_path, 'rb') as f:
        stats = pickle.load(f)
    
    if 'history_mean' in stats:
        print("history_mean 中的所有键:")
        for key in stats['history_mean'].keys():
            print(f"- {key}")
    else:
        print("normalization_stats 中未找到 'history_mean' 键。")

if __name__ == '__main__':
    NORMALIZATION_STATS_PATH = "data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/normalization_stats.pkl"
    inspect_history_keys(NORMALIZATION_STATS_PATH) 