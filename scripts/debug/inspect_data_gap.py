import pandas as pd
from pathlib import Path
import logging

# 配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

# 使用我们一直在用的轨迹文件进行诊断
TRAJECTORY_FILE = Path("/home/<USER>/data/Sucess_or_Die/Yes_train_Yes_destination/Destination_Intent_Predictor__Trajectory_Generator/trajectories/trajectory_099_0_high_mobility.csv")

def inspect_gaps(file_path: Path):
    """
    检查轨迹文件中的时间间隔，并找出最大的间隔。
    """
    if not file_path.exists():
        logging.error(f"文件未找到: {file_path}")
        return

    logging.info(f"正在分析文件: {file_path.name}")
    
    df = pd.read_csv(file_path)
    
    # 确保数据不为空
    if df.empty:
        logging.warning("文件为空，无法分析。")
        return

    # 将时间戳转换为datetime对象
    df['timestamp_dt'] = pd.to_datetime(df['timestamp_ms'], unit='ms')
    
    # 按时间排序以确保顺序正确
    df = df.sort_values(by='timestamp_dt')
    
    # 计算连续点之间的时间差（秒）
    df['time_gap_seconds'] = df['timestamp_dt'].diff().dt.total_seconds()
    
    # 找到最大时间差
    max_gap = df['time_gap_seconds'].max()
    
    logging.info(f"--- 诊断结果 ---")
    logging.info(f"文件中连续两个原始GPS点之间的最大时间间隔为: {max_gap:.2f} 秒")
    
    if max_gap > 10.0:
        logging.warning(f"发现大于10秒的巨大时间间隔！这意味着必然存在至少一个'无原始数据'的10秒聚合窗口。")
        # 找出并显示这个最大间隔所在的位置
        max_gap_idx = df['time_gap_seconds'].idxmax()
        gap_start_row = df.loc[max_gap_idx - 1]
        gap_end_row = df.loc[max_gap_idx]
        
        print("\n--- 产生最大时间间隔的两个连续点 ---")
        print("间隔开始点:")
        print(gap_start_row[['timestamp_dt', 'x', 'y']])
        print("\n间隔结束点:")
        print(gap_end_row[['timestamp_dt', 'x', 'y']])
        print("------------------------------------")

    else:
        logging.info("文件中未发现大于10秒的时间间隔。")

if __name__ == "__main__":
    inspect_gaps(TRAJECTORY_FILE) 