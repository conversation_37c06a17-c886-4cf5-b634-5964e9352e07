#!/usr/bin/env python3
"""
诊断训练问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
from src.data.datasets import LMDBDataset
from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.utils.config_loader import load_config

def check_gradient_flow():
    """检查梯度流动"""
    print("=== 检查梯度流动 ===")
    
    config = load_config('configs/main_config.yaml')
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = TrajectoryPredictorV5(config).to(device)
    
    # 创建数据
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    train_loader = DataLoader(
        train_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=0,
        collate_fn=LMDBDataset.collate_fn
    )
    
    # 获取一个批次
    batch = next(iter(train_loader))
    history_features = batch['history_features'].to(device)
    history_mask = batch['history_mask'].to(device)
    ground_truth_trajectory = batch['ground_truth_trajectory'].to(device)
    ground_truth_destination = batch['ground_truth_destination'].to(device)
    environment_roi = batch['environment_roi'].to(device)
    
    print(f"输入数据形状:")
    print(f"  history_features: {history_features.shape}")
    print(f"  history_mask: {history_mask.shape}")
    print(f"  ground_truth_trajectory: {ground_truth_trajectory.shape}")
    print(f"  environment_roi: {environment_roi.shape}")
    
    # 前向传播
    model.train()
    predicted_trajectory = model(
        history_features=history_features,
        history_mask=history_mask,
        ground_truth_destination=ground_truth_destination,
        environment_roi=environment_roi
    )
    
    print(f"模型输出形状: {predicted_trajectory.shape}")
    
    # 计算损失
    criterion = nn.MSELoss()
    loss = criterion(predicted_trajectory, ground_truth_trajectory)
    print(f"损失值: {loss.item():.6f}")
    
    # 反向传播
    loss.backward()
    
    # 检查梯度
    print("\n梯度统计:")
    total_norm = 0
    param_count = 0
    zero_grad_count = 0
    
    for name, param in model.named_parameters():
        if param.grad is not None:
            param_norm = param.grad.data.norm(2)
            total_norm += param_norm.item() ** 2
            param_count += 1
            
            if param_norm.item() < 1e-7:
                zero_grad_count += 1
                print(f"  {name}: 梯度几乎为零 ({param_norm.item():.2e})")
            elif param_norm.item() > 10:
                print(f"  {name}: 梯度过大 ({param_norm.item():.2e})")
            else:
                print(f"  {name}: 正常 ({param_norm.item():.2e})")
        else:
            print(f"  {name}: 无梯度")
    
    total_norm = total_norm ** (1. / 2)
    print(f"\n总梯度范数: {total_norm:.6f}")
    print(f"零梯度参数数量: {zero_grad_count}/{param_count}")
    
    if zero_grad_count > param_count * 0.5:
        print("⚠️ 警告：超过50%的参数梯度为零，可能存在梯度消失问题")
    
    if total_norm > 100:
        print("⚠️ 警告：梯度范数过大，可能存在梯度爆炸问题")

def check_data_leakage():
    """检查数据泄露"""
    print("\n=== 检查数据泄露 ===")
    
    config = load_config('configs/main_config.yaml')
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    
    # 检查几个样本
    for i in range(3):
        sample = train_dataset[i]
        history_features = sample['history_features']
        ground_truth_trajectory = sample['ground_truth_trajectory']
        
        print(f"\n样本 {i}:")
        print(f"  历史特征最后时间步: {history_features[-1, :2]}")  # x, y
        print(f"  预测轨迹第一时间步: {ground_truth_trajectory[0]}")
        
        # 检查是否有重叠
        last_hist = history_features[-1, :2].numpy()
        first_pred = ground_truth_trajectory[0].numpy()
        distance = np.linalg.norm(last_hist - first_pred)
        
        print(f"  历史结束与预测开始的距离: {distance:.6f}")
        
        if distance < 0.01:  # 归一化空间中很小的距离
            print("  ⚠️ 可能存在数据泄露：历史结束点与预测开始点过于接近")

def check_model_capacity():
    """检查模型容量"""
    print("\n=== 检查模型容量 ===")
    
    config = load_config('configs/main_config.yaml')
    model = TrajectoryPredictorV5(config)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    
    # 检查数据集大小
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    data_size = len(train_dataset)
    
    print(f"训练样本数量: {data_size}")
    print(f"参数/样本比例: {trainable_params/data_size:.1f}")
    
    if trainable_params / data_size > 100:
        print("⚠️ 警告：模型参数过多，可能导致过拟合")
    elif trainable_params / data_size < 1:
        print("⚠️ 警告：模型容量可能不足")

def check_learning_rate():
    """检查学习率设置"""
    print("\n=== 检查学习率设置 ===")
    
    config = load_config('configs/main_config.yaml')
    
    print(f"初始学习率: {config.training.learning_rate}")
    print(f"批次大小: {config.training.batch_size}")
    print(f"优化器: {config.training.optimizer}")
    
    # 建议的学习率范围
    if config.training.learning_rate > 0.01:
        print("⚠️ 警告：学习率可能过高")
    elif config.training.learning_rate < 1e-5:
        print("⚠️ 警告：学习率可能过低")

def check_activation_patterns():
    """检查激活模式"""
    print("\n=== 检查激活模式 ===")
    
    config = load_config('configs/main_config.yaml')
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = TrajectoryPredictorV5(config).to(device)
    
    # 创建数据
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    train_loader = DataLoader(
        train_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=0,
        collate_fn=LMDBDataset.collate_fn
    )
    
    batch = next(iter(train_loader))
    history_features = batch['history_features'].to(device)
    history_mask = batch['history_mask'].to(device)
    ground_truth_destination = batch['ground_truth_destination'].to(device)
    environment_roi = batch['environment_roi'].to(device)
    
    model.eval()
    with torch.no_grad():
        # 检查输入嵌入
        x = model.input_projection(history_features)
        print(f"输入嵌入统计:")
        print(f"  均值: {x.mean().item():.6f}")
        print(f"  标准差: {x.std().item():.6f}")
        print(f"  最小值: {x.min().item():.6f}")
        print(f"  最大值: {x.max().item():.6f}")
        
        # 检查是否有死神经元
        zero_activations = (x.abs() < 1e-6).float().mean()
        print(f"  接近零的激活比例: {zero_activations.item():.2%}")
        
        if zero_activations > 0.5:
            print("  ⚠️ 警告：超过50%的激活接近零，可能存在死神经元问题")

def main():
    """主函数"""
    try:
        print("开始诊断训练问题...")
        
        # 1. 检查梯度流动
        check_gradient_flow()
        
        # 2. 检查数据泄露
        check_data_leakage()
        
        # 3. 检查模型容量
        check_model_capacity()
        
        # 4. 检查学习率
        check_learning_rate()
        
        # 5. 检查激活模式
        check_activation_patterns()
        
        print("\n=== 诊断完成 ===")
        
    except Exception as e:
        print(f"诊断过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
