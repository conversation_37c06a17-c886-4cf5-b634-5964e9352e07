#!/usr/bin/env python3
"""
检查数据的数值范围，分析损失计算
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
from src.data.datasets import LMDBDataset
from src.utils.config_loader import load_config

def analyze_data_ranges():
    """分析数据的数值范围"""
    print("=== 分析数据数值范围 ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 创建数据集
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=0,
        collate_fn=LMDBDataset.collate_fn
    )
    
    # 收集数据统计
    history_features_list = []
    ground_truth_trajectories_list = []
    
    print("收集数据样本...")
    for batch_idx, batch in enumerate(train_loader):
        history_features = batch['history_features']
        ground_truth_trajectory = batch['ground_truth_trajectory']
        
        history_features_list.append(history_features)
        ground_truth_trajectories_list.append(ground_truth_trajectory)
        
        if batch_idx >= 10:  # 只收集前几个批次
            break
    
    # 合并数据
    all_history_features = torch.cat(history_features_list, dim=0)
    all_ground_truth = torch.cat(ground_truth_trajectories_list, dim=0)
    
    print(f"\n数据形状:")
    print(f"  历史特征: {all_history_features.shape}")
    print(f"  预测轨迹: {all_ground_truth.shape}")
    
    print(f"\n历史特征数值范围:")
    print(f"  最小值: {all_history_features.min().item():.6f}")
    print(f"  最大值: {all_history_features.max().item():.6f}")
    print(f"  均值: {all_history_features.mean().item():.6f}")
    print(f"  标准差: {all_history_features.std().item():.6f}")
    
    print(f"\n预测轨迹数值范围:")
    print(f"  最小值: {all_ground_truth.min().item():.6f}")
    print(f"  最大值: {all_ground_truth.max().item():.6f}")
    print(f"  均值: {all_ground_truth.mean().item():.6f}")
    print(f"  标准差: {all_ground_truth.std().item():.6f}")
    
    # 分析各个特征维度
    print(f"\n各特征维度分析:")
    feature_names = ['x', 'y', 'Δt', 'v_n', 'v_e', 'cos_θ', 'sin_θ', 'a_x', 'a_y']
    for i in range(min(9, all_history_features.shape[2])):
        feat_data = all_history_features[:, :, i]
        non_zero_mask = feat_data != 0
        if non_zero_mask.any():
            non_zero_data = feat_data[non_zero_mask]
            print(f"  {feature_names[i]}: 范围[{non_zero_data.min().item():.4f}, {non_zero_data.max().item():.4f}], "
                  f"均值={non_zero_data.mean().item():.4f}, 标准差={non_zero_data.std().item():.4f}")
        else:
            print(f"  {feature_names[i]}: 全为零")
    
    return all_history_features, all_ground_truth

def analyze_loss_calculation(history_features, ground_truth):
    """分析损失计算"""
    print(f"\n=== 分析损失计算 ===")
    
    # 模拟预测结果
    batch_size, seq_len, feature_dim = ground_truth.shape
    
    # 情况1：完全随机预测
    random_prediction = torch.randn_like(ground_truth)
    mse_loss = nn.MSELoss()
    random_loss = mse_loss(random_prediction, ground_truth)
    print(f"随机预测的MSE损失: {random_loss.item():.6f}")
    
    # 情况2：零预测
    zero_prediction = torch.zeros_like(ground_truth)
    zero_loss = mse_loss(zero_prediction, ground_truth)
    print(f"零预测的MSE损失: {zero_loss.item():.6f}")
    
    # 情况3：均值预测
    mean_prediction = torch.full_like(ground_truth, ground_truth.mean().item())
    mean_loss = mse_loss(mean_prediction, ground_truth)
    print(f"均值预测的MSE损失: {mean_loss.item():.6f}")
    
    # 情况4：轻微偏移预测
    slight_offset_prediction = ground_truth + 0.1
    slight_loss = mse_loss(slight_offset_prediction, ground_truth)
    print(f"轻微偏移(+0.1)的MSE损失: {slight_loss.item():.6f}")
    
    # 分析数据方差
    gt_variance = ground_truth.var().item()
    print(f"\n真实轨迹的方差: {gt_variance:.6f}")
    print(f"真实轨迹的标准差: {ground_truth.std().item():.6f}")
    
    # 计算理论最小损失（基于数据内在噪声）
    print(f"\n损失分析:")
    print(f"  如果数据已经归一化到[-2, 2]范围，MSE损失通常在0.01-1.0之间")
    print(f"  当前数据范围: [{ground_truth.min().item():.4f}, {ground_truth.max().item():.4f}]")
    print(f"  数据方差: {gt_variance:.6f}")
    print(f"  期望的合理损失范围: {gt_variance * 0.1:.6f} - {gt_variance * 2:.6f}")

def check_normalization_stats():
    """检查归一化统计数据"""
    print(f"\n=== 检查归一化统计数据 ===")
    
    config = load_config('configs/main_config.yaml')
    stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    
    if os.path.exists(stats_path):
        import pickle
        with open(stats_path, 'rb') as f:
            stats = pickle.load(f)
        
        print("归一化统计数据:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
    else:
        print(f"归一化统计文件不存在: {stats_path}")

def main():
    """主函数"""
    try:
        # 1. 分析数据范围
        history_features, ground_truth = analyze_data_ranges()
        
        # 2. 分析损失计算
        analyze_loss_calculation(history_features, ground_truth)
        
        # 3. 检查归一化统计
        check_normalization_stats()
        
        print(f"\n=== 总结 ===")
        print("如果损失值很小（0.01-0.1），可能的原因:")
        print("1. 数据已经很好地归一化，数值范围较小")
        print("2. 模型预测相对准确")
        print("3. MSE损失对小数值敏感")
        print("\n如果损失值异常小（<0.001），可能的问题:")
        print("1. 数据泄露（模型看到了未来信息）")
        print("2. 归一化过度（数据被压缩到很小的范围）")
        print("3. 损失计算有误")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
