#!/usr/bin/env python3
"""
检查LMDB样本结构
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import lmdb
import pickle
import numpy as np
from src.utils.config_loader import load_config

def check_lmdb_samples():
    """检查LMDB样本结构"""
    print("=== 检查LMDB样本结构 ===")
    
    config = load_config('configs/main_config.yaml')
    data_path = config.data_preprocessing.output_path
    train_path = f"{data_path}/train"
    
    if not os.path.exists(train_path):
        print(f"训练集路径不存在: {train_path}")
        return
    
    # 打开LMDB
    env = lmdb.open(train_path, readonly=True, lock=False)
    
    print("检查前20个样本的键结构:")
    missing_mask_count = 0
    total_count = 0
    
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        
        for i in range(min(20, 982)):  # 检查前20个样本
            key, value = cursor.item()
            sample = pickle.loads(value)
            total_count += 1
            
            print(f"\n样本 {i} (键: {key.decode()}):")
            print(f"  样本键: {list(sample.keys())}")
            
            # 检查必需的键
            required_keys = ['history_features', 'history_mask', 'ground_truth_trajectory', 
                           'ground_truth_destination', 'environment_roi']
            
            for req_key in required_keys:
                if req_key in sample:
                    if isinstance(sample[req_key], np.ndarray):
                        print(f"  {req_key}: {sample[req_key].shape} {sample[req_key].dtype}")
                    else:
                        print(f"  {req_key}: {type(sample[req_key])}")
                else:
                    print(f"  ❌ 缺少键: {req_key}")
                    if req_key == 'history_mask':
                        missing_mask_count += 1
            
            if not cursor.next():
                break
    
    print(f"\n统计:")
    print(f"  检查样本数: {total_count}")
    print(f"  缺少history_mask的样本数: {missing_mask_count}")
    
    # 检查更多样本
    print(f"\n检查所有样本的键结构...")
    missing_indices = []
    
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        
        sample_idx = 0
        for key, value in cursor:
            sample = pickle.loads(value)
            
            if 'history_mask' not in sample:
                missing_indices.append(sample_idx)
            
            sample_idx += 1
            
            if sample_idx >= 100:  # 只检查前100个
                break
    
    print(f"前100个样本中缺少history_mask的索引: {missing_indices}")
    
    env.close()

if __name__ == "__main__":
    check_lmdb_samples()
