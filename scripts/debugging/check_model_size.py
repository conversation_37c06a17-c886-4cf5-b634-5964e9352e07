#!/usr/bin/env python3
"""
检查修改后的模型大小
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.utils.config_loader import load_config

def check_model_size():
    """检查模型大小"""
    print("=== 检查修改后的模型大小 ===")
    
    config = load_config('configs/main_config.yaml')
    model = TrajectoryPredictorV5(config)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    
    # 检查数据集大小
    from src.data.datasets import LMDBDataset
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    data_size = len(train_dataset)
    
    print(f"训练样本数量: {data_size}")
    print(f"参数/样本比例: {trainable_params/data_size:.1f}")
    
    if trainable_params / data_size > 50:
        print("⚠️ 警告：模型参数仍然较多")
    elif trainable_params / data_size < 10:
        print("✅ 模型容量合理")
    else:
        print("✅ 模型容量可接受")
    
    # 显示模型架构
    print(f"\n模型架构:")
    print(f"  d_model: {model.d_model}")
    print(f"  n_heads: {model.n_heads}")
    print(f"  n_layers: {model.n_layers}")
    print(f"  input_dim: {model.input_dim}")

if __name__ == "__main__":
    check_model_size()
