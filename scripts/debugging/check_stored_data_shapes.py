#!/usr/bin/env python3
"""
检查存储在LMDB中的数据形状
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import lmdb
import pickle
import numpy as np
from src.utils.config_loader import load_config

def check_stored_data_shapes():
    """检查存储的数据形状"""
    print("=== 检查存储的数据形状 ===")
    
    config = load_config('configs/main_config.yaml')
    data_path = config.data_preprocessing.output_path
    train_path = f"{data_path}/train"
    
    if not os.path.exists(train_path):
        print(f"训练集路径不存在: {train_path}")
        return
    
    # 打开LMDB
    env = lmdb.open(train_path, readonly=True, lock=False)
    
    print("检查前10个样本的数据形状:")
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        
        for i in range(min(10, 982)):  # 检查前10个样本
            key, value = cursor.item()
            sample = pickle.loads(value)
            
            hist_feat = sample['history_features']
            hist_mask = sample['history_mask']
            pred_traj = sample['ground_truth_trajectory']
            env_roi = sample['environment_roi']
            
            print(f"样本 {i}:")
            print(f"  history_features: {hist_feat.shape} {hist_feat.dtype}")
            print(f"  history_mask: {hist_mask.shape} {hist_mask.dtype}")
            print(f"  ground_truth_trajectory: {pred_traj.shape} {pred_traj.dtype}")
            print(f"  environment_roi: {env_roi.shape} {env_roi.dtype}")
            
            # 检查是否有异常形状
            if hist_feat.shape[0] != 360:
                print(f"  ❌ 异常！history_features第一维应该是360，实际是{hist_feat.shape[0]}")
            if hist_feat.shape[1] != 9:
                print(f"  ❌ 异常！history_features第二维应该是9，实际是{hist_feat.shape[1]}")
            if hist_mask.shape[0] != 360:
                print(f"  ❌ 异常！history_mask长度应该是360，实际是{hist_mask.shape[0]}")
            if pred_traj.shape[0] != 120:
                print(f"  ❌ 异常！ground_truth_trajectory第一维应该是120，实际是{pred_traj.shape[0]}")
            if pred_traj.shape[1] != 2:
                print(f"  ❌ 异常！ground_truth_trajectory第二维应该是2，实际是{pred_traj.shape[1]}")
            
            if not cursor.next():
                break
    
    env.close()
    print("数据形状检查完成")

if __name__ == "__main__":
    check_stored_data_shapes()
