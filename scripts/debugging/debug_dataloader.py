#!/usr/bin/env python3
"""
调试DataLoader问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
from torch.utils.data import DataLoader
from src.data.datasets import LMDBDataset
from src.utils.config_loader import load_config

def debug_dataloader():
    """调试DataLoader"""
    print("=== 调试DataLoader ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 创建数据集
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    print(f"数据集大小: {len(train_dataset)}")
    
    # 检查前几个样本
    print("\n检查前5个样本:")
    for i in range(5):
        sample = train_dataset[i]
        print(f"样本 {i}:")
        for key, value in sample.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape} {value.dtype}")
            else:
                print(f"  {key}: {type(value)}")
    
    # 尝试创建DataLoader
    print("\n尝试创建DataLoader...")
    try:
        train_loader = DataLoader(
            train_dataset,
            batch_size=2,  # 小批次用于调试
            shuffle=False,
            num_workers=0,  # 不使用多进程
            collate_fn=LMDBDataset.collate_fn
        )
        print("DataLoader创建成功")
        
        # 尝试获取第一个批次
        print("尝试获取第一个批次...")
        for batch_idx, batch in enumerate(train_loader):
            print(f"批次 {batch_idx}:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    print(f"  {key}: {value.shape} {value.dtype}")
                elif isinstance(value, list):
                    print(f"  {key}: list of {len(value)} items")
                    if len(value) > 0 and hasattr(value[0], 'shape'):
                        print(f"    第一个元素形状: {value[0].shape}")
                else:
                    print(f"  {key}: {type(value)}")
            
            if batch_idx >= 2:  # 只检查前3个批次
                break
        
        print("✅ DataLoader测试成功!")
        
    except Exception as e:
        print(f"❌ DataLoader错误: {e}")
        import traceback
        traceback.print_exc()
        
        # 尝试手动检查问题样本
        print("\n手动检查可能有问题的样本...")
        problematic_indices = []
        for i in range(min(20, len(train_dataset))):
            sample = train_dataset[i]
            hist_feat = sample['history_features']
            if hist_feat.shape != torch.Size([360, 9]):
                print(f"发现异常样本 {i}: history_features形状为 {hist_feat.shape}")
                problematic_indices.append(i)
        
        if problematic_indices:
            print(f"发现 {len(problematic_indices)} 个异常样本: {problematic_indices}")
        else:
            print("前20个样本形状都正常")

def test_single_batch():
    """测试单个批次"""
    print("\n=== 测试单个批次 ===")
    
    config = load_config('configs/main_config.yaml')
    train_dataset = LMDBDataset(config=config, lmdb_type='train', return_mask=True)
    
    # 手动创建一个小批次
    samples = [train_dataset[0], train_dataset[1]]
    
    print("手动批次中的样本:")
    for i, sample in enumerate(samples):
        print(f"样本 {i}:")
        hist_feat = sample['history_features']
        print(f"  history_features: {hist_feat.shape} {hist_feat.dtype}")
    
    # 尝试手动collate
    try:
        from torch.utils.data._utils.collate import default_collate
        batch = default_collate(samples)
        print("手动collate成功:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape} {value.dtype}")
    except Exception as e:
        print(f"手动collate失败: {e}")
        
        # 尝试使用自定义collate_fn
        try:
            batch = LMDBDataset.collate_fn(samples)
            print("自定义collate成功:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    print(f"  {key}: {value.shape} {value.dtype}")
                elif isinstance(value, list):
                    print(f"  {key}: list of {len(value)} items")
        except Exception as e2:
            print(f"自定义collate也失败: {e2}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_dataloader()
    test_single_batch()
