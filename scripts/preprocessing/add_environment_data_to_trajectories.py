#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建时间: 2025-01-19
功能: 为轨迹数据添加环境信息（DEM、地表覆盖、坡度、坡向）。本脚本读取原始轨迹CSV文件和环境GeoTIFF地图数据，通过地理坐标查询每个轨迹点对应的环境特征值，并将这些特征作为新列添加到轨迹DataFrame中，最终保存为增强后的CSV文件。
输入:
  - 原始轨迹CSV文件：位于 `--trajectory_dir` 参数指定的目录（默认为 `data/trajectories`）。
  - 环境地图GeoTIFF文件：其路径通过 `configs/data_preprocessing.yaml` 中 `data_preprocessing.env_maps` 和 `data_preprocessing.environment_path` 配置指定。
  - 命令行参数：
    - `--config`: 主配置文件路径，默认为 `configs/main_config.yaml`。本脚本将同时加载 `configs/data_preprocessing.yaml`。
    - `--trajectory_dir`: 原始轨迹数据目录，默认为 `data/trajectories`。
    - `--output_dir`: 增强后轨迹文件的输出目录，默认为 `data/trajectories_with_env`。
    - `--num_files`: 要处理的文件数量，0表示处理所有文件，默认为0。
输出:
  - 包含DEM、地表覆盖、坡度、坡向等环境特征的增强轨迹CSV文件，保存到 `--output_dir` 指定的目录下。
原理及处理方法:
  - **配置加载:** 使用 `src.utils.config_loader` 加载并合并 `main_config.yaml` 和 `data_preprocessing.yaml`，获取环境地图文件的相对路径和根目录。
  - **环境数据查询 (`query_environment_data`):**
    1. 接收轨迹点的X、Y坐标数组和环境地图配置。
    2. 针对DEM、地表覆盖、坡度、坡向等每个环境图层，使用 `rasterio.open` 打开GeoTIFF文件。
    3. 利用 `rasterio.transform.rowcol` 将输入的地理坐标转换为栅格数据的行/列像素索引。
    4. 从相应的栅格数据中提取对应像素位置的值，并存储到结果字典中。处理无效索引和缺失值（例如，地表覆盖默认填充255表示未分类，其他填充NaN）。
    5. 返回包含所有查询到的环境特征的字典。
  - **单个轨迹文件处理 (`process_single_trajectory_file`):**
    1. 读取单个原始轨迹CSV文件到Pandas DataFrame。
    2. 调用 `query_environment_data` 为所有轨迹点查询环境信息。
    3. 将查询到的环境数据（dem, land_cover, slope, aspect）作为新列添加到轨迹DataFrame中。
    4. 保存增强后的DataFrame到指定的输出路径，并打印处理统计信息（如有效点的百分比）。
  - **主函数 (`main`):**
    1. 解析命令行参数，设置日志记录。
    2. 根据配置获取轨迹输入和输出目录，并创建输出目录。
    3. 遍历 `trajectory_dir` 下的所有CSV文件（可根据 `num_files` 限制数量）。
    4. 对每个轨迹文件调用 `process_single_trajectory_file` 进行处理，并使用 `tqdm` 显示进度。
    5. 打印最终的处理摘要（成功处理的文件数量）。
"""
import os
import pandas as pd
import numpy as np
import rasterio
from rasterio.transform import rowcol
from pathlib import Path
import logging
import argparse
from tqdm import tqdm
import yaml

# 导入我们自己的config_loader
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.utils.config_loader import load_config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def query_environment_data(x_coords, y_coords, env_maps, env_dir):
    """
    为给定的坐标查询环境数据
    
    Args:
        x_coords: x坐标数组
        y_coords: y坐标数组
        env_maps: 环境地图配置字典
        env_dir: 环境数据目录
    
    Returns:
        dict: 包含DEM、地表覆盖、坡度、坡向数据的字典
    """
    results = {
        'dem': np.full(len(x_coords), np.nan, dtype=np.float32),
        'land_cover': np.full(len(x_coords), 255, dtype=np.uint8),  # 默认未分类
        'slope': np.full(len(x_coords), np.nan, dtype=np.float32),
        'aspect': np.full(len(x_coords), np.nan, dtype=np.float32)
    }
    
    # 查询DEM数据
    dem_path = os.path.join(env_dir, env_maps['dem'])
    if os.path.exists(dem_path):
        try:
            with rasterio.open(dem_path) as src:
                rows, cols = rowcol(src.transform, x_coords, y_coords)
                # 转换为numpy数组以支持比较操作
                rows = np.array(rows)
                cols = np.array(cols)
                # 确保索引在有效范围内
                valid_mask = (rows >= 0) & (rows < src.height) & (cols >= 0) & (cols < src.width)
                if np.any(valid_mask):
                    dem_values = src.read(1)[rows[valid_mask], cols[valid_mask]]
                    results['dem'][valid_mask] = dem_values
        except Exception as e:
            logging.warning(f"读取DEM文件时出错: {e}")
    
    # 查询地表覆盖数据
    lc_path = os.path.join(env_dir, env_maps['landcover'])
    if os.path.exists(lc_path):
        try:
            with rasterio.open(lc_path) as src:
                rows, cols = rowcol(src.transform, x_coords, y_coords)
                rows = np.array(rows)
                cols = np.array(cols)
                valid_mask = (rows >= 0) & (rows < src.height) & (cols >= 0) & (cols < src.width)
                if np.any(valid_mask):
                    lc_values = src.read(1)[rows[valid_mask], cols[valid_mask]]
                    results['land_cover'][valid_mask] = lc_values
        except Exception as e:
            logging.warning(f"读取地表覆盖文件时出错: {e}")
    
    # 查询坡度数据
    slope_path = os.path.join(env_dir, env_maps['slope'])
    if os.path.exists(slope_path):
        try:
            with rasterio.open(slope_path) as src:
                rows, cols = rowcol(src.transform, x_coords, y_coords)
                rows = np.array(rows)
                cols = np.array(cols)
                valid_mask = (rows >= 0) & (rows < src.height) & (cols >= 0) & (cols < src.width)
                if np.any(valid_mask):
                    slope_values = src.read(1)[rows[valid_mask], cols[valid_mask]]
                    results['slope'][valid_mask] = slope_values
        except Exception as e:
            logging.warning(f"读取坡度文件时出错: {e}")
    
    # 查询坡向数据
    aspect_path = os.path.join(env_dir, env_maps['aspect'])
    if os.path.exists(aspect_path):
        try:
            with rasterio.open(aspect_path) as src:
                rows, cols = rowcol(src.transform, x_coords, y_coords)
                rows = np.array(rows)
                cols = np.array(cols)
                valid_mask = (rows >= 0) & (rows < src.height) & (cols >= 0) & (cols < src.width)
                if np.any(valid_mask):
                    aspect_values = src.read(1)[rows[valid_mask], cols[valid_mask]]
                    results['aspect'][valid_mask] = aspect_values
        except Exception as e:
            logging.warning(f"读取坡向文件时出错: {e}")
    
    return results

def process_single_trajectory_file(trajectory_file, env_maps, env_dir, output_dir):
    """
    处理单个轨迹文件，添加环境数据
    
    Args:
        trajectory_file: 轨迹文件路径
        env_maps: 环境地图配置
        env_dir: 环境数据目录
        output_dir: 输出目录
    """
    try:
        # 读取轨迹数据
        df = pd.read_csv(trajectory_file)
        logging.info(f"处理文件: {Path(trajectory_file).name}, 轨迹点数: {len(df)}")
        
        # 查询环境数据
        env_data = query_environment_data(df['x'].values, df['y'].values, env_maps, env_dir)
        
        # 添加环境数据列
        df['dem'] = env_data['dem']
        df['land_cover'] = env_data['land_cover']
        df['slope'] = env_data['slope']
        df['aspect'] = env_data['aspect']
        
        # 计算地表覆盖的众数（用于聚合）
        df['land_cover_mode'] = df['land_cover']  # 单个点的众数就是其本身
        
        # 保存增强后的轨迹文件
        output_file = os.path.join(output_dir, Path(trajectory_file).name)
        df.to_csv(output_file, index=False)
        
        # 统计信息
        valid_dem = np.sum(~np.isnan(env_data['dem']))
        valid_lc = np.sum(env_data['land_cover'] != 255)
        valid_slope = np.sum(~np.isnan(env_data['slope']))
        valid_aspect = np.sum(~np.isnan(env_data['aspect']))
        
        logging.info(f"  - 有效DEM点数: {valid_dem}/{len(df)} ({100*valid_dem/len(df):.1f}%)")
        logging.info(f"  - 有效地表覆盖点数: {valid_lc}/{len(df)} ({100*valid_lc/len(df):.1f}%)")
        logging.info(f"  - 有效坡度点数: {valid_slope}/{len(df)} ({100*valid_slope/len(df):.1f}%)")
        logging.info(f"  - 有效坡向点数: {valid_aspect}/{len(df)} ({100*valid_aspect/len(df):.1f}%)")
        
        return True
        
    except Exception as e:
        logging.error(f"处理文件 {trajectory_file} 时出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="为轨迹数据添加环境信息")
    parser.add_argument('--config', type=str, default='configs/main_config.yaml', help='主配置文件路径。本脚本将同时加载 `configs/data_preprocessing.yaml`。')
    parser.add_argument('--trajectory_dir', type=str, default='data/trajectories', help='轨迹数据目录')
    parser.add_argument('--output_dir', type=str, default='data/trajectories_with_env', help='输出目录')
    parser.add_argument('--num_files', type=int, default=0, help='处理的文件数量，0表示处理所有文件')
    
    args = parser.parse_args()
    
    # 加载并合并配置
    config = load_config(args.config, 'configs/data_preprocessing.yaml')
    
    env_maps = config['data_preprocessing']['env_maps']
    env_dir = config['data_preprocessing']['environment_path']
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取所有轨迹文件
    trajectory_files = [f for f in os.listdir(args.trajectory_dir) if f.endswith('.csv')]
    trajectory_files.sort()
    
    if args.num_files > 0:
        trajectory_files = trajectory_files[:args.num_files]
    
    logging.info(f"开始处理 {len(trajectory_files)} 个轨迹文件...")
    
    # 处理每个轨迹文件
    success_count = 0
    for trajectory_file in tqdm(trajectory_files, desc="处理轨迹文件"):
        file_path = os.path.join(args.trajectory_dir, trajectory_file)
        if process_single_trajectory_file(file_path, env_maps, env_dir, args.output_dir):
            success_count += 1
    
    logging.info(f"处理完成！成功处理 {success_count}/{len(trajectory_files)} 个文件")
    logging.info(f"增强后的轨迹文件保存在: {args.output_dir}")

if __name__ == '__main__':
    main() 