"""
创建时间: 2024-07-22
功能: 主数据预处理脚本，用于从原始轨迹（CSV格式）和环境GIS数据（GeoTIFF格式）生成可用于机器学习模型训练的LMDB格式数据集。本脚本集成了数据加载、数据集划分、在线归一化统计计算和LMDB数据写入等核心功能。
输入:
  - 原始轨迹CSV文件：其路径通过 `configs/data_preprocessing.yaml` 中的 `data_preprocessing.trajectory_path` 指定。
  - 环境GIS数据（DEM, Land Cover, Slope, Aspect）：其路径通过 `configs/data_preprocessing.yaml` 中的 `data_preprocessing.environment_path` 和 `data_preprocessing.env_maps` 配置指定。
  - 配置文件：`configs/main_config.yaml` 和 `configs/data_preprocessing.yaml`，包含所有预处理参数，如时间窗口、特征列表、路径、LMDB容量等。
  - 命令行参数：用于覆盖配置文件中的默认值或启用调试功能，包括 `--base_dir`, `--trajectory_dir`, `--output_dir`, `--environment`, `--force`, `--num_files`, `--num_workers`, `--val_split_ratio`, `--debug_one_file`, `--stats_files_sample_size`, `--commit_batch_size`。
输出:
  - LMDB格式的训练集和验证集：存储在 `configs/data_preprocessing.yaml` 中 `data_preprocessing.output_path` 指定的目录下（例如：`data/processed_lmdb_.../train` 和 `data/processed_lmdb_.../val`）。
  - 归一化统计文件：`normalization_stats.pkl`，保存到LMDB输出目录下，包含了所有轨迹和目标特征的均值和标准差，用于后续模型训练中的数据归一化和反归一化。
原理及处理方法:
  - **Python路径管理:** 动态将项目根目录添加到 `sys.path`，确保能够正确导入所有自定义模块。
  - **配置加载与参数合并:** 脚本首先从 `configs/main_config.yaml` 加载主配置，然后使用 `configs/data_preprocessing.yaml` 对其进行覆盖和补充，确保所有预处理相关的配置项都已正确加载和设置。
  - **文件清理与目录创建:** 根据配置文件中的 `clear_existing_data` 标志，清理现有的LMDB输出目录，并创建必要的目录结构（`output_dir/train` 和 `output_dir/val`）以存储生成的LMDB数据库。
  - **轨迹文件加载与划分:** 从指定的轨迹数据目录读取所有CSV轨迹文件，并根据 `data_preprocessing.val_split_ratio`（可在命令行中覆盖）将其随机划分为训练集和验证集。支持通过 `debugging.use_small_dataset` 和 `debugging.num_files` 参数在调试模式下处理小文件子集。
  - **在线归一化统计计算 (`compute_online_normalization_stats`):**
    1. 利用多进程池并行处理训练集中的一部分文件（由 `stats_files_sample_size` 控制数量）。
    2. 每个子进程调用 `scripts.preprocessing.sample_generator._process_single_file_for_stats` 函数，对单个轨迹文件生成的数据样本（运动学特征和目标点）应用Welford算法，计算其局部均值、方差和样本数。本函数现在直接接收完整的 `config` 字典。
    3. 主进程负责合并所有子进程返回的局部统计量，累积得到全局的均值和标准差。
    4. 将最终计算出的所有历史轨迹特征和目标点坐标的归一化统计量（均值和标准差）序列化并保存为 `normalization_stats.pkl` 文件。
  - **LMDB数据集写入 (`process_and_write_lmdb`):**
    1. 使用多进程并行处理所有训练集和验证集的轨迹文件。
    2. 每个子进程调用 `scripts.preprocessing.sample_generator._process_single_file` 函数，负责从单个轨迹文件生成完整的轨迹样本。每个样本包括历史观测序列、环境ROI、真实未来轨迹和真实目标点等。在此阶段，轨迹和目标特征会根据之前计算的归一化统计量进行归一化。本函数现在直接接收完整的 `config` 字典和 `normalization_stats`。
    3. 生成的样本（Python对象）被 `pickle` 序列化为二进制数据，并通过 `lmdb_writer.process_and_write_lmdb` 函数分批高效地写入相应的LMDB数据库（训练集写入 `output_dir/train`，验证集写入 `output_dir/val`）。
    4. 支持调试模式（`--debug_one_file`），此时仅处理一个文件且不使用多进程，便于排查问题。
  - **环境ROI提取集成:** 在样本生成过程中，会动态调用 `src.data.environment_processor` 中的函数，根据轨迹点在环境GIS数据上裁剪并提取指定大小的环境ROI（包括DEM、坡度、坡向的正弦/余弦、One-Hot编码的地表覆盖），作为模型的重要输入特征。
  - **健壮性与日志:** 脚本包含详细的日志记录，提供处理进度、警告和错误信息，以及时反馈运行状态。同时，通过异常处理机制确保在文件I/O、数据处理或进程间通信失败时，脚本能够优雅地处理。
"""
import logging
import pickle
import random
import shutil
import sys
from pathlib import Path
from tqdm import tqdm
from omegaconf import DictConfig

# --- 路径设置 ---
# 将项目根目录添加到系统路径中，以便导入自定义模块
project_root = Path(__file__).resolve().parents[2]
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# --- 新的导入 ---
from src.utils.config_loader import load_config
from src.utils.welford_stats import update_welford_mean_std, finalize_welford_mean_std
from src.utils.compute_stats import compute_raster_stats # 新增导入
from src.data.lmdb_writer import LMDBWriter
from scripts.preprocessing.sample_generator import generate_samples_for_file, _process_single_file_for_stats

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

def process_and_save_data(config: DictConfig):
    """
    从原始轨迹文件和环境GIS数据生成归一化统计量和样本，并保存到LMDB。
    采用单线程循环以保证稳定性和调试的简易性。
    """
    trajectory_dir = project_root / config.data_preprocessing.trajectory_path
    output_dir = project_root / config.data_preprocessing.output_path
    
    # 解析环境文件路径
    env_dir = project_root / config.data_preprocessing.environment_path
    env_paths = {k: str(env_dir / v) for k, v in config.data_preprocessing.env_maps.items()}

    if config.data_preprocessing.clear_existing_data and output_dir.exists():
        logging.info(f"正在清理现有输出目录: {output_dir}")
        shutil.rmtree(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    (output_dir / 'train').mkdir(exist_ok=True)
    (output_dir / 'val').mkdir(exist_ok=True)

    # --- 文件划分 ---
    all_files = sorted([f for f in trajectory_dir.iterdir() if f.suffix == '.csv'])
    random.seed(42)
    random.shuffle(all_files)

    split_idx = int(len(all_files) * (1 - config.data_preprocessing.val_split_ratio))
    train_files, val_files = all_files[:split_idx], all_files[split_idx:]
    logging.info(f"总文件数: {len(all_files)}, 训练文件数: {len(train_files)}, 验证文件数: {len(val_files)}")
    
    stats_path = output_dir / 'normalization_stats.pkl'

    # --- 1. 计算环境数据 (DEM, Slope) 的全局统计数据 ---
    logging.info("步骤 1/4: 开始计算环境数据 (DEM, Slope) 的全局统计数据...")
    dem_mean, dem_std = compute_raster_stats(env_paths['dem'])
    slope_mean, slope_std = compute_raster_stats(env_paths['slope'])
    logging.info(f"DEM  统计: Mean={dem_mean:.2f}, Std={dem_std:.2f}")
    logging.info(f"Slope统计: Mean={slope_mean:.2f}, Std={slope_std:.2f}")

    # --- 1. 串行计算归一化统计数据 (仅使用训练集) ---
    logging.info("步骤 1/4: 开始串行计算归一化统计数据 (仅使用训练集)...")
    
    history_x_agg, history_y_agg, history_dt_agg = (0, 0.0, 0.0), (0, 0.0, 0.0), (0, 0.0, 0.0)
    target_x_agg, target_y_agg = (0, 0.0, 0.0), (0, 0.0, 0.0)

    for file_path in tqdm(train_files, desc="计算统计数据"):
        history_points, target_points = _process_single_file_for_stats(file_path)
        if history_points.size > 0:
            for x_val in history_points[:, 0]:
                history_x_agg = update_welford_mean_std(history_x_agg, x_val)
            for y_val in history_points[:, 1]:
                history_y_agg = update_welford_mean_std(history_y_agg, y_val)
            for dt_val in history_points[:, 2]:
                history_dt_agg = update_welford_mean_std(history_dt_agg, dt_val)
        if target_points.size > 0:
            for x_val in target_points[:, 0]:
                target_x_agg = update_welford_mean_std(target_x_agg, x_val)
            for y_val in target_points[:, 1]:
                target_y_agg = update_welford_mean_std(target_y_agg, y_val)
    
    hist_mean_x, hist_std_x = finalize_welford_mean_std(history_x_agg)
    hist_mean_y, hist_std_y = finalize_welford_mean_std(history_y_agg)
    hist_mean_dt, hist_std_dt = finalize_welford_mean_std(history_dt_agg)
    target_mean_x, target_std_x = finalize_welford_mean_std(target_x_agg)
    target_mean_y, target_std_y = finalize_welford_mean_std(target_y_agg)

    normalization_stats = {
        'history_mean': {'x': hist_mean_x, 'y': hist_mean_y, 'delta_t': hist_mean_dt},
        'history_std': {'x': hist_std_x, 'y': hist_std_y, 'delta_t': hist_std_dt},
        'target_mean': {'x': target_mean_x, 'y': target_mean_y},
        'target_std': {'x': target_std_x, 'y': target_std_y},
        # --- 环境统计数据 ---
        'dem_mean': dem_mean,
        'dem_std': dem_std,
        'slope_mean': slope_mean,
        'slope_std': slope_std,
    }

    with open(stats_path, 'wb') as f:
        pickle.dump(normalization_stats, f)
    logging.info(f"归一化统计数据已保存到: {stats_path}")

    # --- 3. 串行生成所有样本 ---
    logging.info("步骤 3/4: 开始串行生成所有样本 (这可能会花费一些时间)...")
    all_samples = {'train': [], 'val': []}
    
    # 处理训练集
    for file_path in tqdm(train_files, desc="生成训练样本"):
        sample_list = generate_samples_for_file(file_path, config, normalization_stats, env_paths)
        if sample_list:
            all_samples['train'].extend(sample_list)
            
    # 处理验证集
    for file_path in tqdm(val_files, desc="生成验证样本"):
        sample_list = generate_samples_for_file(file_path, config, normalization_stats, env_paths)
        if sample_list:
            all_samples['val'].extend(sample_list)

    logging.info(f"总共生成 {len(all_samples['train'])} 个训练样本和 {len(all_samples['val'])} 个验证样本。")

    # --- 4. 将样本写入LMDB ---
    for split in ['train', 'val']:
        lmdb_path = output_dir / split
        writer = LMDBWriter(str(lmdb_path), map_size=int(config.data_preprocessing.lmdb_map_size_gb * 1024*1024*1024))
        for i, sample in enumerate(tqdm(all_samples[split], desc=f"写入 {split} LMDB")):
            key = f"{i:09d}"
            writer.add_sample(key, sample)
        writer.close()
        logging.info(f"{split} 数据集处理完成！总样本数: {len(all_samples[split])}")

def main():
    """主函数，加载配置并启动处理流程。"""
    config = load_config('configs/main_config.yaml', 'configs/data_preprocessing.yaml')
    process_and_save_data(config)

if __name__ == '__main__':
    main()