#!/usr/bin/env python3
"""
V5模型数据预处理脚本 - 30分钟历史窗口，40分钟预测窗口
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import logging
import pickle
import random
import shutil
from tqdm import tqdm
import numpy as np
import pandas as pd

from src.utils.config_loader import load_config
from src.data.lmdb_writer import LMDBWriter
from scripts.preprocessing.sample_generator import generate_samples_for_file

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('preprocessing_v5_30min.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def compute_normalization_stats(trajectory_files, config):
    """计算归一化统计数据"""
    logger.info("计算归一化统计数据...")
    
    all_target_points = []
    all_history_points = []
    
    for file_path in tqdm(trajectory_files[:100], desc="计算统计"):  # 只用前100个文件
        try:
            # 使用现有的统计函数
            from scripts.preprocessing.sample_generator import _process_single_file_for_stats
            target_points, history_points = _process_single_file_for_stats(file_path)
            
            if len(target_points) > 0:
                all_target_points.append(target_points)
            # 仅添加形状正确的历史点数据
            if history_points.ndim == 2 and history_points.shape[1] == 3 and len(history_points) > 0:
                all_history_points.append(history_points)
            else:
                logger.warning(f"文件 {file_path.name} 的历史点数据形状不正确或为空: {history_points.shape}")
                
        except Exception as e:
            logger.warning(f"统计计算时跳过文件 {file_path}: {e}")
            continue
    
    # 合并数据
    if all_target_points:
        all_target_points = np.vstack(all_target_points)
    else:
        raise ValueError("没有有效的目标轨迹数据")
    
    if all_history_points:
        all_history_points = np.vstack(all_history_points)
    else:
        raise ValueError("没有有效的历史轨迹数据")
    
    # 计算统计数据
    stats = {
        'target_mean': {
            'x': float(np.mean(all_target_points[:, 0])),
            'y': float(np.mean(all_target_points[:, 1]))
        },
        'target_std': {
            'x': float(np.std(all_target_points[:, 0])),
            'y': float(np.std(all_target_points[:, 1]))
        },
        'history_mean': {
            'x': float(np.mean(all_history_points[:, 0])),
            'y': float(np.mean(all_history_points[:, 1])),
            'delta_t': float(np.mean(all_history_points[:, 2]))
        },
        'history_std': {
            'x': float(np.std(all_history_points[:, 0])),
            'y': float(np.std(all_history_points[:, 1])),
            'delta_t': float(np.std(all_history_points[:, 2]))
        }
    }
    
    # 确保标准差不为零，特别是针对delta_t
    for key in ['target_std', 'history_std']:
        for subkey in stats[key]:
            if stats[key][subkey] < 1e-6:
                stats[key][subkey] = 1.0
                logger.warning(f"标准差过小，设置为1.0: {key}.{subkey}")
    
    logger.info("归一化统计数据计算完成")
    return stats

def process_files(trajectory_files, split_name, config, normalization_stats, env_paths):
    """处理文件并生成样本"""
    logger.info(f"处理 {split_name} 数据集...")
    
    all_samples = []
    
    for file_path in tqdm(trajectory_files, desc=f"处理{split_name}"):
        try:
            samples = generate_samples_for_file(file_path, config, normalization_stats, env_paths)
            all_samples.extend(samples)
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {e}")
    
    logger.info(f"{split_name} 数据集处理完成，共生成 {len(all_samples)} 个样本")
    return all_samples

def get_environment_paths(config):
    """获取环境数据路径"""
    env_dir = Path(config.data_preprocessing.environment_path)
    env_paths = {}
    
    for key, filename in config.data_preprocessing.env_maps.items():
        env_paths[key] = str(env_dir / filename)
    
    return env_paths

def main():
    """主函数"""
    logger.info("=== 开始V5模型数据预处理 (30分钟历史窗口) ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 检查配置
    obs_horizon_s = config.data_preprocessing.observation_horizon_s
    pred_horizon_s = config.data_preprocessing.prediction_horizon_s
    agg_interval_s = config.data_preprocessing.agg_interval_s
    
    logger.info(f"配置信息:")
    logger.info(f"  观测窗口: {obs_horizon_s}秒 ({obs_horizon_s/60:.1f}分钟)")
    logger.info(f"  预测窗口: {pred_horizon_s}秒 ({pred_horizon_s/60:.1f}分钟)")
    logger.info(f"  聚合间隔: {agg_interval_s}秒")
    logger.info(f"  历史点数: {obs_horizon_s // agg_interval_s}")
    logger.info(f"  预测点数: {pred_horizon_s // agg_interval_s}")
    
    # 获取轨迹文件
    trajectory_dir = Path(config.data_preprocessing.trajectory_path)
    if not trajectory_dir.exists():
        raise FileNotFoundError(f"轨迹数据目录不存在: {trajectory_dir}")
    
    trajectory_files = list(trajectory_dir.glob("*.csv"))
    if not trajectory_files:
        raise FileNotFoundError(f"在 {trajectory_dir} 中没有找到CSV文件")
    
    logger.info(f"找到 {len(trajectory_files)} 个轨迹文件")
    
    # 数据集划分
    random.seed(42)
    random.shuffle(trajectory_files)
    
    val_split_ratio = config.data_preprocessing.val_split_ratio
    split_idx = int(len(trajectory_files) * (1 - val_split_ratio))
    train_files = trajectory_files[:split_idx]
    val_files = trajectory_files[split_idx:]
    
    logger.info(f"数据集划分: 训练集 {len(train_files)}, 验证集 {len(val_files)}")
    
    # 计算归一化统计数据
    normalization_stats = compute_normalization_stats(train_files, config)
    
    # 获取环境数据路径
    env_paths = get_environment_paths(config)
    
    # 创建输出目录
    output_dir = Path(config.data_preprocessing.output_path + "_v5_30min")
    if config.data_preprocessing.clear_existing_data and output_dir.exists():
        logger.info(f"清理现有输出目录: {output_dir}")
        shutil.rmtree(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    (output_dir / 'train').mkdir(exist_ok=True)
    (output_dir / 'val').mkdir(exist_ok=True)
    
    # 保存归一化统计数据
    stats_path = output_dir / "normalization_stats.pkl"
    with open(stats_path, 'wb') as f:
        pickle.dump(normalization_stats, f)
    logger.info(f"归一化统计数据已保存到: {stats_path}")
    
    # 处理各个数据集
    for split_name, files in [("train", train_files), ("val", val_files)]:
        if not files:
            logger.warning(f"跳过空的 {split_name} 数据集")
            continue
        
        # 生成样本
        samples = process_files(files, split_name, config, normalization_stats, env_paths)
        
        if not samples:
            logger.warning(f"{split_name} 数据集没有生成任何样本")
            continue
        
        # 写入LMDB
        lmdb_path = output_dir / split_name
        writer = LMDBWriter(str(lmdb_path), map_size=50 * 1024**3)  # 50GB
        
        for i, sample in enumerate(tqdm(samples, desc=f"写入 {split_name} LMDB")):
            key = f"{i:09d}"
            writer.add_sample(key, sample)
        
        writer.close()
        logger.info(f"{split_name} 数据集已写入LMDB: {lmdb_path}")
        
        # 打印样本信息
        if samples:
            sample = samples[0]
            logger.info(f"{split_name} 样本信息:")
            logger.info(f"  历史特征形状: {sample['history_features'].shape}")
            logger.info(f"  历史掩码形状: {sample['history_mask'].shape}")
            logger.info(f"  预测轨迹形状: {sample['ground_truth_trajectory'].shape}")
            logger.info(f"  环境ROI形状: {sample['environment_roi'].shape}")
    
    logger.info("=== V5模型数据预处理完成 ===")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"观测窗口: {obs_horizon_s}秒 ({obs_horizon_s/60:.1f}分钟)")
    logger.info(f"预测窗口: {pred_horizon_s}秒 ({pred_horizon_s/60:.1f}分钟)")

if __name__ == "__main__":
    main() 