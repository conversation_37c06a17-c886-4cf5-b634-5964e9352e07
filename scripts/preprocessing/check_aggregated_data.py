#!/usr/bin/env python3
"""
检查聚合后的数据质量和正确性
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pickle
import lmdb
import logging

from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_original_vs_aggregated_data():
    """对比原始数据和聚合后的数据"""
    logger.info("=== 检查原始数据 vs 聚合数据 ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 选择一个原始轨迹文件进行测试
    trajectory_dir = Path(config.data_preprocessing.trajectory_path)
    trajectory_files = list(trajectory_dir.glob("*.csv"))
    
    if not trajectory_files:
        logger.error("没有找到轨迹文件")
        return
    
    # 选择第一个文件进行测试
    test_file = trajectory_files[0]
    logger.info(f"测试文件: {test_file}")
    
    # 读取原始数据
    df_original = pd.read_csv(test_file)
    logger.info(f"原始数据长度: {len(df_original)}")
    logger.info(f"原始数据列: {df_original.columns.tolist()}")
    logger.info(f"原始数据前5行:\n{df_original.head()}")
    
    # 应用聚合函数
    from scripts.preprocessing.sample_generator import aggregate_trajectory_data
    
    agg_interval_s = config.data_preprocessing.agg_interval_s
    freq_hz = config.data_preprocessing.trajectory.freq_hz
    
    df_aggregated = aggregate_trajectory_data(df_original, agg_interval_s, freq_hz)
    logger.info(f"聚合后数据长度: {len(df_aggregated)}")
    logger.info(f"聚合后数据列: {df_aggregated.columns.tolist()}")
    logger.info(f"聚合后数据前5行:\n{df_aggregated.head()}")
    
    # 计算聚合比例
    aggregation_ratio = len(df_original) / len(df_aggregated) if len(df_aggregated) > 0 else 0
    logger.info(f"聚合比例: {aggregation_ratio:.2f} (期望: {agg_interval_s})")
    
    # 检查时间间隔
    if len(df_aggregated) > 1:
        original_time_diff = df_original['timestamp_ms'].diff().dropna()
        aggregated_time_diff = df_aggregated['timestamp_ms'].diff().dropna()
        
        logger.info(f"原始时间间隔统计:")
        logger.info(f"  均值: {original_time_diff.mean():.2f}ms")
        logger.info(f"  标准差: {original_time_diff.std():.2f}ms")
        logger.info(f"  最小值: {original_time_diff.min():.2f}ms")
        logger.info(f"  最大值: {original_time_diff.max():.2f}ms")
        
        logger.info(f"聚合后时间间隔统计:")
        logger.info(f"  均值: {aggregated_time_diff.mean():.2f}ms")
        logger.info(f"  标准差: {aggregated_time_diff.std():.2f}ms")
        logger.info(f"  最小值: {aggregated_time_diff.min():.2f}ms")
        logger.info(f"  最大值: {aggregated_time_diff.max():.2f}ms")
        logger.info(f"  期望间隔: {agg_interval_s * 1000}ms")
    
    # 可视化对比
    create_comparison_plot(df_original, df_aggregated, test_file.name)
    
    return df_original, df_aggregated

def check_lmdb_samples():
    """检查LMDB中的样本数据"""
    logger.info("=== 检查LMDB样本数据 ===")
    
    config = load_config('configs/main_config.yaml')
    
    # 检查训练集
    train_lmdb_path = f"{config.data_preprocessing.output_path}/train"
    
    if not os.path.exists(train_lmdb_path):
        logger.error(f"LMDB路径不存在: {train_lmdb_path}")
        return
    
    # 打开LMDB
    env = lmdb.open(train_lmdb_path, readonly=True, lock=False)
    
    # 获取样本数量
    with env.begin() as txn:
        cursor = txn.cursor()
        sample_count = 0
        sample_keys = []
        
        for key, _ in cursor:
            try:
                key_int = int(key.decode())
                sample_keys.append(key_int)
                sample_count += 1
            except ValueError:
                continue
    
    logger.info(f"LMDB中的样本数量: {sample_count}")
    
    if sample_count == 0:
        logger.warning("LMDB中没有样本数据")
        env.close()
        return
    
    # 检查前几个样本
    sample_keys.sort()
    num_check = min(3, len(sample_keys))
    
    for i in range(num_check):
        key = sample_keys[i]
        with env.begin() as txn:
            sample_data = txn.get(f'{key}'.encode())
            if sample_data is None:
                continue
            
            sample = pickle.loads(sample_data)
            
            logger.info(f"\n样本 {key}:")
            logger.info(f"  文件路径: {sample.get('file_path', 'N/A')}")
            logger.info(f"  历史特征形状: {sample['history_features'].shape}")
            logger.info(f"  历史掩码形状: {sample['history_mask'].shape}")
            logger.info(f"  预测轨迹形状: {sample['ground_truth_trajectory'].shape}")
            logger.info(f"  目标点形状: {sample['ground_truth_destination'].shape}")
            logger.info(f"  环境ROI形状: {sample['environment_roi'].shape}")
            
            # 检查聚合信息
            if 'aggregation_info' in sample:
                agg_info = sample['aggregation_info']
                logger.info(f"  聚合信息:")
                logger.info(f"    聚合间隔: {agg_info.get('agg_interval_s', 'N/A')}秒")
                logger.info(f"    原始长度: {agg_info.get('original_total_len', 'N/A')}")
                logger.info(f"    聚合长度: {agg_info.get('aggregated_total_len', 'N/A')}")
                logger.info(f"    预测点数: {agg_info.get('prediction_horizon_points', 'N/A')}")
                logger.info(f"    历史点数: {agg_info.get('max_history_len_points', 'N/A')}")
            
            # 检查数据范围
            history_features = sample['history_features']
            pred_trajectory = sample['ground_truth_trajectory']
            
            logger.info(f"  历史特征统计:")
            logger.info(f"    X坐标范围: [{history_features[:, 0].min():.2f}, {history_features[:, 0].max():.2f}]")
            logger.info(f"    Y坐标范围: [{history_features[:, 1].min():.2f}, {history_features[:, 1].max():.2f}]")
            logger.info(f"    时间间隔范围: [{history_features[:, 2].min():.2f}, {history_features[:, 2].max():.2f}]")
            
            logger.info(f"  预测轨迹统计:")
            logger.info(f"    X坐标范围: [{pred_trajectory[:, 0].min():.2f}, {pred_trajectory[:, 0].max():.2f}]")
            logger.info(f"    Y坐标范围: [{pred_trajectory[:, 1].min():.2f}, {pred_trajectory[:, 1].max():.2f}]")
            
            # 检查掩码
            mask_valid_count = np.sum(sample['history_mask'] == 1.0)
            logger.info(f"  有效历史点数: {mask_valid_count}/{len(sample['history_mask'])}")
    
    env.close()

def create_comparison_plot(df_original, df_aggregated, filename):
    """创建原始数据和聚合数据的对比图"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 轨迹对比
    ax1 = axes[0, 0]
    ax1.plot(df_original['x'], df_original['y'], 'b-', alpha=0.7, linewidth=1, label='原始轨迹')
    ax1.plot(df_aggregated['x'], df_aggregated['y'], 'r-', alpha=0.8, linewidth=2, label='聚合轨迹')
    ax1.scatter(df_aggregated['x'], df_aggregated['y'], c='red', s=20, alpha=0.8)
    ax1.set_title('轨迹对比')
    ax1.set_xlabel('X坐标')
    ax1.set_ylabel('Y坐标')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # 时间序列对比 - X坐标
    ax2 = axes[0, 1]
    ax2.plot(range(len(df_original)), df_original['x'], 'b-', alpha=0.7, linewidth=1, label='原始X')
    ax2.plot(np.linspace(0, len(df_original)-1, len(df_aggregated)), df_aggregated['x'], 'r-', alpha=0.8, linewidth=2, label='聚合X')
    ax2.set_title('X坐标时间序列')
    ax2.set_xlabel('时间点')
    ax2.set_ylabel('X坐标')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 时间序列对比 - Y坐标
    ax3 = axes[1, 0]
    ax3.plot(range(len(df_original)), df_original['y'], 'b-', alpha=0.7, linewidth=1, label='原始Y')
    ax3.plot(np.linspace(0, len(df_original)-1, len(df_aggregated)), df_aggregated['y'], 'r-', alpha=0.8, linewidth=2, label='聚合Y')
    ax3.set_title('Y坐标时间序列')
    ax3.set_xlabel('时间点')
    ax3.set_ylabel('Y坐标')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 时间间隔分布
    ax4 = axes[1, 1]
    if len(df_original) > 1:
        original_diff = df_original['timestamp_ms'].diff().dropna() / 1000.0
        ax4.hist(original_diff, bins=30, alpha=0.7, label='原始时间间隔', color='blue')
    
    if len(df_aggregated) > 1:
        aggregated_diff = df_aggregated['timestamp_ms'].diff().dropna() / 1000.0
        ax4.hist(aggregated_diff, bins=30, alpha=0.7, label='聚合时间间隔', color='red')
    
    ax4.set_title('时间间隔分布')
    ax4.set_xlabel('时间间隔 (秒)')
    ax4.set_ylabel('频次')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'aggregation_comparison_{filename}.png', dpi=300, bbox_inches='tight')
    logger.info(f"对比图已保存: aggregation_comparison_{filename}.png")
    plt.show()

def check_normalization_stats():
    """检查归一化统计数据"""
    logger.info("=== 检查归一化统计数据 ===")
    
    config = load_config('configs/main_config.yaml')
    stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    
    if not os.path.exists(stats_path):
        logger.error(f"归一化统计文件不存在: {stats_path}")
        return
    
    with open(stats_path, 'rb') as f:
        stats = pickle.load(f)
    
    logger.info("归一化统计数据:")
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")

def main():
    """主函数"""
    try:
        # 1. 检查原始数据 vs 聚合数据
        df_original, df_aggregated = check_original_vs_aggregated_data()
        
        # 2. 检查LMDB样本数据
        check_lmdb_samples()
        
        # 3. 检查归一化统计
        check_normalization_stats()
        
        logger.info("=== 数据检查完成 ===")
        
    except Exception as e:
        logger.error(f"检查过程中出现错误: {e}", exc_info=True)

if __name__ == "__main__":
    main()
