import logging
import random
from pathlib import Path

import numpy as np
import pandas as pd
import torch
from omegaconf import DictConfig

from src.data.environment_processor import get_environment_roi_for_trajectory

def normalize_data(data, stats, key_prefix):
    """使用给定的统计数据对轨迹数据进行归一化 (x, y)。"""
    mean_x = stats[f"{key_prefix}_mean"]['x']
    std_x = stats[f"{key_prefix}_std"]['x']
    mean_y = stats[f"{key_prefix}_mean"]['y']
    std_y = stats[f"{key_prefix}_std"]['y']

    normalized_data = data.copy()
    normalized_data[..., 0] = (data[..., 0] - mean_x) / (std_x + 1e-6)
    normalized_data[..., 1] = (data[..., 1] - mean_y) / (std_y + 1e-6)
    return normalized_data

def normalize_data_with_dt(data, stats, key_prefix):
    """归一化带dt的数据 (x, y, delta_t)。"""
    mean_x = stats[f"{key_prefix}_mean"]['x']
    std_x = stats[f"{key_prefix}_std"]['x']
    mean_y = stats[f"{key_prefix}_mean"]['y']
    std_y = stats[f"{key_prefix}_std"]['y']
    mean_dt = stats[f"{key_prefix}_mean"]['delta_t']
    std_dt = stats[f"{key_prefix}_std"]['delta_t']

    normalized_data = data.copy()
    normalized_data[..., 0] = (data[..., 0] - mean_x) / (std_x + 1e-6)
    normalized_data[..., 1] = (data[..., 1] - mean_y) / (std_y + 1e-6)
    normalized_data[..., 2] = (data[..., 2] - mean_dt) / (std_dt + 1e-6)
    return normalized_data

def aggregate_trajectory_data(df, agg_interval_s=10, freq_hz=1):
    """
    对轨迹数据进行时间聚合

    Args:
        df: 原始轨迹数据 (1Hz采样)
        agg_interval_s: 聚合间隔 (秒)
        freq_hz: 原始采样频率

    Returns:
        aggregated_df: 聚合后的数据
    """
    if agg_interval_s <= 0 or len(df) == 0:
        return df

    # 计算聚合窗口大小
    window_size = int(agg_interval_s * freq_hz)  # 10秒 * 1Hz = 10个点

    if window_size <= 1:
        return df

    # 计算聚合后的长度
    agg_len = len(df) // window_size
    if len(df) % window_size != 0:
        agg_len += 1

    # 初始化聚合后的数据
    agg_data = []

    for i in range(agg_len):
        start_idx = i * window_size
        end_idx = min((i + 1) * window_size, len(df))
        window_data = df.iloc[start_idx:end_idx]

        if len(window_data) == 0:
            continue

        # 聚合不同类型的特征
        agg_row = {}

        # 数值类型特征：取平均值
        numerical_features = ['x', 'y', 'timestamp_ms']
        for feature in numerical_features:
            if feature in window_data.columns:
                agg_row[feature] = window_data[feature].mean()

        # 方向类型特征：需要特殊处理 (如果有的话)
        # TODO: 如果有航向角等方向特征，需要使用圆形平均

        # 分类类型特征：使用众数 (如果有的话)
        # TODO: 如果有地表覆盖物等分类特征，需要使用one-hot编码

        agg_data.append(agg_row)

    agg_df = pd.DataFrame(agg_data)
    return agg_df

def generate_samples_for_file(
    file_path: Path,
    config: DictConfig,
    normalization_stats: dict,
    env_paths: dict,
) -> list:
    """
    根据最终确认的“事件驱动、累积历史”逻辑为单个轨迹文件生成样本。
    """
    try:
        data_cfg = config.data_preprocessing
        freq_hz = data_cfg.trajectory.freq_hz
        
        min_obs_duration_s = data_cfg.masking.min_duration_min * 60
        max_obs_duration_s = data_cfg.masking.max_duration_min * 60
        min_gap_duration_s = data_cfg.masking.min_gap_duration_min * 60
        max_gap_duration_s = data_cfg.masking.max_gap_duration_min * 60
        
        # 修复：使用聚合后的有效频率计算点数
        agg_interval_s = data_cfg.agg_interval_s  # 10秒聚合间隔
        agg_freq = freq_hz / agg_interval_s  # 聚合后的有效频率：1Hz / 10s = 0.1Hz

        prediction_horizon_points = int(data_cfg.prediction_horizon_s * agg_freq)  # 1200s * 0.1Hz = 120个点
        max_history_len_points = int(data_cfg.masking.max_history_len_s * agg_freq)  # 300s * 0.1Hz = 30个点

        # 读取原始数据
        df_original = pd.read_csv(file_path)

        # 进行时间聚合
        agg_interval_s = data_cfg.agg_interval_s  # 10秒聚合间隔
        df = aggregate_trajectory_data(df_original, agg_interval_s, freq_hz)

        total_len = len(df)
        samples = []

        # 记录聚合信息
        logging.debug(f"文件 {file_path}: 原始长度 {len(df_original)} -> 聚合长度 {total_len}")

        if total_len == 0:
            logging.warning(f"聚合后数据为空，跳过文件: {file_path}")
            return []

        # 为每个文件设置不同的随机种子，确保不同文件有不同的观测/间歇模式
        file_seed = hash(str(file_path)) % (2**32)  # 基于文件路径生成种子
        random.seed(file_seed)

        # 1. 模拟观测/间歇交替，构建覆盖整个轨迹的掩码
        # 确保轨迹开始就是观测期（符合实际逻辑）
        full_trajectory_mask = np.zeros(total_len, dtype=np.float32)
        current_idx = 0
        is_obs_period = True  # 确保从观测期开始

        while current_idx < total_len:
            if is_obs_period:
                duration = random.randint(min_obs_duration_s, max_obs_duration_s)
                end_idx = min(current_idx + duration, total_len)
                full_trajectory_mask[current_idx:end_idx] = 1.0
                current_idx = end_idx
            else:
                duration = random.randint(min_gap_duration_s, max_gap_duration_s)
                current_idx = min(current_idx + duration, total_len)
            is_obs_period = not is_obs_period

        # 2. 找到所有观测窗口的结束点作为事件点（每次观测结束后立即预测）
        diff_mask = np.diff(full_trajectory_mask, prepend=0, append=0)
        event_indices = np.where(diff_mask == -1)[0] - 1  # 观测窗口结束的索引

        # 检查轨迹是否以观测状态结束，如果是，也添加为事件点
        if full_trajectory_mask[-1] == 1.0:
            event_indices = np.append(event_indices, total_len - 1)

        # 3. 为每个事件点生成样本
        for event_idx in event_indices:
            if event_idx < 0: continue

            # 检查是否有足够的未来数据
            if event_idx + 1 + prediction_horizon_points > total_len:
                continue

            # a. 截取未来轨迹
            future_start_idx = event_idx + 1
            future_end_idx = future_start_idx + prediction_horizon_points
            future_trajectory_raw = df.iloc[future_start_idx:future_end_idx][['x', 'y']].values

            # b. 截取累积历史（包含观测+间歇的完整历史）
            history_df = df.iloc[:event_idx + 1]
            history_mask_raw = full_trajectory_mask[:event_idx + 1]

            # 检查是否有足够的观测数据
            observed_count = np.sum(history_mask_raw == 1.0)
            if observed_count < 2: continue

            # 计算完整历史的delta_t（包含观测和间歇时间）
            delta_t = history_df['timestamp_ms'].diff().fillna(0).values / 1000.0
            history_points_raw = history_df[['x', 'y']].values
            history_with_dt = np.hstack([history_points_raw, delta_t[:, np.newaxis]])

            # c. 归一化
            normalized_future = normalize_data(future_trajectory_raw, normalization_stats, 'target')
            normalized_history = normalize_data_with_dt(history_with_dt, normalization_stats, 'history')

            # d. 填充/截断历史数据和掩码
            padded_history = np.zeros((max_history_len_points, 3), dtype=np.float32)
            padded_mask = np.zeros(max_history_len_points, dtype=np.float32)
            num_history_points = len(normalized_history)
            len_to_copy = min(num_history_points, max_history_len_points)

            # 填充历史数据（包含观测和间歇）- 使用左对齐，从时间0开始
            padded_history[:len_to_copy] = normalized_history[:len_to_copy]
            # 填充掩码（标识观测/间歇状态）- 使用左对齐，从时间0开始
            padded_mask[:len_to_copy] = history_mask_raw[:len_to_copy]

            # e. 提取环境ROI（基于完整历史轨迹）
            env_rois_df = pd.DataFrame(history_points_raw, columns=['x', 'y'])
            env_rois = get_environment_roi_for_trajectory(env_rois_df, env_paths, config, normalization_stats)
            padded_env_rois = np.zeros((max_history_len_points, *env_rois.shape[1:]), dtype=np.float32)
            if env_rois.shape[0] > 0:
                padded_env_rois[:len_to_copy] = env_rois[:len_to_copy]

            sample = {
                'file_path': str(file_path),
                'event_idx': event_idx,
                'history_features': padded_history,  # 保持为numpy数组
                'history_mask': padded_mask,  # 保持为numpy数组
                'ground_truth_trajectory': normalized_future,  # 保持为numpy数组
                'ground_truth_destination': normalized_future[-1],  # 保持为numpy数组
                'original_history_points': history_with_dt,
                'environment_roi': padded_env_rois,  # 保持为numpy数组
                'debug_full_history_points': history_df[['x', 'y']].values,
                'debug_full_history_mask': history_mask_raw,
            }
            samples.append(sample)
        
        return samples

    except Exception as e:
        logging.error(f"处理文件 {file_path} 时发生严重错误: {e}", exc_info=True)
        return []

def _process_single_file_for_stats(file_path: Path) -> tuple:
    """为计算统计数据提取轨迹点 (x, y, delta_t)。使用聚合数据。"""
    try:
        # 读取原始数据
        df_original = pd.read_csv(file_path)

        # 进行时间聚合（使用默认10秒间隔）
        df = aggregate_trajectory_data(df_original, agg_interval_s=10, freq_hz=1)

        if len(df) == 0:
            return (np.array([]), np.array([]))

        # 目标轨迹 (x, y) - 聚合后的数据
        target_points = df[['x', 'y']].values

        # 历史轨迹 (x, y, delta_t) - 聚合后的数据
        delta_t = df['timestamp_ms'].diff().fillna(10000).values / 1000.0  # 聚合后默认间隔10秒
        history_points = np.hstack([df[['x', 'y']].values, delta_t[:, np.newaxis]])

        return (history_points, target_points)
    except Exception as e:
        logging.warning(f"统计计算时处理文件 {file_path} 失败: {e}")
        return (np.array([]), np.array([]))

