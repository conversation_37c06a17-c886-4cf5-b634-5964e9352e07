#!/usr/bin/env python3
"""
聚合版本的数据预处理脚本
先进行10秒时间聚合，然后基于聚合后的点提取ROI
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import logging
import pickle
import numpy as np
import pandas as pd
import random
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm

from src.utils.config_loader import load_config
from src.data.lmdb_writer import LMDBWriter
from src.data.environment_processor import get_environment_roi_for_trajectory

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('preprocessing_aggregated.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def aggregate_trajectory_data(df, agg_interval_s=10, freq_hz=1):
    """
    对轨迹数据进行时间聚合
    
    Args:
        df: 原始轨迹数据 (1Hz采样)
        agg_interval_s: 聚合间隔 (秒)
        freq_hz: 原始采样频率
    
    Returns:
        aggregated_df: 聚合后的数据
    """
    if agg_interval_s <= 0 or len(df) == 0:
        return df
    
    # 计算聚合窗口大小
    window_size = int(agg_interval_s * freq_hz)  # 10秒 * 1Hz = 10个点
    
    if window_size <= 1:
        return df
    
    # 计算聚合后的长度
    agg_len = len(df) // window_size
    if len(df) % window_size != 0:
        agg_len += 1
    
    # 初始化聚合后的数据
    agg_data = []
    
    for i in range(agg_len):
        start_idx = i * window_size
        end_idx = min((i + 1) * window_size, len(df))
        window_data = df.iloc[start_idx:end_idx]
        
        if len(window_data) == 0:
            continue
        
        # 聚合不同类型的特征
        agg_row = {}
        
        # 数值类型特征：取平均值
        numerical_features = ['x', 'y', 'timestamp_ms']
        for feature in numerical_features:
            if feature in window_data.columns:
                agg_row[feature] = window_data[feature].mean()
        
        agg_data.append(agg_row)
    
    agg_df = pd.DataFrame(agg_data)
    return agg_df

def normalize_data(data, normalization_stats, data_type):
    """归一化轨迹数据 (x, y)"""
    if data_type == 'target':
        mean_x = normalization_stats['target_mean']['x']
        std_x = normalization_stats['target_std']['x']
        mean_y = normalization_stats['target_mean']['y']
        std_y = normalization_stats['target_std']['y']
    else:  # history
        mean_x = normalization_stats['history_mean']['x']
        std_x = normalization_stats['history_std']['x']
        mean_y = normalization_stats['history_mean']['y']
        std_y = normalization_stats['history_std']['y']
    
    normalized_data = data.copy()
    normalized_data[..., 0] = (data[..., 0] - mean_x) / (std_x + 1e-6)
    normalized_data[..., 1] = (data[..., 1] - mean_y) / (std_y + 1e-6)
    return normalized_data

def normalize_data_with_dt(data, normalization_stats, data_type):
    """归一化轨迹数据 (x, y, delta_t)"""
    if data_type == 'target':
        mean_x = normalization_stats['target_mean']['x']
        std_x = normalization_stats['target_std']['x']
        mean_y = normalization_stats['target_mean']['y']
        std_y = normalization_stats['target_std']['y']
    else:  # history
        mean_x = normalization_stats['history_mean']['x']
        std_x = normalization_stats['history_std']['x']
        mean_y = normalization_stats['history_mean']['y']
        std_y = normalization_stats['history_std']['y']
    
    mean_dt = normalization_stats['history_mean']['delta_t']
    std_dt = normalization_stats['history_std']['delta_t']
    
    normalized_data = data.copy()
    normalized_data[..., 0] = (data[..., 0] - mean_x) / (std_x + 1e-6)
    normalized_data[..., 1] = (data[..., 1] - mean_y) / (std_y + 1e-6)
    normalized_data[..., 2] = (data[..., 2] - mean_dt) / (std_dt + 1e-6)
    return normalized_data

def generate_aggregated_samples(file_path, config, normalization_stats, env_paths):
    """
    为单个文件生成聚合样本
    """
    try:
        data_cfg = config.data_preprocessing
        agg_interval_s = data_cfg.agg_interval_s  # 10秒聚合间隔
        freq_hz = data_cfg.trajectory.freq_hz
        
        # 计算聚合后的点数
        agg_freq = freq_hz / agg_interval_s  # 0.1Hz
        prediction_horizon_points = int(data_cfg.prediction_horizon_s * agg_freq)  # 120个点
        observation_horizon_points = int(data_cfg.observation_horizon_s * agg_freq)  # 30个点
        
        # 读取并聚合数据
        df = pd.read_csv(file_path)
        agg_df = aggregate_trajectory_data(df, agg_interval_s, freq_hz)
        
        if len(agg_df) < observation_horizon_points + prediction_horizon_points:
            return []
        
        samples = []
        
        # 简化：只在轨迹中间生成一个样本
        start_idx = observation_horizon_points
        end_idx = len(agg_df) - prediction_horizon_points
        
        if start_idx >= end_idx:
            return []
        
        # 选择中间点作为事件点
        event_idx = (start_idx + end_idx) // 2
        
        # 提取历史轨迹
        history_df = agg_df.iloc[event_idx - observation_horizon_points + 1:event_idx + 1]
        history_points = history_df[['x', 'y']].values
        
        # 计算delta_t
        delta_t = history_df['timestamp_ms'].diff().fillna(agg_interval_s * 1000).values / 1000.0
        history_with_dt = np.hstack([history_points, delta_t[:, np.newaxis]])
        
        # 提取未来轨迹
        future_df = agg_df.iloc[event_idx + 1:event_idx + 1 + prediction_horizon_points]
        future_points = future_df[['x', 'y']].values
        
        # 归一化
        normalized_history = normalize_data_with_dt(history_with_dt, normalization_stats, 'history')
        normalized_future = normalize_data(future_points, normalization_stats, 'target')
        
        # 基于聚合后的历史点提取环境ROI
        history_roi_df = pd.DataFrame(history_points, columns=['x', 'y'])
        env_rois = get_environment_roi_for_trajectory(history_roi_df, env_paths, config, normalization_stats)
        
        # 创建掩码（聚合后的点都是有效的）
        history_mask = np.ones(observation_horizon_points, dtype=np.float32)
        
        sample = {
            'file_path': str(file_path),
            'event_idx': event_idx,
            'history_features': normalized_history.astype(np.float32),
            'history_mask': history_mask,
            'ground_truth_trajectory': normalized_future.astype(np.float32),
            'ground_truth_destination': normalized_future[-1].astype(np.float32),
            'environment_roi': env_rois.astype(np.float32),
            'aggregation_info': {
                'agg_interval_s': agg_interval_s,
                'original_length': len(df),
                'aggregated_length': len(agg_df),
                'observation_points': observation_horizon_points,
                'prediction_points': prediction_horizon_points
            }
        }
        samples.append(sample)
        
        return samples
        
    except Exception as e:
        logger.error(f"处理文件 {file_path} 时出错: {e}")
        return []

def compute_aggregated_normalization_stats(trajectory_files, config):
    """计算聚合数据的归一化统计"""
    logger.info("计算聚合数据的归一化统计...")
    
    agg_interval_s = config.data_preprocessing.agg_interval_s
    freq_hz = config.data_preprocessing.trajectory.freq_hz
    
    all_target_points = []
    all_history_points = []
    
    for file_path in tqdm(trajectory_files[:100], desc="计算统计"):  # 只用前100个文件
        try:
            df = pd.read_csv(file_path)
            agg_df = aggregate_trajectory_data(df, agg_interval_s, freq_hz)
            
            if len(agg_df) < 10:
                continue
            
            # 目标点
            target_points = agg_df[['x', 'y']].values
            all_target_points.append(target_points)
            
            # 历史点（包含delta_t）
            delta_t = agg_df['timestamp_ms'].diff().fillna(agg_interval_s * 1000).values / 1000.0
            history_points = np.hstack([target_points, delta_t[:, np.newaxis]])
            all_history_points.append(history_points)
            
        except Exception as e:
            logger.warning(f"统计计算时跳过文件 {file_path}: {e}")
            continue
    
    # 合并数据
    all_target_points = np.vstack(all_target_points)
    all_history_points = np.vstack(all_history_points)
    
    # 计算统计
    stats = {
        'target_mean': {
            'x': float(np.mean(all_target_points[:, 0])),
            'y': float(np.mean(all_target_points[:, 1]))
        },
        'target_std': {
            'x': float(np.std(all_target_points[:, 0])),
            'y': float(np.std(all_target_points[:, 1]))
        },
        'history_mean': {
            'x': float(np.mean(all_history_points[:, 0])),
            'y': float(np.mean(all_history_points[:, 1])),
            'delta_t': float(np.mean(all_history_points[:, 2]))
        },
        'history_std': {
            'x': float(np.std(all_history_points[:, 0])),
            'y': float(np.std(all_history_points[:, 1])),
            'delta_t': float(np.std(all_history_points[:, 2]))
        }
    }
    
    # 确保标准差不为零
    for key in ['target_std', 'history_std']:
        for subkey in stats[key]:
            if stats[key][subkey] < 1e-6:
                stats[key][subkey] = 1.0
    
    logger.info("归一化统计计算完成")
    return stats

def main():
    """主函数"""
    logger.info("=== 开始聚合数据预处理 ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 检查聚合配置
    agg_interval_s = config.data_preprocessing.agg_interval_s
    freq_hz = config.data_preprocessing.trajectory.freq_hz
    
    logger.info(f"聚合间隔: {agg_interval_s}秒")
    logger.info(f"原始频率: {freq_hz}Hz")
    logger.info(f"聚合后频率: {freq_hz/agg_interval_s}Hz")
    
    # 计算聚合后的维度
    obs_points = int(config.data_preprocessing.observation_horizon_s / agg_interval_s)
    pred_points = int(config.data_preprocessing.prediction_horizon_s / agg_interval_s)
    
    logger.info(f"观测窗口: {obs_points}个点 ({config.data_preprocessing.observation_horizon_s}秒)")
    logger.info(f"预测窗口: {pred_points}个点 ({config.data_preprocessing.prediction_horizon_s}秒)")
    
    # 获取轨迹文件
    trajectory_dir = Path(config.data_preprocessing.input_path)
    trajectory_files = list(trajectory_dir.glob("*.csv"))
    
    if not trajectory_files:
        raise FileNotFoundError(f"在 {trajectory_dir} 中没有找到CSV文件")
    
    logger.info(f"找到 {len(trajectory_files)} 个轨迹文件")
    
    # 数据集划分
    random.seed(42)
    random.shuffle(trajectory_files)
    
    train_ratio = config.data_preprocessing.train_ratio
    val_ratio = config.data_preprocessing.val_ratio
    
    n_train = int(len(trajectory_files) * train_ratio)
    n_val = int(len(trajectory_files) * val_ratio)
    
    train_files = trajectory_files[:n_train]
    val_files = trajectory_files[n_train:n_train + n_val]
    test_files = trajectory_files[n_train + n_val:]
    
    logger.info(f"数据集划分: 训练集 {len(train_files)}, 验证集 {len(val_files)}, 测试集 {len(test_files)}")
    
    # 计算归一化统计
    normalization_stats = compute_aggregated_normalization_stats(train_files, config)
    
    # 获取环境数据路径
    env_paths = get_environment_paths(config)
    
    # 创建输出目录
    output_dir = Path(config.data_preprocessing.output_path + "_aggregated")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存归一化统计
    stats_path = output_dir / "normalization_stats.pkl"
    with open(stats_path, 'wb') as f:
        pickle.dump(normalization_stats, f)
    logger.info(f"归一化统计已保存: {stats_path}")
    
    # 处理各个数据集
    for split_name, files in [("train", train_files), ("val", val_files), ("test", test_files)]:
        if not files:
            continue
        
        logger.info(f"处理 {split_name} 数据集...")
        
        # 创建LMDB写入器
        lmdb_path = output_dir / split_name
        lmdb_writer = LMDBWriter(str(lmdb_path), map_size=50 * 1024**3)  # 50GB
        
        sample_count = 0
        
        # 处理文件
        for file_path in tqdm(files, desc=f"处理{split_name}"):
            samples = generate_aggregated_samples(file_path, config, normalization_stats, env_paths)
            
            for sample in samples:
                lmdb_writer.write_sample(sample_count, sample)
                sample_count += 1
        
        lmdb_writer.close()
        logger.info(f"{split_name} 数据集完成: {sample_count} 个样本写入 {lmdb_path}")
    
    logger.info("=== 聚合数据预处理完成 ===")
    logger.info(f"输出目录: {output_dir}")

if __name__ == "__main__":
    main()
