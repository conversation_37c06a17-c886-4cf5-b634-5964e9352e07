#!/usr/bin/env python3
"""
修复版本的样本生成器，包含正确的10秒时间聚合功能
"""

import pandas as pd
import numpy as np
import random
import logging
from pathlib import Path
from omegaconf import DictConfig
from .environment_roi_extractor import get_environment_roi_for_trajectory

def normalize_data(data, normalization_stats, data_type):
    """归一化轨迹数据 (x, y)"""
    if data_type == 'target':
        mean_x = normalization_stats['target_mean']['x']
        std_x = normalization_stats['target_std']['x']
        mean_y = normalization_stats['target_mean']['y']
        std_y = normalization_stats['target_std']['y']
    else:  # history
        mean_x = normalization_stats['history_mean']['x']
        std_x = normalization_stats['history_std']['x']
        mean_y = normalization_stats['history_mean']['y']
        std_y = normalization_stats['history_std']['y']
    
    normalized_data = data.copy()
    normalized_data[..., 0] = (data[..., 0] - mean_x) / (std_x + 1e-6)
    normalized_data[..., 1] = (data[..., 1] - mean_y) / (std_y + 1e-6)
    return normalized_data

def normalize_data_with_dt(data, normalization_stats, data_type):
    """归一化轨迹数据 (x, y, delta_t)"""
    if data_type == 'target':
        mean_x = normalization_stats['target_mean']['x']
        std_x = normalization_stats['target_std']['x']
        mean_y = normalization_stats['target_mean']['y']
        std_y = normalization_stats['target_std']['y']
    else:  # history
        mean_x = normalization_stats['history_mean']['x']
        std_x = normalization_stats['history_std']['x']
        mean_y = normalization_stats['history_mean']['y']
        std_y = normalization_stats['history_std']['y']
    
    mean_dt = normalization_stats['history_mean']['delta_t']
    std_dt = normalization_stats['history_std']['delta_t']
    
    normalized_data = data.copy()
    normalized_data[..., 0] = (data[..., 0] - mean_x) / (std_x + 1e-6)
    normalized_data[..., 1] = (data[..., 1] - mean_y) / (std_y + 1e-6)
    normalized_data[..., 2] = (data[..., 2] - mean_dt) / (std_dt + 1e-6)
    return normalized_data

def aggregate_trajectory_data(df, agg_interval_s=10, freq_hz=1):
    """
    对轨迹数据进行时间聚合
    
    Args:
        df: 原始轨迹数据 (1Hz采样)
        agg_interval_s: 聚合间隔 (秒)
        freq_hz: 原始采样频率
    
    Returns:
        aggregated_df: 聚合后的数据
        original_indices: 每个聚合点对应的原始数据中心索引
    """
    if agg_interval_s <= 0 or len(df) == 0:
        return df, np.arange(len(df))
    
    # 计算聚合窗口大小
    window_size = int(agg_interval_s * freq_hz)  # 10秒 * 1Hz = 10个点
    
    if window_size <= 1:
        return df, np.arange(len(df))
    
    # 计算聚合后的长度
    agg_len = len(df) // window_size
    if len(df) % window_size != 0:
        agg_len += 1
    
    # 初始化聚合后的数据
    agg_data = []
    original_indices = []
    
    for i in range(agg_len):
        start_idx = i * window_size
        end_idx = min((i + 1) * window_size, len(df))
        window_data = df.iloc[start_idx:end_idx]
        
        if len(window_data) == 0:
            continue
        
        # 聚合不同类型的特征
        agg_row = {}
        
        # 数值类型特征：取平均值
        numerical_features = ['x', 'y', 'timestamp_ms']
        for feature in numerical_features:
            if feature in window_data.columns:
                agg_row[feature] = window_data[feature].mean()
        
        # 方向类型特征：需要特殊处理 (如果有的话)
        # TODO: 如果有航向角等方向特征，需要使用圆形平均
        
        # 分类类型特征：使用众数 (如果有的话)
        # TODO: 如果有地表覆盖物等分类特征，需要使用one-hot编码
        
        agg_data.append(agg_row)
        # 记录聚合点对应的原始数据中心索引
        center_idx = (start_idx + end_idx - 1) // 2
        original_indices.append(center_idx)
    
    agg_df = pd.DataFrame(agg_data)
    return agg_df, np.array(original_indices)

def aggregate_mask(mask, agg_interval_s=10, freq_hz=1):
    """
    对掩码进行聚合：如果窗口内有任何观测数据，则聚合点为观测状态
    """
    if agg_interval_s <= 0 or len(mask) == 0:
        return mask
    
    window_size = int(agg_interval_s * freq_hz)
    if window_size <= 1:
        return mask
    
    agg_len = len(mask) // window_size
    if len(mask) % window_size != 0:
        agg_len += 1
    
    agg_mask = []
    for i in range(agg_len):
        start_idx = i * window_size
        end_idx = min((i + 1) * window_size, len(mask))
        window_mask = mask[start_idx:end_idx]
        
        # 如果窗口内有任何观测数据，则聚合点为观测状态
        agg_mask.append(1.0 if np.any(window_mask == 1.0) else 0.0)
    
    return np.array(agg_mask)

def generate_samples_for_file_with_aggregation(
    file_path: Path,
    config: DictConfig,
    normalization_stats: dict,
    env_paths: dict,
) -> list:
    """
    根据"事件驱动、累积历史"逻辑为单个轨迹文件生成样本，包含10秒时间聚合。
    """
    try:
        data_cfg = config.data_preprocessing
        freq_hz = data_cfg.trajectory.freq_hz
        agg_interval_s = data_cfg.agg_interval_s  # 10秒聚合间隔
        
        min_obs_duration_s = data_cfg.masking.min_duration_min * 60
        max_obs_duration_s = data_cfg.masking.max_duration_min * 60
        min_gap_duration_s = data_cfg.masking.min_gap_duration_min * 60
        max_gap_duration_s = data_cfg.masking.max_gap_duration_min * 60
        
        # 修复：使用聚合后的点数计算
        agg_freq = freq_hz / agg_interval_s  # 聚合后的有效频率：1Hz / 10s = 0.1Hz
        prediction_horizon_points = int(data_cfg.prediction_horizon_s * agg_freq)  # 1200s * 0.1Hz = 120个点
        max_history_len_points = int(data_cfg.masking.max_history_len_s * agg_freq)  # 300s * 0.1Hz = 30个点

        # 读取原始数据
        df = pd.read_csv(file_path)
        
        # 进行时间聚合
        agg_df, original_indices = aggregate_trajectory_data(df, agg_interval_s, freq_hz)
        total_len = len(agg_df)
        
        if total_len == 0:
            logging.warning(f"聚合后数据为空: {file_path}")
            return []
        
        samples = []

        # 为每个文件设置不同的随机种子
        file_seed = hash(str(file_path)) % (2**32)
        random.seed(file_seed)

        # 1. 在原始数据上模拟观测/间歇交替，然后聚合掩码
        original_mask = np.zeros(len(df), dtype=np.float32)
        current_idx = 0
        is_obs_period = True

        while current_idx < len(df):
            if is_obs_period:
                duration = random.randint(min_obs_duration_s, max_obs_duration_s)
                end_idx = min(current_idx + duration, len(df))
                original_mask[current_idx:end_idx] = 1.0
                current_idx = end_idx
            else:
                duration = random.randint(min_gap_duration_s, max_gap_duration_s)
                current_idx = min(current_idx + duration, len(df))
            is_obs_period = not is_obs_period

        # 聚合掩码
        agg_mask = aggregate_mask(original_mask, agg_interval_s, freq_hz)

        # 2. 找到聚合后的观测窗口结束点作为事件点
        diff_mask = np.diff(agg_mask, prepend=0, append=0)
        event_indices = np.where(diff_mask == -1)[0] - 1

        # 检查轨迹是否以观测状态结束
        if agg_mask[-1] == 1.0:
            event_indices = np.append(event_indices, total_len - 1)

        # 3. 为每个事件点生成样本
        for event_idx in event_indices:
            if event_idx < 0:
                continue

            # 检查是否有足够的未来数据
            if event_idx + 1 + prediction_horizon_points > total_len:
                continue

            # a. 截取未来轨迹 (聚合后的数据)
            future_start_idx = event_idx + 1
            future_end_idx = future_start_idx + prediction_horizon_points
            future_trajectory_raw = agg_df.iloc[future_start_idx:future_end_idx][['x', 'y']].values

            # b. 截取累积历史 (聚合后的数据)
            history_df = agg_df.iloc[:event_idx + 1]
            history_mask_raw = agg_mask[:event_idx + 1]

            # 检查是否有足够的观测数据
            observed_count = np.sum(history_mask_raw == 1.0)
            if observed_count < 2:
                continue

            # 计算聚合后历史的delta_t
            delta_t = history_df['timestamp_ms'].diff().fillna(0).values / 1000.0
            history_points_raw = history_df[['x', 'y']].values
            history_with_dt = np.hstack([history_points_raw, delta_t[:, np.newaxis]])

            # c. 归一化
            normalized_future = normalize_data(future_trajectory_raw, normalization_stats, 'target')
            normalized_history = normalize_data_with_dt(history_with_dt, normalization_stats, 'history')

            # d. 填充/截断历史数据和掩码
            padded_history = np.zeros((max_history_len_points, 3), dtype=np.float32)
            padded_mask = np.zeros(max_history_len_points, dtype=np.float32)
            num_history_points = len(normalized_history)
            len_to_copy = min(num_history_points, max_history_len_points)

            # 填充历史数据和掩码
            padded_history[:len_to_copy] = normalized_history[:len_to_copy]
            padded_mask[:len_to_copy] = history_mask_raw[:len_to_copy]

            # e. 提取环境ROI (基于聚合后的历史轨迹)
            env_rois_df = pd.DataFrame(history_points_raw, columns=['x', 'y'])
            env_rois = get_environment_roi_for_trajectory(env_rois_df, env_paths, config, normalization_stats)
            padded_env_rois = np.zeros((max_history_len_points, *env_rois.shape[1:]), dtype=np.float32)
            if env_rois.shape[0] > 0:
                padded_env_rois[:len_to_copy] = env_rois[:len_to_copy]

            sample = {
                'file_path': str(file_path),
                'event_idx': event_idx,
                'history_features': padded_history,
                'history_mask': padded_mask,
                'ground_truth_trajectory': normalized_future,
                'ground_truth_destination': normalized_future[-1],
                'original_history_points': history_with_dt,
                'environment_roi': padded_env_rois,
                'debug_aggregated_history_points': history_df[['x', 'y']].values,
                'debug_aggregated_history_mask': history_mask_raw,
                'aggregation_info': {
                    'agg_interval_s': agg_interval_s,
                    'original_total_len': len(df),
                    'aggregated_total_len': total_len,
                    'prediction_horizon_points': prediction_horizon_points,
                    'max_history_len_points': max_history_len_points
                }
            }
            samples.append(sample)
        
        logging.info(f"文件 {file_path}: 原始长度 {len(df)} -> 聚合长度 {total_len}, 生成样本 {len(samples)} 个")
        return samples

    except Exception as e:
        logging.error(f"处理文件 {file_path} 时发生严重错误: {e}", exc_info=True)
        return []

def _process_single_file_for_stats(file_path: Path) -> tuple:
    """为计算统计数据提取轨迹点 (x, y, delta_t)。"""
    try:
        df = pd.read_csv(file_path)
        
        # 目标轨迹 (x, y)
        target_points = df[['x', 'y']].values
        
        # 历史轨迹 (x, y, delta_t)
        delta_t = df['timestamp_ms'].diff().fillna(0).values / 1000.0
        history_points = np.hstack([df[['x', 'y']].values, delta_t[:, np.newaxis]])
        
        return target_points, history_points
        
    except Exception as e:
        logging.error(f"处理统计文件 {file_path} 时出错: {e}")
        return np.array([]), np.array([])
