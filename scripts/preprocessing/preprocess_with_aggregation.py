#!/usr/bin/env python3
"""
使用10秒时间聚合的数据预处理脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import logging
import pickle
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import numpy as np

from src.utils.config_loader import load_config
from .sample_generator_with_aggregation import (
    generate_samples_for_file_with_aggregation,
    _process_single_file_for_stats
)
from .environment_roi_extractor import get_environment_paths
from .lmdb_writer import write_samples_to_lmdb

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('preprocessing_with_aggregation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def compute_normalization_stats_with_aggregation(trajectory_files, config):
    """计算归一化统计数据（考虑聚合）"""
    logger.info("计算归一化统计数据...")
    
    all_target_points = []
    all_history_points = []
    
    # 使用多进程处理文件
    with ProcessPoolExecutor(max_workers=8) as executor:
        future_to_file = {
            executor.submit(_process_single_file_for_stats, file_path): file_path
            for file_path in trajectory_files
        }
        
        for future in tqdm(as_completed(future_to_file), total=len(trajectory_files), desc="计算统计"):
            file_path = future_to_file[future]
            try:
                target_points, history_points = future.result()
                if len(target_points) > 0:
                    all_target_points.append(target_points)
                if len(history_points) > 0:
                    all_history_points.append(history_points)
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {e}")
    
    # 合并所有数据
    if all_target_points:
        all_target_points = np.vstack(all_target_points)
    else:
        raise ValueError("没有有效的目标轨迹数据")
    
    if all_history_points:
        all_history_points = np.vstack(all_history_points)
    else:
        raise ValueError("没有有效的历史轨迹数据")
    
    # 计算统计数据
    stats = {
        'target_mean': {
            'x': float(np.mean(all_target_points[:, 0])),
            'y': float(np.mean(all_target_points[:, 1]))
        },
        'target_std': {
            'x': float(np.std(all_target_points[:, 0])),
            'y': float(np.std(all_target_points[:, 1]))
        },
        'history_mean': {
            'x': float(np.mean(all_history_points[:, 0])),
            'y': float(np.mean(all_history_points[:, 1])),
            'delta_t': float(np.mean(all_history_points[:, 2]))
        },
        'history_std': {
            'x': float(np.std(all_history_points[:, 0])),
            'y': float(np.std(all_history_points[:, 1])),
            'delta_t': float(np.std(all_history_points[:, 2]))
        }
    }
    
    # 确保标准差不为零
    for key in ['target_std', 'history_std']:
        for subkey in stats[key]:
            if stats[key][subkey] < 1e-6:
                stats[key][subkey] = 1.0
                logger.warning(f"标准差过小，设置为1.0: {key}.{subkey}")
    
    logger.info("归一化统计数据计算完成")
    logger.info(f"目标数据: 均值=({stats['target_mean']['x']:.2f}, {stats['target_mean']['y']:.2f}), "
               f"标准差=({stats['target_std']['x']:.2f}, {stats['target_std']['y']:.2f})")
    logger.info(f"历史数据: 均值=({stats['history_mean']['x']:.2f}, {stats['history_mean']['y']:.2f}), "
               f"标准差=({stats['history_std']['x']:.2f}, {stats['history_std']['y']:.2f})")
    logger.info(f"时间间隔: 均值={stats['history_mean']['delta_t']:.2f}s, "
               f"标准差={stats['history_std']['delta_t']:.2f}s")
    
    return stats

def process_files_with_aggregation(trajectory_files, split_name, config, normalization_stats, env_paths):
    """处理文件并生成聚合样本"""
    logger.info(f"处理 {split_name} 数据集...")
    
    all_samples = []
    
    # 使用多进程处理文件
    with ProcessPoolExecutor(max_workers=8) as executor:
        future_to_file = {
            executor.submit(
                generate_samples_for_file_with_aggregation,
                file_path, config, normalization_stats, env_paths
            ): file_path
            for file_path in trajectory_files
        }
        
        for future in tqdm(as_completed(future_to_file), total=len(trajectory_files), desc=f"处理{split_name}"):
            file_path = future_to_file[future]
            try:
                samples = future.result()
                all_samples.extend(samples)
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {e}")
    
    logger.info(f"{split_name} 数据集处理完成，共生成 {len(all_samples)} 个样本")
    return all_samples

def main():
    """主函数"""
    logger.info("=== 开始使用10秒聚合的数据预处理 ===")
    
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 检查聚合配置
    agg_interval_s = config.data_preprocessing.agg_interval_s
    logger.info(f"聚合间隔: {agg_interval_s}秒")
    
    # 计算聚合后的数据维度
    freq_hz = config.data_preprocessing.trajectory.freq_hz
    agg_freq = freq_hz / agg_interval_s
    
    obs_horizon_s = config.data_preprocessing.observation_horizon_s
    pred_horizon_s = config.data_preprocessing.prediction_horizon_s
    
    obs_points_agg = int(obs_horizon_s * agg_freq)  # 300s * 0.1Hz = 30个点
    pred_points_agg = int(pred_horizon_s * agg_freq)  # 1200s * 0.1Hz = 120个点
    
    logger.info(f"聚合后维度: 观测窗口 {obs_points_agg} 个点, 预测窗口 {pred_points_agg} 个点")
    
    # 获取轨迹文件
    trajectory_dir = Path(config.data_preprocessing.input_path)
    if not trajectory_dir.exists():
        raise FileNotFoundError(f"轨迹数据目录不存在: {trajectory_dir}")
    
    trajectory_files = list(trajectory_dir.glob("*.csv"))
    if not trajectory_files:
        raise FileNotFoundError(f"在 {trajectory_dir} 中没有找到CSV文件")
    
    logger.info(f"找到 {len(trajectory_files)} 个轨迹文件")
    
    # 数据集划分
    np.random.seed(42)
    np.random.shuffle(trajectory_files)
    
    train_ratio = config.data_preprocessing.train_ratio
    val_ratio = config.data_preprocessing.val_ratio
    
    n_train = int(len(trajectory_files) * train_ratio)
    n_val = int(len(trajectory_files) * val_ratio)
    
    train_files = trajectory_files[:n_train]
    val_files = trajectory_files[n_train:n_train + n_val]
    test_files = trajectory_files[n_train + n_val:]
    
    logger.info(f"数据集划分: 训练集 {len(train_files)}, 验证集 {len(val_files)}, 测试集 {len(test_files)}")
    
    # 计算归一化统计数据
    normalization_stats = compute_normalization_stats_with_aggregation(train_files, config)
    
    # 获取环境数据路径
    env_paths = get_environment_paths(config)
    
    # 创建输出目录
    output_dir = Path(config.data_preprocessing.output_path + "_aggregated")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存归一化统计数据
    stats_path = output_dir / "normalization_stats.pkl"
    with open(stats_path, 'wb') as f:
        pickle.dump(normalization_stats, f)
    logger.info(f"归一化统计数据已保存到: {stats_path}")
    
    # 处理各个数据集
    for split_name, files in [("train", train_files), ("val", val_files), ("test", test_files)]:
        if not files:
            logger.warning(f"跳过空的 {split_name} 数据集")
            continue
        
        # 生成样本
        samples = process_files_with_aggregation(files, split_name, config, normalization_stats, env_paths)
        
        if not samples:
            logger.warning(f"{split_name} 数据集没有生成任何样本")
            continue
        
        # 写入LMDB
        lmdb_path = output_dir / split_name
        write_samples_to_lmdb(samples, lmdb_path, config)
        logger.info(f"{split_name} 数据集已写入LMDB: {lmdb_path}")
        
        # 打印样本信息
        sample = samples[0]
        logger.info(f"{split_name} 样本信息:")
        logger.info(f"  历史特征形状: {sample['history_features'].shape}")
        logger.info(f"  历史掩码形状: {sample['history_mask'].shape}")
        logger.info(f"  预测轨迹形状: {sample['ground_truth_trajectory'].shape}")
        logger.info(f"  环境ROI形状: {sample['environment_roi'].shape}")
        if 'aggregation_info' in sample:
            agg_info = sample['aggregation_info']
            logger.info(f"  聚合信息: {agg_info['original_total_len']} -> {agg_info['aggregated_total_len']} 个点")
    
    logger.info("=== 聚合数据预处理完成 ===")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"聚合间隔: {agg_interval_s}秒")
    logger.info(f"观测窗口: {obs_points_agg}个点 ({obs_horizon_s}秒)")
    logger.info(f"预测窗口: {pred_points_agg}个点 ({pred_horizon_s}秒)")

if __name__ == "__main__":
    main()
