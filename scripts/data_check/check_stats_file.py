import pickle

def inspect_pickle_file_recursive(data, indent=0):
    """
    递归地打印出字典的内容。
    """
    for key, value in data.items():
        print("  " * indent + f"- 键: '{key}', 值的类型: {type(value)}")
        if isinstance(value, dict):
            inspect_pickle_file_recursive(value, indent + 1)

def inspect_pickle_file(file_path):
    """
    加载并检查 pickle 文件的内容。
    """
    try:
        with open(file_path, 'rb') as f:
            data = pickle.load(f)

        print(f"文件 '{file_path}' 加载成功。")
        print("="*30)
        
        if isinstance(data, dict):
            print("文件内容是一个字典。以下是其内容：")
            inspect_pickle_file_recursive(data)
        else:
            print(f"文件内容不是一个字典，其类型是: {type(data)}")

        print("="*30)

    except FileNotFoundError:
        print(f"错误: 文件未找到 at '{file_path}'")
    except Exception as e:
        print(f"检查过程中出现错误: {e}")

if __name__ == "__main__":
    stats_file_path = 'data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/normalization_stats.pkl' # 修正路径
    inspect_pickle_file(stats_file_path) 