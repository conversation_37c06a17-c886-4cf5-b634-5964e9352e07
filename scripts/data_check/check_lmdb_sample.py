import lmdb
import pickle
import argparse
import sys
from pathlib import Path

# 确保src目录在python path中
project_root = Path(__file__).resolve().parents[2]
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.utils.config_loader import load_config # 导入配置加载器

def inspect_lmdb_sample(lmdb_path):
    """
    加载并检查LMDB数据库的第一个样本。
    """
    try:
        env = lmdb.open(lmdb_path, readonly=True, lock=False, readahead=False, meminit=False)
        with env.begin(write=False) as txn:
            cursor = txn.cursor()
            if cursor.first():
                key = cursor.key()
                value = cursor.value()
                
                print(f"成功从 '{lmdb_path}' 加载第一个样本。")
                print("="*30)
                
                data_sample = pickle.loads(value)
                
                if isinstance(data_sample, dict):
                    print("样本内容是一个字典。以下是键和对应值的类型：")
                    for k, v in data_sample.items():
                        print(f"  - 键: '{k}', 值的类型: {type(v)}")
                else:
                    print(f"样本内容不是一个字典，其类型是: {type(data_sample)}")
            else:
                print(f"LMDB数据库 '{lmdb_path}' 为空。")
        print("="*30)
    except lmdb.Error as e:
        print(f"读取LMDB数据库时出错: {e}")
    except Exception as e:
        print(f"检查过程中出现错误: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="加载并检查LMDB数据库的第一个样本。")
    parser.add_argument('--lmdb_dir', type=str, 
                        help='要检查的LMDB数据库的路径，例如 data/processed_lmdb/train。如果未提供，将从配置文件加载。')
    parser.add_argument('--config', type=str, default='configs/main_config.yaml', 
                        help='主配置文件路径。')
    parser.add_argument('--data_config', type=str, default='configs/data_preprocessing.yaml', 
                        help='数据预处理配置文件路径。')

    args = parser.parse_args()

    if args.lmdb_dir:
        target_lmdb_path = args.lmdb_dir
    else:
        # 从配置文件加载路径
        config = load_config(args.config, args.data_config)
        target_lmdb_path = config.data_preprocessing.output_path + '/train'
        print(f"从配置文件加载LMDB路径: {target_lmdb_path}")

    inspect_lmdb_sample(target_lmdb_path) 