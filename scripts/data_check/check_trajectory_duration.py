
import pandas as pd
from pathlib import Path
import argparse
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

def check_duration(file_path: Path, required_seconds: int):
    """
    检查单个CSV文件的轨迹时长。

    Args:
        file_path (Path): CSV文件的路径。
        required_seconds (int): 要求的最小秒数。
    """
    try:
        # 1. 读取CSV文件
        df = pd.read_csv(file_path)
        
        # 2. 检查数据有效性
        if df.empty:
            logging.warning(f"文件为空: {file_path.name}")
            return

        if 'timestamp_ms' not in df.columns:
            logging.error(f"文件缺少 'timestamp_ms' 列: {file_path.name}")
            return
            
        # 3. 计算时长
        # 确保时间戳是数字类型并丢弃无效行
        df['timestamp_ms'] = pd.to_numeric(df['timestamp_ms'], errors='coerce')
        df.dropna(subset=['timestamp_ms'], inplace=True)

        if len(df) < 2:
            logging.warning(f"有效数据点不足2个: {file_path.name}")
            return

        # 获取第一个和最后一个时间戳 (毫秒)
        start_time_ms = df['timestamp_ms'].iloc[0]
        end_time_ms = df['timestamp_ms'].iloc[-1]
        
        duration_ms = end_time_ms - start_time_ms
        duration_s = duration_ms / 1000
        duration_td = pd.to_timedelta(duration_s, unit='s')
        
        # 4. 打印诊断信息
        status = "✅ 满足" if duration_s >= required_seconds else "❌ 不足"
        logging.info(f"--- 文件: {file_path.name} ---")
        logging.info(f"  原始行数: {len(df)}")
        logging.info(f"  开始时间戳 (ms): {start_time_ms}")
        logging.info(f"  结束时间戳 (ms): {end_time_ms}")
        logging.info(f"  计算总时长: {duration_td} (约 {duration_s / 60:.2f} 分钟)")
        logging.info(f"  对比 {required_seconds / 60:.0f} 分钟的要求: {status}")
        print("-" * 50)

    except Exception as e:
        logging.error(f"处理文件时发生错误 {file_path.name}: {e}")

def main():
    parser = argparse.ArgumentParser(description="检查轨迹CSV文件的总时长。")
    parser.add_argument('--trajectory_dir', type=str, required=True, help='轨迹CSV文件所在的目录。')
    parser.add_argument('--num_files', type=int, default=20, help='要检查的文件数量（0表示全部）。')
    parser.add_argument('--required_minutes', type=int, default=45, help='样本所需的最小分钟数。')
    args = parser.parse_args()

    trajectory_path = Path(args.trajectory_dir)
    if not trajectory_path.is_dir():
        logging.error(f"错误: 目录不存在: {trajectory_path}")
        return

    files_to_check = sorted(list(trajectory_path.glob('*.csv')))
    if args.num_files > 0:
        files_to_check = files_to_check[:args.num_files]

    logging.info(f"开始检查目录 '{trajectory_path}' 中的 {len(files_to_check)} 个文件...")
    logging.info(f"要求的最小轨迹时长为: {args.required_minutes} 分钟 ({args.required_minutes * 60} 秒)")
    
    for file in files_to_check:
        check_duration(file, args.required_minutes * 60)

if __name__ == "__main__":
    main() 