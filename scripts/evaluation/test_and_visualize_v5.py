#!/usr/bin/env python3
"""
V5模型测试与可视化
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import pickle
from torch.utils.data import DataLoader

from src.data.datasets import LMDBDataset
from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.utils.config_loader import load_config
from src.evaluation.metrics import calculate_ade, calculate_fde

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_trained_model(config, device):
    """加载训练好的模型"""
    print("=== 加载训练好的V5模型 ===")
    
    model = TrajectoryPredictorV5(config).to(device)
    
    # 查找V5模型检查点 - 优先使用最佳模型
    best_checkpoint = Path("models/trajectory_predictor_v5_best.pth")
    if best_checkpoint.exists():
        latest_checkpoint = best_checkpoint
        print(f"使用最佳V5模型: {latest_checkpoint}")
    else:
        # 备选：使用最新的epoch模型
        epoch_checkpoint = Path("models/trajectory_predictor_v5_epoch_40.pth")
        if epoch_checkpoint.exists():
            latest_checkpoint = epoch_checkpoint
            print(f"使用V5模型epoch 40: {latest_checkpoint}")
        else:
            print("❌ 未找到V5模型检查点")
            return None
    print(f"加载检查点: {latest_checkpoint}")
    
    try:
        checkpoint = torch.load(latest_checkpoint, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 成功加载模型 (轮次: {checkpoint.get('epoch', 'unknown')})")
        train_loss = checkpoint.get('train_loss', 'unknown')
        val_loss = checkpoint.get('val_loss', 'unknown')
        if isinstance(train_loss, (int, float)):
            print(f"   训练损失: {train_loss:.4f}")
        else:
            print(f"   训练损失: {train_loss}")
        if isinstance(val_loss, (int, float)):
            print(f"   验证损失: {val_loss:.4f}")
        else:
            print(f"   验证损失: {val_loss}")
        return model
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return None

def evaluate_model(model, val_loader, device, config):
    """评估模型性能"""
    print("\n=== 评估模型性能 ===")
    
    model.eval()
    total_loss = 0
    all_ade = []
    all_fde = []
    all_predictions = []
    all_ground_truths = []
    all_histories = []
    
    criterion = nn.MSELoss()
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_loader):
            # 准备数据
            history_features = batch['history_features'].to(device)
            history_mask = batch['history_mask'].to(device)
            ground_truth_trajectory = batch['ground_truth_trajectory'].to(device)
            ground_truth_destination = batch['ground_truth_destination'].to(device)
            environment_roi = batch['environment_roi'].to(device)
            
            # 模型预测
            predicted_trajectory = model(
                history_features=history_features,
                history_mask=history_mask,
                ground_truth_destination=ground_truth_destination,
                environment_roi=environment_roi
            )
            
            # 计算损失
            loss = criterion(predicted_trajectory, ground_truth_trajectory)
            total_loss += loss.item()
            
            # 计算指标
            pred_np = predicted_trajectory.cpu().numpy()
            gt_np = ground_truth_trajectory.cpu().numpy()
            
            for i in range(pred_np.shape[0]):
                ade = calculate_ade(pred_np[i], gt_np[i])
                fde = calculate_fde(pred_np[i], gt_np[i])
                all_ade.append(ade)
                all_fde.append(fde)
            
            # 保存用于可视化
            all_predictions.append(pred_np)
            all_ground_truths.append(gt_np)
            all_histories.append(history_features.cpu().numpy())
            
            if batch_idx >= 10:  # 只评估前几个批次
                break
    
    # 计算平均指标
    avg_loss = total_loss / (batch_idx + 1)
    avg_ade = np.mean(all_ade)
    avg_fde = np.mean(all_fde)
    
    print(f"验证结果:")
    print(f"  平均损失: {avg_loss:.4f}")
    print(f"  平均ADE: {avg_ade:.4f}")
    print(f"  平均FDE: {avg_fde:.4f}")
    print(f"  ADE标准差: {np.std(all_ade):.4f}")
    print(f"  FDE标准差: {np.std(all_fde):.4f}")
    
    return {
        'avg_loss': avg_loss,
        'avg_ade': avg_ade,
        'avg_fde': avg_fde,
        'all_ade': all_ade,
        'all_fde': all_fde,
        'predictions': all_predictions,
        'ground_truths': all_ground_truths,
        'histories': all_histories
    }

def visualize_predictions(results, config, num_samples=6):
    """可视化预测结果"""
    print(f"\n=== 可视化预测结果 ===")
    
    predictions = results['predictions']
    ground_truths = results['ground_truths']
    histories = results['histories']
    
    # 加载归一化统计数据用于反归一化
    stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    if os.path.exists(stats_path):
        with open(stats_path, 'rb') as f:
            norm_stats = pickle.load(f)
        # 根据实际的归一化统计数据结构调整
        if 'target' in norm_stats and 'x_mean' in norm_stats['target']:
            x_mean, x_std = norm_stats['target']['x_mean'], norm_stats['target']['x_std']
            y_mean, y_std = norm_stats['target']['y_mean'], norm_stats['target']['y_std']
        elif 'target_mean' in norm_stats and 'target_std' in norm_stats:
            # 使用新的归一化统计格式
            target_mean = norm_stats['target_mean']
            target_std = norm_stats['target_std']
            x_mean, y_mean = target_mean['x'], target_mean['y']
            x_std, y_std = target_std['x'], target_std['y']
        else:
            print("警告：无法找到归一化统计，使用默认值")
            x_mean, x_std, y_mean, y_std = 0, 1, 0, 1
    else:
        print("⚠️ 未找到归一化统计数据，使用原始数值")
        x_mean, x_std = 0, 1
        y_mean, y_std = 0, 1
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    sample_count = 0
    for batch_pred, batch_gt, batch_hist in zip(predictions, ground_truths, histories):
        for i in range(batch_pred.shape[0]):
            if sample_count >= num_samples:
                break
            
            ax = axes[sample_count]
            
            # 反归一化数据
            pred_traj = batch_pred[i] * np.array([x_std, y_std]) + np.array([x_mean, y_mean])
            gt_traj = batch_gt[i] * np.array([x_std, y_std]) + np.array([x_mean, y_mean])
            
            # 提取历史轨迹 (只取x, y坐标)
            hist_traj = batch_hist[i, :, :2] * np.array([x_std, y_std]) + np.array([x_mean, y_mean])
            
            # 找到有效的历史点
            valid_hist = hist_traj[np.any(hist_traj != np.array([x_mean, y_mean]), axis=1)]
            
            # 绘制轨迹
            if len(valid_hist) > 0:
                ax.plot(valid_hist[:, 0], valid_hist[:, 1], 'b-', linewidth=2, 
                       label='历史轨迹', alpha=0.7)
                ax.scatter(valid_hist[-1, 0], valid_hist[-1, 1], c='blue', s=100, 
                          marker='o', label='历史终点', zorder=5)
            
            ax.plot(gt_traj[:, 0], gt_traj[:, 1], 'g-', linewidth=2, 
                   label='真实轨迹', alpha=0.8)
            ax.plot(pred_traj[:, 0], pred_traj[:, 1], 'r--', linewidth=2, 
                   label='预测轨迹', alpha=0.8)
            
            # 标记起点和终点
            ax.scatter(gt_traj[0, 0], gt_traj[0, 1], c='green', s=100, 
                      marker='s', label='预测起点', zorder=5)
            ax.scatter(gt_traj[-1, 0], gt_traj[-1, 1], c='green', s=100, 
                      marker='*', label='真实终点', zorder=5)
            ax.scatter(pred_traj[-1, 0], pred_traj[-1, 1], c='red', s=100, 
                      marker='*', label='预测终点', zorder=5)
            
            # 计算误差
            ade = calculate_ade(batch_pred[i], batch_gt[i])
            fde = calculate_fde(batch_pred[i], batch_gt[i])
            
            ax.set_title(f'样本 {sample_count + 1}\nADE: {ade:.3f}, FDE: {fde:.3f}')
            ax.set_xlabel('X坐标 (米)')
            ax.set_ylabel('Y坐标 (米)')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            ax.axis('equal')
            
            sample_count += 1
        
        if sample_count >= num_samples:
            break
    
    plt.tight_layout()
    plt.savefig('v5_model_predictions.png', dpi=300, bbox_inches='tight')
    print("✅ 预测可视化已保存: v5_model_predictions.png")
    plt.show()

def analyze_error_distribution(results):
    """分析误差分布"""
    print(f"\n=== 分析误差分布 ===")
    
    all_ade = results['all_ade']
    all_fde = results['all_fde']
    
    # 创建误差分布图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # ADE分布
    ax1.hist(all_ade, bins=20, alpha=0.7, color='blue', edgecolor='black')
    ax1.axvline(np.mean(all_ade), color='red', linestyle='--', 
               label=f'均值: {np.mean(all_ade):.3f}')
    ax1.axvline(np.median(all_ade), color='orange', linestyle='--', 
               label=f'中位数: {np.median(all_ade):.3f}')
    ax1.set_xlabel('ADE (平均位移误差)')
    ax1.set_ylabel('频次')
    ax1.set_title('ADE分布')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # FDE分布
    ax2.hist(all_fde, bins=20, alpha=0.7, color='green', edgecolor='black')
    ax2.axvline(np.mean(all_fde), color='red', linestyle='--', 
               label=f'均值: {np.mean(all_fde):.3f}')
    ax2.axvline(np.median(all_fde), color='orange', linestyle='--', 
               label=f'中位数: {np.median(all_fde):.3f}')
    ax2.set_xlabel('FDE (最终位移误差)')
    ax2.set_ylabel('频次')
    ax2.set_title('FDE分布')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('v5_error_distribution.png', dpi=300, bbox_inches='tight')
    print("✅ 误差分布图已保存: v5_error_distribution.png")
    plt.show()
    
    # 打印统计信息
    print(f"ADE统计:")
    print(f"  最小值: {np.min(all_ade):.4f}")
    print(f"  最大值: {np.max(all_ade):.4f}")
    print(f"  25%分位数: {np.percentile(all_ade, 25):.4f}")
    print(f"  75%分位数: {np.percentile(all_ade, 75):.4f}")
    
    print(f"FDE统计:")
    print(f"  最小值: {np.min(all_fde):.4f}")
    print(f"  最大值: {np.max(all_fde):.4f}")
    print(f"  25%分位数: {np.percentile(all_fde, 25):.4f}")
    print(f"  75%分位数: {np.percentile(all_fde, 75):.4f}")

def main():
    """主函数"""
    print("🚀 开始V5模型测试与可视化")
    
    # 配置
    config = load_config('configs/main_config.yaml')
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型
    model = load_trained_model(config, device)
    if model is None:
        print("❌ 无法加载模型，退出")
        return
    
    # 创建验证数据集
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    val_loader = DataLoader(
        val_dataset,
        batch_size=8,
        shuffle=False,
        num_workers=0,
        collate_fn=LMDBDataset.collate_fn
    )
    
    # 评估模型
    results = evaluate_model(model, val_loader, device, config)
    
    # 可视化预测结果
    visualize_predictions(results, config)
    
    # 分析误差分布
    analyze_error_distribution(results)
    
    print("\n🎉 V5模型测试与可视化完成！")

if __name__ == "__main__":
    main()
