#!/usr/bin/env python3
"""
V3 (自回归) vs V4 (一次性生成) 模型对比测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import pickle
import logging
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.v3_masked_autoregressive.model import MaskedAutoregressiveModel
from src.models.v4_non_autoregressive.model import NonAutoregressiveModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model(model_type, checkpoint_path, config, normalization_stats, device):
    """加载模型"""
    try:
        if model_type == 'v3':
            model = MaskedAutoregressiveModel(config.model, normalization_stats)
        elif model_type == 'v4':
            model = NonAutoregressiveModel(config.model, normalization_stats)
        else:
            raise ValueError(f"未知模型类型: {model_type}")
        
        checkpoint = torch.load(checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device)
        model.eval()
        
        epoch = checkpoint.get('epoch', 'unknown')
        val_loss = checkpoint.get('val_loss', 'unknown')
        
        logger.info(f"{model_type.upper()}模型加载成功 - Epoch: {epoch}, Val Loss: {val_loss}")
        return model
        
    except Exception as e:
        logger.error(f"{model_type.upper()}模型加载失败: {e}")
        return None

def denormalize_trajectory(normalized_traj, normalization_stats, use_target_stats=True):
    """反归一化轨迹"""
    if use_target_stats and 'target_mean' in normalization_stats:
        mean_x = normalization_stats['target_mean']['x']
        std_x = normalization_stats['target_std']['x']
        mean_y = normalization_stats['target_mean']['y']
        std_y = normalization_stats['target_std']['y']
    else:
        mean_x = normalization_stats['history_mean']['x']
        std_x = normalization_stats['history_std']['x']
        mean_y = normalization_stats['history_mean']['y']
        std_y = normalization_stats['history_std']['y']
    
    denorm_traj = normalized_traj.clone()
    denorm_traj[..., 0] = denorm_traj[..., 0] * std_x + mean_x
    denorm_traj[..., 1] = denorm_traj[..., 1] * std_y + mean_y
    
    return denorm_traj

def evaluate_model(model, model_name, data_loader, normalization_stats, device, num_samples=20):
    """评估单个模型"""
    logger.info(f"评估{model_name}模型...")
    
    results = []
    sample_count = 0
    
    with torch.no_grad():
        for batch in data_loader:
            if sample_count >= num_samples or batch is None:
                break
            
            # 数据移到设备
            history_features = batch['history_features'].to(device)
            ground_truth_trajectory = batch['ground_truth_trajectory'].to(device)
            history_mask = batch['history_mask'].to(device)
            environment_roi = batch['environment_roi'].to(device)
            
            try:
                # 模型预测
                predicted_trajectory, predicted_destination = model(
                    history_trajectory=history_features,
                    environment_roi=environment_roi,
                    history_mask=history_mask,
                    ground_truth_trajectory=None  # 验证时不传入
                )
                
                # 转换为CPU并去掉batch维度
                history_cpu = history_features[0].cpu()
                pred_cpu = predicted_trajectory[0].cpu()
                gt_cpu = ground_truth_trajectory[0].cpu()
                mask_cpu = history_mask[0].cpu()
                pred_dest_cpu = predicted_destination[0].cpu()
                
                # 获取有效历史数据
                valid_indices = torch.where(mask_cpu)[0]
                valid_history = history_cpu[valid_indices]
                
                if len(valid_history) == 0:
                    continue
                
                # 反归一化
                history_physical = denormalize_trajectory(valid_history, normalization_stats, use_target_stats=False)
                pred_physical = denormalize_trajectory(pred_cpu, normalization_stats, use_target_stats=True)
                gt_physical = denormalize_trajectory(gt_cpu, normalization_stats, use_target_stats=True)
                pred_dest_physical = denormalize_trajectory(pred_dest_cpu.unsqueeze(0), normalization_stats, use_target_stats=True).squeeze(0)
                
                # 计算误差
                trajectory_error = torch.norm(pred_physical - gt_physical, dim=1).mean().item()
                endpoint_error = torch.norm(pred_physical[-1] - gt_physical[-1]).item()
                destination_error = torch.norm(pred_dest_physical - gt_physical[-1]).item()
                
                # 计算轨迹长度
                if len(pred_physical) > 1:
                    pred_diffs = torch.diff(pred_physical, dim=0)
                    pred_distance = torch.norm(pred_diffs, dim=1).sum().item()
                else:
                    pred_distance = 0
                
                if len(gt_physical) > 1:
                    gt_diffs = torch.diff(gt_physical, dim=0)
                    gt_distance = torch.norm(gt_diffs, dim=1).sum().item()
                else:
                    gt_distance = 0
                
                # 存储结果
                result = {
                    'sample_id': sample_count + 1,
                    'model_name': model_name,
                    'history_physical': history_physical,
                    'pred_physical': pred_physical,
                    'gt_physical': gt_physical,
                    'pred_dest_physical': pred_dest_physical,
                    'trajectory_error': trajectory_error,
                    'endpoint_error': endpoint_error,
                    'destination_error': destination_error,
                    'pred_distance': pred_distance,
                    'gt_distance': gt_distance
                }
                results.append(result)
                
                sample_count += 1
                
            except Exception as e:
                logger.error(f"{model_name}模型预测样本{sample_count + 1}失败: {e}")
                continue
    
    logger.info(f"{model_name}模型评估完成，成功样本数: {len(results)}")
    return results

def compare_results(v3_results, v4_results):
    """对比两个模型的结果"""
    logger.info("=== 模型对比分析 ===")
    
    if not v3_results or not v4_results:
        logger.error("缺少模型结果，无法对比")
        return
    
    # 提取误差数据
    v3_traj_errors = [r['trajectory_error'] for r in v3_results]
    v3_endpoint_errors = [r['endpoint_error'] for r in v3_results]
    v3_dest_errors = [r['destination_error'] for r in v3_results]
    
    v4_traj_errors = [r['trajectory_error'] for r in v4_results]
    v4_endpoint_errors = [r['endpoint_error'] for r in v4_results]
    v4_dest_errors = [r['destination_error'] for r in v4_results]
    
    # 统计分析
    print(f"\n{'='*60}")
    print(f"{'指标':<20} {'V3 (自回归)':<20} {'V4 (一次性)':<20} {'改善':<10}")
    print(f"{'='*60}")
    
    # 轨迹平均误差
    v3_traj_mean = np.mean(v3_traj_errors)
    v4_traj_mean = np.mean(v4_traj_errors)
    traj_improvement = (v3_traj_mean - v4_traj_mean) / v3_traj_mean * 100
    print(f"{'轨迹平均误差(m)':<20} {v3_traj_mean:<20.0f} {v4_traj_mean:<20.0f} {traj_improvement:>+7.1f}%")
    
    # 轨迹终点误差
    v3_endpoint_mean = np.mean(v3_endpoint_errors)
    v4_endpoint_mean = np.mean(v4_endpoint_errors)
    endpoint_improvement = (v3_endpoint_mean - v4_endpoint_mean) / v3_endpoint_mean * 100
    print(f"{'轨迹终点误差(m)':<20} {v3_endpoint_mean:<20.0f} {v4_endpoint_mean:<20.0f} {endpoint_improvement:>+7.1f}%")
    
    # 目标点误差
    v3_dest_mean = np.mean(v3_dest_errors)
    v4_dest_mean = np.mean(v4_dest_errors)
    dest_improvement = (v3_dest_mean - v4_dest_mean) / v3_dest_mean * 100
    print(f"{'目标点误差(m)':<20} {v3_dest_mean:<20.0f} {v4_dest_mean:<20.0f} {dest_improvement:>+7.1f}%")
    
    print(f"{'='*60}")
    
    # 标准差对比
    print(f"\n{'标准差对比':<20} {'V3 (自回归)':<20} {'V4 (一次性)':<20}")
    print(f"{'-'*60}")
    print(f"{'轨迹误差标准差':<20} {np.std(v3_traj_errors):<20.0f} {np.std(v4_traj_errors):<20.0f}")
    print(f"{'终点误差标准差':<20} {np.std(v3_endpoint_errors):<20.0f} {np.std(v4_endpoint_errors):<20.0f}")
    print(f"{'目标误差标准差':<20} {np.std(v3_dest_errors):<20.0f} {np.std(v4_dest_errors):<20.0f}")
    
    # 总结
    print(f"\n=== 对比总结 ===")
    if traj_improvement > 0:
        print(f"✅ V4模型轨迹预测平均改善 {traj_improvement:.1f}%")
    else:
        print(f"❌ V4模型轨迹预测平均退化 {abs(traj_improvement):.1f}%")
    
    if endpoint_improvement > 0:
        print(f"✅ V4模型终点预测平均改善 {endpoint_improvement:.1f}%")
    else:
        print(f"❌ V4模型终点预测平均退化 {abs(endpoint_improvement):.1f}%")
    
    if dest_improvement > 0:
        print(f"✅ V4模型目标预测平均改善 {dest_improvement:.1f}%")
    else:
        print(f"❌ V4模型目标预测平均退化 {abs(dest_improvement):.1f}%")

def create_comparison_visualization(v3_results, v4_results, num_samples=6):
    """创建对比可视化"""
    logger.info("创建对比可视化...")
    
    # 选择前几个样本进行可视化
    samples_to_show = min(num_samples, len(v3_results), len(v4_results))
    
    fig, axes = plt.subplots(2, samples_to_show, figsize=(4*samples_to_show, 8))
    if samples_to_show == 1:
        axes = axes.reshape(2, 1)
    
    for i in range(samples_to_show):
        v3_result = v3_results[i]
        v4_result = v4_results[i]
        
        # V3结果 (上排)
        ax_v3 = axes[0, i]
        history = v3_result['history_physical']
        pred_v3 = v3_result['pred_physical']
        gt = v3_result['gt_physical']
        
        ax_v3.plot(history[:, 0].numpy(), history[:, 1].numpy(), 'b-o', markersize=1, label='历史轨迹', alpha=0.7)
        ax_v3.plot(gt[:, 0].numpy(), gt[:, 1].numpy(), 'g-', linewidth=2, label='真实轨迹', alpha=0.8)
        ax_v3.plot(pred_v3[:, 0].numpy(), pred_v3[:, 1].numpy(), 'r--', linewidth=2, label='V3预测', alpha=0.8)
        
        ax_v3.set_title(f'V3 (自回归) - 样本{i+1}\n误差: {v3_result["trajectory_error"]:.0f}m', fontsize=10)
        ax_v3.legend(fontsize=8)
        ax_v3.grid(True, alpha=0.3)
        ax_v3.set_aspect('equal', adjustable='box')
        
        # V4结果 (下排)
        ax_v4 = axes[1, i]
        pred_v4 = v4_result['pred_physical']
        
        ax_v4.plot(history[:, 0].numpy(), history[:, 1].numpy(), 'b-o', markersize=1, label='历史轨迹', alpha=0.7)
        ax_v4.plot(gt[:, 0].numpy(), gt[:, 1].numpy(), 'g-', linewidth=2, label='真实轨迹', alpha=0.8)
        ax_v4.plot(pred_v4[:, 0].numpy(), pred_v4[:, 1].numpy(), 'm--', linewidth=2, label='V4预测', alpha=0.8)
        
        ax_v4.set_title(f'V4 (一次性) - 样本{i+1}\n误差: {v4_result["trajectory_error"]:.0f}m', fontsize=10)
        ax_v4.legend(fontsize=8)
        ax_v4.grid(True, alpha=0.3)
        ax_v4.set_aspect('equal', adjustable='box')
        
        # 设置相同的坐标范围
        all_x = torch.cat([history[:, 0], gt[:, 0], pred_v3[:, 0], pred_v4[:, 0]])
        all_y = torch.cat([history[:, 1], gt[:, 1], pred_v3[:, 1], pred_v4[:, 1]])
        
        x_margin = (all_x.max() - all_x.min()) * 0.1
        y_margin = (all_y.max() - all_y.min()) * 0.1
        
        xlim = (all_x.min() - x_margin, all_x.max() + x_margin)
        ylim = (all_y.min() - y_margin, all_y.max() + y_margin)
        
        ax_v3.set_xlim(xlim)
        ax_v3.set_ylim(ylim)
        ax_v4.set_xlim(xlim)
        ax_v4.set_ylim(ylim)
    
    plt.tight_layout()
    plt.savefig('v3_vs_v4_comparison.svg', format='svg', dpi=150)
    logger.info("对比可视化已保存到: v3_vs_v4_comparison.svg")

def main():
    """主函数"""
    logger.info("=== V3 vs V4 模型对比测试 ===")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载配置和数据
    v3_config = load_config('configs/main_config.yaml', 'configs/models/v3_masked_autoregressive.yaml')
    v4_config = load_config('configs/main_config.yaml', 'configs/models/v4_non_autoregressive.yaml')
    
    # 加载归一化统计
    normalization_stats_path = f"{v3_config.data_preprocessing.output_path}/normalization_stats.pkl"
    with open(normalization_stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    
    # 创建验证数据集
    val_dataset = LMDBDataset(config=v3_config, lmdb_type='val', return_mask=True)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=True, collate_fn=LMDBDataset.collate_fn)
    
    logger.info(f"验证集样本数: {len(val_dataset)}")
    
    # 查找模型检查点
    v3_checkpoint_dir = Path("checkpoints/v3_masked_autoregressive")
    v4_checkpoint_dir = Path("checkpoints/v4_non_autoregressive")
    
    v3_checkpoints = list(v3_checkpoint_dir.glob("best_model_*.pth")) if v3_checkpoint_dir.exists() else []
    v4_checkpoints = list(v4_checkpoint_dir.glob("best_model_*.pth")) if v4_checkpoint_dir.exists() else []
    
    if not v3_checkpoints:
        logger.error("未找到V3模型检查点")
        return
    
    if not v4_checkpoints:
        logger.warning("未找到V4模型检查点，将只评估V3模型")
        # 这里可以先训练V4模型
        logger.info("请先训练V4模型: python scripts/training/08_train_v4_non_autoregressive.py")
        return
    
    # 选择最新的检查点
    v3_checkpoint = max(v3_checkpoints, key=lambda x: x.stat().st_mtime)
    v4_checkpoint = max(v4_checkpoints, key=lambda x: x.stat().st_mtime)
    
    logger.info(f"V3检查点: {v3_checkpoint}")
    logger.info(f"V4检查点: {v4_checkpoint}")
    
    # 加载模型
    v3_model = load_model('v3', v3_checkpoint, v3_config, normalization_stats, device)
    v4_model = load_model('v4', v4_checkpoint, v4_config, normalization_stats, device)
    
    if v3_model is None or v4_model is None:
        logger.error("模型加载失败")
        return
    
    # 评估模型
    v3_results = evaluate_model(v3_model, 'V3', val_loader, normalization_stats, device, num_samples=20)
    
    # 重新创建数据加载器 (因为已经遍历过了)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=True, collate_fn=LMDBDataset.collate_fn)
    v4_results = evaluate_model(v4_model, 'V4', val_loader, normalization_stats, device, num_samples=20)
    
    # 对比结果
    compare_results(v3_results, v4_results)
    
    # 创建可视化
    create_comparison_visualization(v3_results, v4_results, num_samples=6)
    
    logger.info("对比测试完成！")

if __name__ == "__main__":
    main()
