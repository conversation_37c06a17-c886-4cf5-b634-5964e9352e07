#!/usr/bin/env python3
"""
简单的V5模型测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader

from src.data.datasets import LMDBDataset
from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def main():
    """主函数"""
    print("🚀 开始简单V5模型测试")
    
    # 配置
    config = load_config('configs/main_config.yaml')
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    print("创建V5模型...")
    model = TrajectoryPredictorV5(config).to(device)
    
    # 加载检查点
    checkpoint_path = "models/trajectory_predictor_v5_best.pth"
    if not os.path.exists(checkpoint_path):
        checkpoint_path = "models/trajectory_predictor_v5_epoch_40.pth"
    
    if not os.path.exists(checkpoint_path):
        print("❌ 未找到V5模型检查点")
        return
    
    print(f"加载检查点: {checkpoint_path}")
    try:
        checkpoint = torch.load(checkpoint_path, map_location=device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 成功加载模型 (轮次: {checkpoint.get('epoch', 'unknown')})")
        else:
            model.load_state_dict(checkpoint)
            print("✅ 成功加载模型")
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return
    
    # 创建验证数据集
    print("创建验证数据集...")
    val_dataset = LMDBDataset(config=config, lmdb_type='val', return_mask=True)
    val_loader = DataLoader(
        val_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=0,
        collate_fn=LMDBDataset.collate_fn
    )
    print(f"验证集样本数: {len(val_dataset)}")
    
    # 测试模型
    print("测试模型...")
    model.eval()
    criterion = nn.MSELoss()
    
    total_loss = 0
    sample_count = 0
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_loader):
            if batch_idx >= 5:  # 只测试前5个批次
                break
                
            # 准备数据
            history_features = batch['history_features'].to(device)
            history_mask = batch['history_mask'].to(device)
            ground_truth_trajectory = batch['ground_truth_trajectory'].to(device)
            ground_truth_destination = batch['ground_truth_destination'].to(device)
            environment_roi = batch['environment_roi'].to(device)
            
            print(f"批次 {batch_idx}:")
            print(f"  history_features: {history_features.shape}")
            print(f"  ground_truth_trajectory: {ground_truth_trajectory.shape}")
            
            # 模型预测
            try:
                predicted_trajectory = model(
                    history_features=history_features,
                    history_mask=history_mask,
                    ground_truth_destination=ground_truth_destination,
                    environment_roi=environment_roi
                )
                
                print(f"  predicted_trajectory: {predicted_trajectory.shape}")
                
                # 计算损失
                loss = criterion(predicted_trajectory, ground_truth_trajectory)
                total_loss += loss.item()
                sample_count += 1
                
                print(f"  损失: {loss.item():.4f}")
                
                # 简单可视化第一个样本
                if batch_idx == 0:
                    pred_np = predicted_trajectory[0].cpu().numpy()
                    gt_np = ground_truth_trajectory[0].cpu().numpy()
                    
                    plt.figure(figsize=(10, 8))
                    plt.plot(gt_np[:, 0], gt_np[:, 1], 'g-', linewidth=2, label='真实轨迹')
                    plt.plot(pred_np[:, 0], pred_np[:, 1], 'r--', linewidth=2, label='预测轨迹')
                    plt.scatter(gt_np[0, 0], gt_np[0, 1], c='green', s=100, marker='s', label='起点')
                    plt.scatter(gt_np[-1, 0], gt_np[-1, 1], c='green', s=100, marker='*', label='真实终点')
                    plt.scatter(pred_np[-1, 0], pred_np[-1, 1], c='red', s=100, marker='*', label='预测终点')
                    
                    plt.title('V5模型预测结果示例')
                    plt.xlabel('X坐标')
                    plt.ylabel('Y坐标')
                    plt.legend()
                    plt.grid(True, alpha=0.3)
                    plt.axis('equal')
                    plt.savefig('v5_simple_test.png', dpi=300, bbox_inches='tight')
                    print("  ✅ 可视化已保存: v5_simple_test.png")
                    plt.show()
                
            except Exception as e:
                print(f"  ❌ 预测失败: {e}")
                import traceback
                traceback.print_exc()
                break
    
    if sample_count > 0:
        avg_loss = total_loss / sample_count
        print(f"\n📊 测试结果:")
        print(f"  平均损失: {avg_loss:.4f}")
        print(f"  测试批次数: {sample_count}")
        print("🎉 V5模型测试完成！")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
