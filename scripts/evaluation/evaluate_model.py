# -*- coding: utf-8 -*-
"""
创建时间: 2025/07/19
功能: 评估已训练好的轨迹预测模型的性能。本脚本通过加载模型权重和验证数据集，计算并报告预测轨迹与真实轨迹之间的平均位移误差（Average Displacement Error, ADE）和最终位移误差（Final Displacement Error, FDE）。
输入:
  - `main_config_path`: 字符串，指定主配置文件的YAML文件路径（例如：`configs/main_config.yaml`）。此文件包含了通用配置和指向模型特定配置的路径。
  - `model_path`: 字符串，已训练模型权重文件的路径（例如：`checkpoints/e2e_model_v1_20250719_082313/latest.pth`）。
  - LMDB验证数据集：通过配置文件中 `data.val_lmdb_path` 指定，包含了用于评估的轨迹样本和环境ROI。
  - 归一化统计文件：通过配置文件中 `data.stats_path` 指定，用于轨迹数据的反归一化。
输出:
  - 控制台输出评估进度条。
  - 最终评估报告，包括：总样本数、计算得到的平均位移误差 (ADE) 和最终位移误差 (FDE)，单位为米。
原理:
  - **配置解析:** 使用 `src.utils.config_loader` 库加载并合并 `main_config.yaml` 和模型特定的配置文件，获取评估所需的所有参数。
  - **设备管理:** 根据配置或系统可用性，自动选择使用CUDA (GPU) 或CPU进行计算。
  - **数据归一化与反归一化:** 加载预计算的归一化统计数据。模型输出的是归一化后的轨迹，在计算ADE和FDE前，需使用这些统计数据将预测轨迹和真实轨迹反归一化到原始坐标空间。
  - **模型加载与评估模式:** 实例化 `src.model.EndToEndModel`，加载指定路径的预训练模型权重，并将模型设置为评估模式 (`model.eval()`)，禁用Dropout和BatchNorm等训练特有层。
  - **数据加载器构建:** 创建 `src.data.datasets.TrajectoryDataset` 和 `torch.utils.data.DataLoader`，用于高效地批量加载验证数据集。DataLoader配置了批次大小、禁用随机打乱和多进程加载。
  - **误差指标计算:** 在 `torch.no_grad()` 上下文中进行推理，以节省内存并加速。对于每个批次的预测结果，将归一化轨迹反归一化后，使用NumPy计算ADE（预测轨迹与真实轨迹每个时间步的欧氏距离的平均值）和FDE（预测轨迹终点与真实轨迹终点之间的欧氏距离）。
处理方法:
  - 脚本通过 `if __name__ == "__main__":` 块提供默认的配置和模型路径，方便直接运行进行快速评估。
  - 使用 `tqdm` 库为数据加载和评估过程提供可视化的进度条。
  - 包含健壮的错误处理机制，能够捕获文件读取、模型加载、数据处理和硬件设备相关的异常。
  - 输出结果格式清晰，便于分析模型的性能表现。
"""
import sys
import os
import argparse
from pathlib import Path

# 调整sys.path，确保能导入项目根目录下的模块
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

import torch
import numpy as np
from torch.utils.data import DataLoader
from tqdm import tqdm
import yaml
# from omegaconf import OmegaConf # 暂时保留，如果后续config_loader完全替代则可移除

# 从src导入模型和数据集，而不是直接导入EndToEndModel
from src.data.datasets import LMDBDataset # 将 TrajectoryDataset 替换为 LMDBDataset
from src.utils.normalization import load_normalization_stats, denormalize_trajectory
from src.utils.config_loader import load_config, update_config # 导入新的config_loader
from src.models.v1_grid_classifier.end_to_end_model import GridClassifierV1 # 导入V1模型
# TODO: 未来需要在这里导入其他模型，并使用字典或if-elif结构动态选择
from src.models.end_to_end_model import EndToEndModel # 导入EndToEndModel (通用模型，可能需要修改路径)

def get_model_class_by_name(model_name: str):
    """根据模型名称返回对应的模型类。"""
    # 移除_model后缀进行匹配
    if model_name.endswith('_model'):
        model_name = model_name[: -len('_model')]

    if model_name == 'v1_grid_classifier': # 假设配置文件中的模型名称与此匹配
        return GridClassifierV1
    elif model_name == 'end_to_end': # EndToEndModel
        return EndToEndModel
    # TODO: 添加其他模型
    else:
        raise ValueError(f"不支持的模型名称: {model_name}")

def calculate_ade(pred_traj, gt_traj):
    """
    计算平均位移误差 (ADE)
    """
    # (T, 2) -> (T)
    error = np.linalg.norm(pred_traj - gt_traj, axis=-1)
    # (T) -> scalar
    ade = np.mean(error)
    return ade

def calculate_fde(pred_traj, gt_traj):
    """计算最终位移误差 (FDE)"""
    # (2,) -> scalar
    error = np.linalg.norm(pred_traj[-1] - gt_traj[-1], axis=-1)
    return error

def evaluate(main_config_path: str, model_config_name: str, checkpoint_path: str):
    """
    主评估函数
    Args:
        main_config_path (str): 主配置文件的路径。
        model_config_name (str): 模型特定配置文件的名称（例如：`v1_grid_classifier_model.yaml`）。
        checkpoint_path (str): 模型权重文件的路径。
    """
    # 1. 加载主配置和模型特定配置
    config = load_config(main_config_path) # 加载主配置文件
    
    # 加载模型特定配置并合并
    model_specific_config_path = os.path.join('configs', 'models', model_config_name)
    model_config = load_config(model_specific_config_path)
    update_config(config, model_config) # 使用update_config进行递归合并

    # 2. 设置设备
    device = torch.device(config.device if hasattr(config, 'device') else ('cuda' if torch.cuda.is_available() else 'cpu')) # 检查属性是否存在
    print(f"使用设备: {device}")

    # 3. 加载归一化统计数据
    # 确保 config.data_preprocessing.output_path 是正确的基目录
    normalization_stats_path = Path(config.data_preprocessing.output_path) / 'normalization_stats.pkl'
    if not normalization_stats_path.exists():
        raise FileNotFoundError(f"错误: 归一化统计文件未找到: {normalization_stats_path}")

    normalization_stats = load_normalization_stats(str(normalization_stats_path)) # load_normalization_stats 期望字符串路径

    # 确保这里使用正确的数据归一化统计量
    mean_x = normalization_stats['target_x_mean']
    std_x = normalization_stats['target_x_std']
    mean_y = normalization_stats['target_y_mean']
    std_y = normalization_stats['target_y_std']

    # 4. 构建模型并加载权重
    model_name_base = Path(model_config_name).stem # 从文件名中提取模型名称，例如 'v1_grid_classifier_model'
    ModelClass = get_model_class_by_name(model_name_base)
    
    # 检查是否是V1模型，V1模型需要额外的dest_mean和dest_std参数
    if ModelClass == GridClassifierV1:
        dest_mean_for_model = torch.tensor([mean_x, mean_y], dtype=torch.float32).to(device)
        dest_std_for_model = torch.tensor([std_x, std_y], dtype=torch.float32).to(device)
        model = ModelClass(config, dest_mean_for_model, dest_std_for_model).to(device)
    else: # 其他模型，如EndToEndModel，传入 config 对象本身
        # 对于 EndToEndModel，它可能直接从 config 中读取所需的模型参数
        # 假设 EndToEndModel 的初始化只需要 config.model 部分
        model = ModelClass(config).to(device) # 将整个 config 对象传入，而不是 config.model

    checkpoint = torch.load(checkpoint_path, map_location=device)
    # 尝试兼容不同的state_dict键名，例如 'model_state_dict' 或直接是模型参数
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint) # 如果直接是state_dict
    
    model.eval()
    print(f"模型权重已从 {checkpoint_path} 加载。")

    # 5. 构建验证数据加载器
    val_dataset = LMDBDataset( # 修改为 LMDBDataset
        config=config, # 传入整个 config 对象
        lmdb_type='val'
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        num_workers=config.training.get('num_workers', config.data_preprocessing.num_processing_workers), # 优先从 training 获取，否则从 data_preprocessing 获取
        pin_memory=config.training.get('pin_memory', True),
        # LMDBDataset 不再需要 collate_fn 参数，因为内部已处理
    )

    total_ade = 0
    total_fde = 0
    total_samples = 0

    with torch.no_grad():
        progress_bar = tqdm(val_loader, desc="正在评估")
        for batch in progress_bar:
            if batch is None:
                continue

            # 准备数据
            # 确保使用字典方式访问键
            history_data = batch['history'].to(device) # 将 'history_features' 改为 'history'
            environment_roi = batch['environment_roi'].to(device)
            ground_truth_destination = batch['ground_truth_destination'].to(device) # 新增: ground_truth_destination
            ground_truth_trajectory = batch['ground_truth_trajectory'].to(device) # 新增: ground_truth_trajectory

            # 模型预测
            if ModelClass == GridClassifierV1:
                # 对于 GridClassifierV1，forward 方法需要 ground_truth_destination 作为 dest_gt
                model_output = model(history_data, environment_roi, ground_truth_destination, ground_truth_trajectory)
                predicted_trajectory = model.get_best_mode_trajectory(model_output['gmm_params_flat']) # 获取反归一化的轨迹
            else: # EndToEndModel
                 # EndToEndModel 期望 ground_truth_trajectory 参数，在评估时也会使用其内部逻辑来处理
                model_output = model(history_data, environment_roi, ground_truth_trajectory=ground_truth_trajectory)
                predicted_trajectory = model_output['predicted_trajectory'] # EndToEndModel 直接输出反归一化的轨迹
            
            # 将预测和真实轨迹从 GPU 转移到 CPU 并转换为 NumPy 数组
            predicted_trajectory_numpy = predicted_trajectory.cpu().numpy()
            ground_truth_trajectory_numpy = ground_truth_trajectory.cpu().numpy()

            # 反归一化 (如果模型已经输出反归一化，则这步可能不再需要)
            # 对于 GridClassifierV1，get_best_mode_trajectory 已经反归一化，所以这里跳过
            # 对于 EndToEndModel，其 predicted_trajectory 也是反归一化的，这里也跳过
            # 但是 ground_truth_trajectory_numpy 是归一化的，需要反归一化
            for i in range(predicted_trajectory_numpy.shape[0]):
                pred_traj_single = predicted_trajectory_numpy[i]
                gt_traj_single = denormalize_trajectory(ground_truth_trajectory_numpy[i], mean_x, std_x, mean_y, std_y) # 仅反归一化真实轨迹

                # 计算ADE和FDE
                total_ade += calculate_ade(pred_traj_single, gt_traj_single)
                total_fde += calculate_fde(pred_traj_single, gt_traj_single)
                total_samples += 1

    avg_ade = total_ade / total_samples
    avg_fde = total_fde / total_samples

    print("\n" + "="*30)
    print("      评 估 结 果")
    print("="*30)
    print(f"  总样本数: {total_samples}")
    print(f"  平均位移误差 (ADE): {avg_ade:.4f} 米")
    print(f"  最终位移误差 (FDE): {avg_fde:.4f} 米")
    print("="*30)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="评估已训练好的轨迹预测模型的性能。")
    parser.add_argument('--main_config', type=str, default='configs/main_config.yaml',
                        help='主配置文件的路径。')
    parser.add_argument('--model_config_name', type=str, required=True,
                        help='模型特定配置文件的名称（例如：`v1_grid_classifier_model.yaml`）。')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='已训练模型权重文件的路径。')
    
    args = parser.parse_args()
    evaluate(args.main_config, args.model_config_name, args.checkpoint) 