#!/usr/bin/env python3
"""
测试V4一次性生成模型（使用聚合数据）
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import numpy as np
import matplotlib.pyplot as plt
import pickle
import logging
from tqdm import tqdm

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset
from src.models.v4_non_autoregressive.model import NonAutoregressiveModel
# from src.utils.visualization import plot_trajectory_prediction  # 暂时注释掉

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model_and_config():
    """加载模型和配置"""
    # 加载配置
    config = load_config('configs/main_config.yaml')

    # 加载V4模型配置
    from omegaconf import OmegaConf
    v4_config = OmegaConf.load('configs/models/v4_non_autoregressive.yaml')
    
    # 加载归一化统计
    normalization_stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    with open(normalization_stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)

    # 创建模型
    model = NonAutoregressiveModel(v4_config.model, normalization_stats)
    
    # 加载最佳模型权重
    checkpoint_dir = Path("checkpoints/v4_non_autoregressive")
    checkpoint_files = list(checkpoint_dir.glob("best_model_*.pth"))
    
    if not checkpoint_files:
        raise FileNotFoundError("没有找到V4模型检查点文件")
    
    # 选择最新的检查点
    latest_checkpoint = max(checkpoint_files, key=lambda x: x.stat().st_mtime)
    logger.info(f"加载模型检查点: {latest_checkpoint}")
    
    checkpoint = torch.load(latest_checkpoint, map_location='cpu')
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, config, normalization_stats

def denormalize_trajectory(trajectory, normalization_stats, data_type='target'):
    """反归一化轨迹数据"""
    if data_type == 'target':
        mean_x = normalization_stats['target_mean']['x']
        std_x = normalization_stats['target_std']['x']
        mean_y = normalization_stats['target_mean']['y']
        std_y = normalization_stats['target_std']['y']
    else:  # history
        mean_x = normalization_stats['history_mean']['x']
        std_x = normalization_stats['history_std']['x']
        mean_y = normalization_stats['history_mean']['y']
        std_y = normalization_stats['history_std']['y']
    
    denormalized = trajectory.copy()
    denormalized[..., 0] = trajectory[..., 0] * std_x + mean_x
    denormalized[..., 1] = trajectory[..., 1] * std_y + mean_y
    
    return denormalized

def test_model_inference():
    """测试模型推理"""
    logger.info("=== V4一次性生成模型测试 ===")
    
    # 加载模型和配置
    model, config, normalization_stats = load_model_and_config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    logger.info(f"使用设备: {device}")
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 加载测试数据 - 直接使用路径
    import lmdb
    val_lmdb_path = f"{config.data_preprocessing.output_path}/val"

    # 简化：直接读取LMDB数据
    env = lmdb.open(val_lmdb_path, readonly=True, lock=False)

    # 获取样本数量 - 通过遍历键来计算
    with env.begin() as txn:
        cursor = txn.cursor()
        num_samples = 0
        for key, _ in cursor:
            try:
                int(key.decode())  # 只计算数字键
                num_samples += 1
            except ValueError:
                continue

    logger.info(f"验证集样本数: {num_samples}")
    # 测试几个样本
    num_test_samples = min(4, num_samples)
    
    results = []
    
    with torch.no_grad():
        for i in range(num_test_samples):
            # 直接从LMDB读取样本
            with env.begin() as txn:
                sample_data = txn.get(f'{i}'.encode())
                if sample_data is None:
                    continue
                sample = pickle.loads(sample_data)
            
            # 准备输入数据
            history_trajectory = torch.FloatTensor(sample['history_features']).unsqueeze(0).to(device)
            history_mask = torch.FloatTensor(sample['history_mask']).unsqueeze(0).to(device)
            environment_roi = torch.FloatTensor(sample['environment_roi']).unsqueeze(0).to(device)
            
            # 模型推理
            start_time = torch.cuda.Event(enable_timing=True) if device.type == 'cuda' else None
            end_time = torch.cuda.Event(enable_timing=True) if device.type == 'cuda' else None
            
            if device.type == 'cuda':
                start_time.record()
            
            predicted_trajectory, predicted_destination = model(
                history_trajectory, history_mask, environment_roi
            )
            
            if device.type == 'cuda':
                end_time.record()
                torch.cuda.synchronize()
                inference_time = start_time.elapsed_time(end_time)
            else:
                inference_time = 0
            
            # 转换为numpy
            pred_traj = predicted_trajectory.cpu().numpy()[0]  # [120, 2]
            pred_dest = predicted_destination.cpu().numpy()[0]  # [2]
            
            # 真实数据
            gt_trajectory = sample['ground_truth_trajectory']  # [120, 2]
            gt_destination = sample['ground_truth_destination']  # [2]
            history_features = sample['history_features']  # [30, 3]
            
            # 反归一化
            pred_traj_denorm = denormalize_trajectory(pred_traj, normalization_stats, 'target')
            pred_dest_denorm = denormalize_trajectory(pred_dest.reshape(1, -1), normalization_stats, 'target')[0]
            gt_traj_denorm = denormalize_trajectory(gt_trajectory, normalization_stats, 'target')
            gt_dest_denorm = denormalize_trajectory(gt_destination.reshape(1, -1), normalization_stats, 'target')[0]
            history_denorm = denormalize_trajectory(history_features[:, :2], normalization_stats, 'history')
            
            # 计算误差
            traj_mse = np.mean((pred_traj_denorm - gt_traj_denorm) ** 2)
            dest_mse = np.mean((pred_dest_denorm - gt_dest_denorm) ** 2)
            
            # 计算ADE和FDE
            ade = np.mean(np.sqrt(np.sum((pred_traj_denorm - gt_traj_denorm) ** 2, axis=1)))
            fde = np.sqrt(np.sum((pred_dest_denorm - gt_dest_denorm) ** 2))
            
            result = {
                'sample_idx': i,
                'predicted_trajectory': pred_traj_denorm,
                'predicted_destination': pred_dest_denorm,
                'ground_truth_trajectory': gt_traj_denorm,
                'ground_truth_destination': gt_dest_denorm,
                'history_trajectory': history_denorm,
                'trajectory_mse': traj_mse,
                'destination_mse': dest_mse,
                'ade': ade,
                'fde': fde,
                'inference_time_ms': inference_time
            }
            
            results.append(result)
            
            logger.info(f"样本 {i+1}/{num_test_samples}:")
            logger.info(f"  轨迹MSE: {traj_mse:.2f}")
            logger.info(f"  目标MSE: {dest_mse:.2f}")
            logger.info(f"  ADE: {ade:.2f}m")
            logger.info(f"  FDE: {fde:.2f}m")
            logger.info(f"  推理时间: {inference_time:.2f}ms")

    # 关闭LMDB环境
    env.close()

    # 计算平均指标
    avg_traj_mse = np.mean([r['trajectory_mse'] for r in results])
    avg_dest_mse = np.mean([r['destination_mse'] for r in results])
    avg_ade = np.mean([r['ade'] for r in results])
    avg_fde = np.mean([r['fde'] for r in results])
    avg_inference_time = np.mean([r['inference_time_ms'] for r in results])
    
    logger.info("\n=== 平均性能指标 ===")
    logger.info(f"平均轨迹MSE: {avg_traj_mse:.2f}")
    logger.info(f"平均目标MSE: {avg_dest_mse:.2f}")
    logger.info(f"平均ADE: {avg_ade:.2f}m")
    logger.info(f"平均FDE: {avg_fde:.2f}m")
    logger.info(f"平均推理时间: {avg_inference_time:.2f}ms")
    
    return results

def create_visualization(results):
    """创建可视化"""
    logger.info("创建可视化...")
    
    num_samples = len(results)
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    for i, result in enumerate(results):
        if i >= 4:
            break
            
        ax = axes[i]
        
        # 绘制历史轨迹
        history = result['history_trajectory']
        ax.plot(history[:, 0], history[:, 1], 'b-', linewidth=2, label='历史轨迹', alpha=0.7)
        ax.scatter(history[-1, 0], history[-1, 1], c='blue', s=100, marker='o', label='当前位置')
        
        # 绘制真实未来轨迹
        gt_traj = result['ground_truth_trajectory']
        ax.plot(gt_traj[:, 0], gt_traj[:, 1], 'g-', linewidth=2, label='真实轨迹', alpha=0.8)
        ax.scatter(gt_traj[-1, 0], gt_traj[-1, 1], c='green', s=100, marker='s', label='真实目标')
        
        # 绘制预测轨迹
        pred_traj = result['predicted_trajectory']
        ax.plot(pred_traj[:, 0], pred_traj[:, 1], 'r--', linewidth=2, label='预测轨迹', alpha=0.8)
        ax.scatter(pred_traj[-1, 0], pred_traj[-1, 1], c='red', s=100, marker='^', label='预测目标')
        
        # 设置标题和标签
        ax.set_title(f'样本 {i+1} - ADE: {result["ade"]:.1f}m, FDE: {result["fde"]:.1f}m')
        ax.set_xlabel('X坐标 (m)')
        ax.set_ylabel('Y坐标 (m)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
    
    plt.tight_layout()
    plt.savefig('v4_aggregated_test_results.png', dpi=300, bbox_inches='tight')
    logger.info("可视化已保存到: v4_aggregated_test_results.png")
    plt.show()

def main():
    """主函数"""
    try:
        # 测试模型推理
        results = test_model_inference()
        
        # 创建可视化
        create_visualization(results)
        
        logger.info("V4聚合模型测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}", exc_info=True)

if __name__ == "__main__":
    main()
