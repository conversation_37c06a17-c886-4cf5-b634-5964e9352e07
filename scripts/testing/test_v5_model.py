#!/usr/bin/env python3
"""
V5模型测试脚本 - 验证改进的目标引导和注意力机制
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import numpy as np
import matplotlib.pyplot as plt
import logging
from datetime import datetime

from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.data.lmdb_dataset import LMDBDataset
from src.utils.config_loader import load_config
from src.utils.metrics import calculate_ade, calculate_fde

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model(model_path, config, device):
    """加载训练好的模型"""
    model = TrajectoryPredictorV5(config)
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()
    
    logger.info(f"成功加载模型: {model_path}")
    logger.info(f"模型训练epoch: {checkpoint.get('epoch', 'N/A')}")
    logger.info(f"验证损失: {checkpoint.get('val_loss', 'N/A'):.6f}")
    logger.info(f"验证ADE: {checkpoint.get('val_ade', 'N/A'):.4f}")
    logger.info(f"验证FDE: {checkpoint.get('val_fde', 'N/A'):.4f}")
    
    return model

def test_model_on_dataset(model, dataset, device, num_samples=10):
    """在数据集上测试模型"""
    model.eval()
    
    total_ade = 0.0
    total_fde = 0.0
    predictions = []
    ground_truths = []
    
    with torch.no_grad():
        for i in range(min(num_samples, len(dataset))):
            sample = dataset[i]
            
            # 准备输入数据
            history_features = sample['history_features'].unsqueeze(0).to(device)
            history_mask = sample['history_mask'].unsqueeze(0).to(device)
            ground_truth_trajectory = sample['ground_truth_trajectory'].unsqueeze(0).to(device)
            ground_truth_destination = sample['ground_truth_destination'].unsqueeze(0).to(device)
            environment_roi = sample['environment_roi'].unsqueeze(0).to(device)
            
            # 模型推理
            predicted_trajectory = model(
                history_features=history_features,
                history_mask=history_mask,
                ground_truth_destination=ground_truth_destination,
                environment_roi=environment_roi
            )
            
            # 转换为numpy
            pred_np = predicted_trajectory.cpu().numpy()[0]  # (prediction_horizon, 2)
            gt_np = ground_truth_trajectory.cpu().numpy()[0]  # (prediction_horizon, 2)
            
            # 计算指标
            ade = calculate_ade(pred_np[np.newaxis, :, :], gt_np[np.newaxis, :, :])
            fde = calculate_fde(pred_np[np.newaxis, :, :], gt_np[np.newaxis, :, :])
            
            total_ade += ade
            total_fde += fde
            
            predictions.append(pred_np)
            ground_truths.append(gt_np)
            
            logger.info(f"样本 {i+1}: ADE={ade:.4f}, FDE={fde:.4f}")
    
    avg_ade = total_ade / num_samples
    avg_fde = total_fde / num_samples
    
    logger.info(f"平均ADE: {avg_ade:.4f}")
    logger.info(f"平均FDE: {avg_fde:.4f}")
    
    return predictions, ground_truths, avg_ade, avg_fde

def visualize_predictions(predictions, ground_truths, num_plots=4):
    """可视化预测结果"""
    num_plots = min(num_plots, len(predictions))
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    for i in range(num_plots):
        ax = axes[i]
        
        pred = predictions[i]
        gt = ground_truths[i]
        
        # 绘制轨迹
        ax.plot(gt[:, 0], gt[:, 1], 'b-', linewidth=2, label='真实轨迹', alpha=0.8)
        ax.plot(pred[:, 0], pred[:, 1], 'r--', linewidth=2, label='预测轨迹', alpha=0.8)
        
        # 标记起点和终点
        ax.scatter(gt[0, 0], gt[0, 1], c='blue', s=100, marker='o', label='真实起点', zorder=5)
        ax.scatter(gt[-1, 0], gt[-1, 1], c='blue', s=100, marker='s', label='真实终点', zorder=5)
        ax.scatter(pred[0, 0], pred[0, 1], c='red', s=100, marker='o', label='预测起点', zorder=5)
        ax.scatter(pred[-1, 0], pred[-1, 1], c='red', s=100, marker='s', label='预测终点', zorder=5)
        
        # 计算指标
        ade = np.mean(np.linalg.norm(pred - gt, axis=1))
        fde = np.linalg.norm(pred[-1] - gt[-1])
        
        ax.set_title(f'样本 {i+1}\nADE: {ade:.2f}, FDE: {fde:.2f}')
        ax.set_xlabel('X坐标')
        ax.set_ylabel('Y坐标')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
    
    plt.tight_layout()
    
    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'v5_model_predictions_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    logger.info(f"预测可视化已保存: {filename}")
    
    plt.show()

def analyze_attention_weights(model, sample, device):
    """分析注意力权重（如果模型支持）"""
    model.eval()
    
    with torch.no_grad():
        # 准备输入数据
        history_features = sample['history_features'].unsqueeze(0).to(device)
        history_mask = sample['history_mask'].unsqueeze(0).to(device)
        ground_truth_destination = sample['ground_truth_destination'].unsqueeze(0).to(device)
        environment_roi = sample['environment_roi'].unsqueeze(0).to(device)
        
        # 获取注意力权重（需要修改模型以返回注意力权重）
        predicted_trajectory = model(
            history_features=history_features,
            history_mask=history_mask,
            ground_truth_destination=ground_truth_destination,
            environment_roi=environment_roi
        )
        
        logger.info("注意力分析功能需要进一步实现")

def main():
    """主测试函数"""
    # 加载配置
    config = load_config('configs/main_config.yaml')
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 模型路径
    model_path = "models/trajectory_predictor_v5_best.pth"
    
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        logger.info("请先训练V5模型")
        return
    
    # 加载模型
    model = load_model(model_path, config, device)
    
    # 打印模型信息
    model_info = model.get_model_info()
    logger.info("=== V5模型信息 ===")
    for key, value in model_info.items():
        logger.info(f"{key}: {value}")
    
    # 加载测试数据集
    data_path = config.data_preprocessing.output_path
    val_dataset = LMDBDataset(
        lmdb_path=f"{data_path}/val",
        normalization_stats_path=f"{data_path}/normalization_stats.pkl"
    )
    
    logger.info(f"验证集样本数: {len(val_dataset)}")
    
    if len(val_dataset) == 0:
        logger.error("验证集为空，请检查数据预处理")
        return
    
    # 测试模型
    logger.info("=== 开始模型测试 ===")
    predictions, ground_truths, avg_ade, avg_fde = test_model_on_dataset(
        model, val_dataset, device, num_samples=20
    )
    
    # 可视化结果
    logger.info("=== 生成可视化结果 ===")
    visualize_predictions(predictions, ground_truths, num_plots=4)
    
    # 分析注意力权重（示例）
    if len(val_dataset) > 0:
        logger.info("=== 注意力权重分析 ===")
        analyze_attention_weights(model, val_dataset[0], device)
    
    logger.info("=== 测试完成 ===")
    logger.info(f"最终结果 - 平均ADE: {avg_ade:.4f}, 平均FDE: {avg_fde:.4f}")

if __name__ == "__main__":
    main()
