# -*- coding: utf-8 -*-
"""
@_Time_   : 2025/07/19 10:00
@_Author_ : 郑明灿
@_File_   : check_lmdb_sample.py
@_IDE_    : PyCharm
@_Aim_    : 检查LMDB中的单个样本，验证其结构和维度是否正确。
"""
import lmdb
import pickle
import os

# 创建时间: 2025/07/19
# 功能: 检查LMDB数据库中的单个数据样本，验证其内部结构、特征键、数据类型和维度（形状）是否符合预期。
# 输入:
#   - `lmdb_path`: LMDB数据库的路径（例如：`data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train`）。
# 输出: 
#   - 打印所检查样本的键名。
#   - 详细列出样本字典中每个特征的键、数据类型和其维度/形状。
#   - 错误或警告信息（例如：LMDB路径不存在、数据库为空）。
# 原理:
#   - 使用 `lmdb` 库以只读模式打开指定的LMDB数据库环境。
#   - 通过数据库游标 (`txn.cursor()`) 获取数据库中的第一个数据样本（键-值对）。
#   - 使用 `pickle` 库对获取到的二进制值进行反序列化，还原为原始的Python字典（即数据样本）。
#   - 遍历反序列化后的样本字典，获取每个特征的键名、Python数据类型，并尝试获取其 `shape` 属性（适用于NumPy数组、PyTorch张量等）。
# 处理方法:
#   - 接收LMDB数据库路径作为参数。
#   - 率先检查数据库路径的有效性，如果路径不存在，则给出清晰的错误提示，并建议可能的解决方案。
#   - 安全地打开LMDB环境，设置 `readonly=True`, `lock=False`, `readahead=False`, `meminit=False` 以优化读取性能并避免锁定问题。
#   - 获取第一个样本并进行反序列化，如果数据库为空，则给出警告。
#   - 逐一打印样本中的所有键，并显示其对应值的数据类型及形状信息。
#   - 脚本的 `main` 函数示例性地展示了如何检查训练集LMDB数据库。
def check_sample(lmdb_path):
    """
    打开LMDB并打印第一个样本的信息。
    """
    if not os.path.exists(lmdb_path):
        print(f"[错误] LMDB数据库未找到: {lmdb_path}")
        # 尝试检查上级目录是否存在
        parent_dir = os.path.dirname(lmdb_path)
        if os.path.exists(parent_dir):
            print(f"目录 {parent_dir} 存在，但其中的 data.mdb 文件缺失。")
            print("这可能意味着预处理脚本未能成功生成数据库文件。")
        else:
            print(f"目录 {parent_dir} 也不存在。请确认预处理脚本已运行。")
        return

    print(f"--- 正在检查LMDB数据库: {lmdb_path} ---")
    env = lmdb.open(lmdb_path, readonly=True, lock=False, readahead=False, meminit=False)

    with env.begin(write=False) as txn:
        # 获取第一个样本
        cursor = txn.cursor()
        if not cursor.first():
            print("[警告] 数据库为空，没有任何样本。")
            return
            
        key, value = cursor.item()
        sample = pickle.loads(value)

        print(f"成功读取样本，键: {key.decode()}")
        print("\n样本内容结构:")
        for data_key, data_value in sample.items():
            print(f"  - 键: '{data_key}', 类型: {type(data_value)}, 维度/形状: {getattr(data_value, 'shape', 'N/A')}")
            
        print("\n--- 检查完毕 ---")

if __name__ == "__main__":
    # 检查训练集
    train_lmdb_path = "data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train"
    check_sample(train_lmdb_path) 