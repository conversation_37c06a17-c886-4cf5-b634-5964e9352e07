"""
创建时间: 2023-11-19
功能: 本脚本旨在提供一个全面的数据处理流水线检查工具，从原始轨迹CSV数据开始，逐步验证数据在LMDB存储中、以及通过PyTorch的Dataset和DataLoader加载后的状态。它通过分阶段打印数据样本的详细信息，帮助用户确认数据预处理的正确性和模型输入数据的格式。
输入:
  - `main_config_path`: 字符串，主配置文件的YAML文件路径（例如：`configs/main_config.yaml`）。此文件包含了通用配置和指向模型特定配置的路径。
  - `raw_csv_path`: 字符串，一个原始轨迹CSV文件的路径（例如：`data/trajectories/trajectory_099_3_high_mobility.csv`），用于检查原始数据。
  - `lmdb_key_to_inspect`: 字符串，可选参数，指定LMDB中特定样本的键名。如果为 `None`，脚本将自动查找并使用LMDB中的第一个可用键进行检查。
输出:
  - **阶段1 (原始CSV数据检查):** 打印原始CSV文件路径、前几行数据、数据类型信息和基本统计。
  - **阶段2 (LMDB样本检查):** 打印LMDB数据库路径、前几个可用的键名、指定或第一个样本反序列化后的内容概览（包括内部键、数据类型、形状以及部分数据内容，如观测轨迹、未来真值轨迹、目标点和元数据）。
  - **阶段3 (模型输入张量检查):** 打印通过 `LMDBDataset` 和 `DataLoader` 处理后，送入模型训练的PyTorch张量（包括 `history_features`、`ground_truth_trajectory`、`ground_truth_destination` 和 `environment_roi`）的形状、数据类型以及前几行数据，并对归一化、特征合并等现象进行观察总结。
原理:
  - **Python路径设置:** 脚本在启动时将项目根目录动态添加到 `sys.path`，确保能够正确导入项目内部的模块（如 `src.data.datasets.LMDBDataset`）。
  - **CSV数据读取:** 使用 `pandas` 库读取并展示原始CSV文件的内容和结构，验证其格式是否符合预期。
  - **LMDB数据访问:** 利用 `lmdb` 库以只读模式访问预处理后的LMDB数据库。通过 `txn.cursor()` 遍历键值对，并使用 `pickle` 对二进制值进行反序列化，还原为Python对象。
  - **PyTorch Dataset/DataLoader:** 实例化 `src.data.datasets.LMDBDataset`，该类负责根据配置文件从LMDB加载并处理单个样本，包括数据归一化、特征编码、ROI提取等。然后，使用 `torch.utils.data.DataLoader` 封装数据集，模拟模型训练时的数据批处理过程。
  - **张量内容检查:** 从 DataLoader 中获取一个批次的数据（通常是单个样本），并详细检查其中每个关键张量（如历史轨迹、未来轨迹、目标点和环境ROI）的维度和数值范围，判断其是否经过正确的归一化和格式化。
处理方法:
  - 脚本通过 `if __name__ == "__main__":` 块设置默认的配置文件和原始CSV路径，可以直接运行进行测试。
  - 使用清晰的阶段划分和标题，引导用户逐步检查数据。
  - 在每个阶段都包含了错误处理机制（`try-except` 块），以捕获文件I/O、LMDB访问、数据反序列化或PyTorch数据加载过程中可能出现的异常，并提供详细的错误信息。
  - 打印输出针对不同数据类型（如DataFrame, Dict, NumPy Array, PyTorch Tensor）进行格式化，确保信息的可读性。
  - 动态获取LMDB中的第一个键进行检查，增加了脚本的鲁棒性，避免了硬编码特定键的限制。
"""
import sys
import os

# 必须在导入我们自己的模块之前，将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

import pandas as pd
import lmdb
import pickle
import yaml
import torch
from torch.utils.data import DataLoader

from src.data.datasets import LMDBDataset # 更新导入路径
from src.utils.config_loader import load_config, update_config # 导入config_loader

def inspect_data_pipeline(main_config_path, raw_csv_path, lmdb_key_to_inspect):
    """
    检查数据处理管道的三个关键阶段：
    1. 原始CSV数据
    2. 存入LMDB的序列化样本
    3. 经过Dataset和DataLoader处理后，送入模型的张量
    """
    print("=" * 50)
    print("阶段 1: 检查原始CSV数据")
    print("=" * 50)
    try:
        raw_df = pd.read_csv(raw_csv_path)
        print(f"成功读取原始CSV文件: {raw_csv_path}")
        print("原始数据前5行:")
        print(raw_df.head())
        # 我们可以看到坐标x, y的数值很大，而速度、加速度等特征数值较小
        # 这也验证了我们分开进行归一化的必要性
        print("\n原始数据信息:")
        raw_df.info()
        print("\n")
    except Exception as e:
        print(f"读取原始CSV文件失败: {e}\n")
        return

    # 加载主配置和模型特定配置
    config = load_config(main_config_path) # 加载主配置文件

    # 检查主配置文件中是否有model_config_path，并加载合并
    if 'model_config_path' in config:
        model_specific_config_path = os.path.join('configs', 'models', config.model_config_path)
        model_config = load_config(model_specific_config_path)
        update_config(config, model_config) # 使用update_config进行递归合并
    else:
        print("主配置文件中未找到 'model_config_path' 字段，请确保配置正确。")

    # LMDB路径现在应该在合并后的config中
    # 优先使用data_preprocessing.output_path，因为它指定了LMDB的根目录
    lmdb_base_path = config['data_preprocessing']['output_path']
    # 根据训练/验证分割，选择具体的LMDB路径
    lmdb_path = os.path.join(lmdb_base_path, 'train') # 默认检查训练集LMDB

    print("=" * 50)
    print("阶段 2: 检查LMDB中的单个样本")
    print("=" * 50)
    try:
        env = lmdb.open(lmdb_path, readonly=True, lock=False, readahead=False, meminit=False)
        with env.begin(write=False) as txn:
            # 先打印出前5个key，帮助我们找到一个有效的key
            cursor = txn.cursor()
            print("LMDB中的前5个keys:")
            keys = []
            for i, (key, _) in enumerate(cursor):
                if i >= 5:
                    break
                decoded_key = key.decode('utf-8')
                keys.append(decoded_key)
                print(f"  - {decoded_key}")

            # 使用列表中的第一个key进行检查
            if keys:
                lmdb_key_to_inspect = keys[0]
                print(f"\n将使用第一个key进行检查: '{lmdb_key_to_inspect}'")
            else:
                print("错误: LMDB数据库为空或无法读取keys。")
                return

            serialized_sample = txn.get(lmdb_key_to_inspect.encode('utf-8'))
            if serialized_sample:
                sample = pickle.loads(serialized_sample)
                print(f"成功从LMDB读取并反序列化样本 (key: '{lmdb_key_to_inspect}')")
                
                print("\n样本内容概览:")
                for key, value in sample.items():
                    if isinstance(value, dict):
                         print(f"  - {key}: {value}")
                    elif hasattr(value, 'shape'):
                        print(f"  - {key}: shape={value.shape}, dtype={value.dtype}")
                    else:
                        print(f"  - {key}: {value}")

                # 注意：这里的列名是根据我们对数据处理的理解推断的，
                # 因为它没有在v2的配置文件中明确指定。
                # 这与旧的预处理流程不同。
                # obs_feature_columns = [
                #     'x', 'y', 'timestamp_ms', 
                #     'velocity_x', 'velocity_y', 
                #     'acceleration_x', 'acceleration_y', 
                #     'heading', 'curvature'
                # ]
                # 历史特征现在应该在 'history_features' 键下
                # LMDB中存储的history_features的列顺序由data_preprocessing.yaml的history_features定义
                history_feature_names = config['data_preprocessing']['history_features']

                print("\n观测轨迹 (history_features):")
                # 确保列名数量与数据维度匹配
                if 'history_features' in sample and sample['history_features'].shape[1] == len(history_feature_names):
                    print(pd.DataFrame(sample['history_features'], columns=history_feature_names).head())
                else:
                    print(f"键 'history_features' 不存在或形状不匹配。实际形状：{sample['history_features'].shape if 'history_features' in sample else 'N/A'}, 期望列数：{len(history_feature_names)}")
                    if 'history_features' in sample:
                        print(sample['history_features'][:5,:])
                
                print("\n未来真值轨迹 (ground_truth_trajectory):")
                # 未来轨迹只有x, y两列
                if 'ground_truth_trajectory' in sample:
                    print(pd.DataFrame(sample['ground_truth_trajectory'], columns=['x', 'y']).head())
                else:
                    print("键 'ground_truth_trajectory' 不存在。")

                print("\n未来真值目标点 (ground_truth_destination):", sample.get('ground_truth_destination', 'N/A'))
                print("环境ROI序列 (environment_roi_sequence):", sample.get('environment_roi_sequence', 'N/A').shape)
                print("元数据 (file_id):", sample.get('file_id', 'N/A'))
                print("时间戳 (timestamp_ms):", sample.get('timestamp_ms', 'N/A'))

            else:
                print(f"在LMDB中未找到键: {lmdb_key_to_inspect}")
    except Exception as e:
        print(f"检查LMDB失败: {e}\n")
        # env.close() # 已经有了finally块来关闭
        return
    finally:
        if 'env' in locals(): # 确保env变量已经定义
            env.close()
    
    print("\n")
    print("=" * 50)
    print("阶段 3: 检查送入模型的张量 (来自Dataset/DataLoader)")
    print("=" * 50)
    try:
        # 我们使用训练集的配置来初始化Dataset
        dataset = LMDBDataset(lmdb_path=lmdb_path, config=config) # 传递完整的config对象
        # 创建一个DataLoader来获取一个批次的数据
        data_loader = DataLoader(dataset, batch_size=1, shuffle=False)

        # 获取第一个批次的数据
        batch = next(iter(data_loader))

        print("成功从DataLoader获取一个批次的数据。")
        print("批次中包含的键:", batch.keys())
        
        # 提取第一个（也是唯一一个）样本的数据
        print("\n--- 单个样本的张量数据 (已归一化和编码) ---")
        
        hist_traj_tensor = batch['history_features'][0] # 更新键名
        future_traj_tensor = batch['ground_truth_trajectory'][0] # 更新键名
        goal_tensor = batch['ground_truth_destination'][0] # 更新键名
        env_roi_tensor = batch['environment_roi'][0]


        print(f"\n历史轨迹张量 (history_features):") # 更新键名
        print(f"  - Shape: {hist_traj_tensor.shape}")
        print(f"  - 前几个值:\n{hist_traj_tensor[:3, :]}")

        print(f"\n未来轨迹张量 (ground_truth_trajectory):") # 更新键名
        print(f"  - Shape: {future_traj_tensor.shape}")
        print(f"  - 前几个值:\n{future_traj_tensor[:3, :]}")

        print(f"\n目标点张量 (ground_truth_destination):") # 更新键名
        print(f"  - Shape: {goal_tensor.shape}")
        print(f"  - 值: {goal_tensor}")

        print(f"\n环境ROI张量 (environment_roi):")
        print(f"  - Shape: {env_roi_tensor.shape}")


        print("\n观察:")
        print("1. 轨迹数据的数值都在0附近，表明归一化已生效。")
        print("2. 坐标和动态特征都在同一个张量中，但我们在Dataset中是分开归一化的。")
        print("3. 环境ROI包含了DEM和独热编码的地表覆盖，合并在了一起。")
        print("4. 所有数据都已转换成PyTorch张量，可以直接输入模型。")

    except Exception as e:
        print(f"检查Dataset/DataLoader输出失败: {e}\n")

if __name__ == "__main__":
    # 使用主配置文件，其中应包含模型特定的配置路径
    MAIN_CONFIG_PATH = 'configs/main_config.yaml'
    # 使用我们之前选定的CSV文件
    RAW_CSV_PATH = 'data/trajectories/trajectory_099_3_high_mobility.csv' # 更新为data/trajectories
    
    LMDB_KEY = None # 设置为None，让脚本动态获取

    inspect_data_pipeline(MAIN_CONFIG_PATH, RAW_CSV_PATH, LMDB_KEY) 
 
 
 
 
 
 