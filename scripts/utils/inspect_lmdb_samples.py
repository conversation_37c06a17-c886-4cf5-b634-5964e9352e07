#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建时间: 2025-07-19 23:15
功能: 检查LMDB数据集中的样本数据
输入: LMDB数据库路径
输出: 样本的详细信息，包括形状、数据类型、数值范围等
原理: 读取LMDB数据库中的样本，分析其结构和内容
作者: 郑明灿
"""

import os
import pickle
import argparse
import numpy as np
import lmdb
from pathlib import Path

def inspect_lmdb_samples(db_path: str, num_samples: int = 5):
    """
    检查LMDB数据库中的样本
    
    Args:
        db_path: LMDB数据库路径
        num_samples: 要检查的样本数量
    """
    print(f"=== 检查LMDB数据库: {db_path} ===")
    
    if not os.path.exists(db_path):
        print(f"错误: 数据库路径不存在: {db_path}")
        return
    
    # 打开LMDB数据库
    env = lmdb.open(db_path, readonly=True)
    
    with env.begin() as txn:
        # 获取数据库统计信息
        stats = txn.stat()
        total_samples = stats['entries']
        print(f"数据库总样本数: {total_samples}")
        
        # 检查前几个样本
        cursor = txn.cursor()
        sample_count = 0
        
        for key, value in cursor:
            if sample_count >= num_samples:
                break
                
            print(f"\n--- 样本 {sample_count + 1} ---")
            print(f"键: {key.decode('ascii')}")
            
            # 反序列化样本
            try:
                sample = pickle.loads(value)
                print(f"样本类型: {type(sample)}")
                print(f"样本键: {list(sample.keys())}")
                
                # 检查每个字段
                for field_name, field_data in sample.items():
                    print(f"\n  字段: {field_name}")
                    print(f"    类型: {type(field_data)}")
                    
                    if isinstance(field_data, np.ndarray):
                        print(f"    形状: {field_data.shape}")
                        print(f"    数据类型: {field_data.dtype}")
                        print(f"    数值范围: [{field_data.min():.4f}, {field_data.max():.4f}]")
                        print(f"    均值: {field_data.mean():.4f}")
                        print(f"    标准差: {field_data.std():.4f}")
                        
                        # 对于轨迹数据，显示更多信息
                        if field_name == 'trajectory_sequence':
                            print(f"    轨迹长度: {field_data.shape[0]}")
                            print(f"    特征数量: {field_data.shape[1]}")
                            print(f"    前3个时间步:")
                            for i in range(min(3, field_data.shape[0])):
                                print(f"      时间步{i}: {field_data[i]}")
                        
                        elif field_name == 'ground_truth_trajectory':
                            print(f"    预测长度: {field_data.shape[0]}")
                            print(f"    前3个预测点:")
                            for i in range(min(3, field_data.shape[0])):
                                print(f"      预测点{i}: {field_data[i]}")
                        
                        elif field_name == 'environment_roi':
                            print(f"    环境ROI通道数: {field_data.shape[0]}")
                            print(f"    高度: {field_data.shape[1]}")
                            print(f"    宽度: {field_data.shape[2]}")
                            print(f"    通道0(DEM)范围: [{field_data[0].min():.2f}, {field_data[0].max():.2f}]")
                            if field_data.shape[0] > 1:
                                print(f"    通道1(地表覆盖)范围: [{field_data[1].min():.2f}, {field_data[1].max():.2f}]")
                    
                    elif isinstance(field_data, (int, float)):
                        print(f"    值: {field_data}")
                    
                    elif isinstance(field_data, str):
                        print(f"    值: {field_data}")
                    
                    else:
                        print(f"    值: {field_data}")
                
            except Exception as e:
                print(f"    反序列化错误: {e}")
            
            sample_count += 1
    
    env.close()
    print(f"\n=== 检查完成，共检查了 {sample_count} 个样本 ===")

def main():
    parser = argparse.ArgumentParser(description="检查LMDB数据集中的样本")
    parser.add_argument('--db_path', type=str, required=True, help='LMDB数据库路径')
    parser.add_argument('--num_samples', type=int, default=3, help='要检查的样本数量')
    
    args = parser.parse_args()
    inspect_lmdb_samples(args.db_path, args.num_samples)

if __name__ == "__main__":
    main() 