"""
创建时间: 2023-11-19
功能: 从 `TrajectoryDataset` 加载指定数量的样本，并详细打印每个样本的内部结构、特征键、数据类型和维度（形状），以及部分数据内容，用于验证数据集的正确性和完整性。
输入:
  - `config_path`: 字符串，指定用于加载数据集的YAML配置文件路径（例如：`configs/main_config.yaml`）。此配置文件应包含 `TrajectoryDataset` 初始化所需的所有参数，如LMDB路径等。
  - `num_samples`: 整数，可选参数，指定要检查的样本数量，默认为3。
输出:
  - 数据集初始化状态和总样本数。
  - 每个被检查样本的详细报告，包括其内部所有特征的键名、数据类型（`torch.Tensor` 或 `numpy.ndarray` 等）以及数据的形状。
  - 对于Tensor或ndarray类型的数据，会打印其数据类型和部分数据内容（例如：1D数组的前5个元素，多维数组的2x2切片），以便快速目视检查。
  - 如果数据集为空或在加载/处理样本时发生错误，会打印相应的警告或错误信息。
原理:
  - 脚本首先加载指定的YAML配置文件，从中获取数据集构建所需的参数。
  - 实例化 `src.modeling.dataset.TrajectoryDataset` 类，该类负责从底层LMDB数据库或其他数据源加载预处理后的轨迹数据样本。
  - 通过迭代数据集（或直接通过索引访问），获取指定数量的样本。
  - 对于每个获取到的样本，它是一个字典，脚本会遍历字典中的每个键值对。
  - 利用Python的 `isinstance` 函数判断值的类型，特别是区分 `torch.Tensor` 和 `numpy.ndarray`。
  - 根据数据类型，使用 `shape` 属性获取其维度，并采用切片或 `.item()` 方法安全地打印部分数据，避免输出过大。
处理方法:
  - 脚本通过命令行参数或硬编码的配置文件路径来启动。
  - 在加载数据集之前，会进行基本的路径和数据集可用性检查。
  - 使用 `np.set_printoptions` 调整NumPy数组的打印格式，使其更易读。
  - 遍历每个选定的样本，并对其内部的每个数据键执行详细的检查和打印。
  - 对可能发生的加载或打印错误进行异常捕获，确保脚本的健壮性。
  - 旨在提供一个直观的工具，帮助开发者快速验证预处理流水线和数据集内容的正确性。
"""
import pandas as pd
import torch
from torch.utils.data import DataLoader
import yaml
import sys
import os
import logging
import argparse

# 调整sys.path，确保能导入项目根目录下的模块
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.data.datasets import LMDBDataset # 更新导入路径
from src.utils.config_loader import load_config # 添加导入

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def inspect_dataset_samples(config_path, num_samples_to_inspect=3):
    """
    从 TrajectoryDataset 加载几个样本并打印其内容。
    """
    try:
        # 加载主配置和数据预处理配置
        main_config_path = config_path
        data_prep_config_path = 'configs/data_preprocessing.yaml'
        # 使用config_loader加载并合并配置
        config = load_config(main_config_path, override_path=data_prep_config_path)

        # 将LMDB路径设置为数据预处理配置中定义的输出路径，并指向训练集
        lmdb_path = os.path.join(config['data_preprocessing']['output_path'], 'train')

        # 实例化 TrajectoryDataset
        # LMDBDataset现在只需要lmdb_path和config，不需要normalization_stats
        dataset = LMDBDataset(lmdb_path=lmdb_path, config=config) # 传入config
        print(f"成功初始化数据集。总样本数: {len(dataset)}")

        if len(dataset) == 0:
            print("警告: 数据集为空，无法检查样本。")
            return

        # 遍历数据集并打印样本信息
        print(f"\n正在检查前 {num_samples_to_inspect} 个样本:\n")
        for i in range(min(num_samples_to_inspect, len(dataset))):
            try:
                sample = dataset[i]
                print(f"--- 样本 {i} ---")
                for key, value in sample.items():
                    if isinstance(value, torch.Tensor):
                        print(f"  - {key}: Shape={value.shape}, Dtype={value.dtype}, Min={value.min():.4f}, Max={value.max():.4f}")
                        if value.dim() == 2: # 打印前几行对于二维张量
                            print(f"    前3行:\n{value[:3, :]}")
                        elif value.dim() == 1: # 打印所有值对于一维张量
                            print(f"    值: {value}")
                    elif isinstance(value, np.ndarray):
                         print(f"  - {key}: Shape={value.shape}, Dtype={value.dtype}")
                         if value.ndim == 2: # 打印前几行对于二维数组
                             print(f"    前3行:\n{value[:3, :]}")
                         elif value.ndim == 1: # 打印所有值对于一维数组
                             print(f"    值: {value}")
                    else:
                        print(f"  - {key}: {type(value).__name__} = {value}")
                print("\n")
            except Exception as e:
                print(f"错误: 处理样本 {i} 时发生异常: {e}")
                import traceback
                traceback.print_exc()
                continue
    except Exception as e:
        print(f"错误: 初始化或加载数据集时出错: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="检查TrajectoryDataset中的数据样本。")
    parser.add_argument('--config', type=str, default='configs/main_config.yaml', help='配置文件路径，例如 configs/main_config.yaml。')
    parser.add_argument('--num_samples', type=int, default=3, help='要检查的样本数量。')
    args = parser.parse_args()
    
    # 加载主配置和数据预处理配置
    main_config_path = args.config
    data_prep_config_path = 'configs/data_preprocessing.yaml'
    # 使用config_loader加载并合并配置
    config = load_config(main_config_path, override_path=data_prep_config_path)
    
    # 将LMDB路径设置为数据预处理配置中定义的输出路径，并指向训练集
    config.data.lmdb_path = os.path.join(config['data_preprocessing']['output_path'], 'train')
    
    inspect_dataset_samples(config, args.num_samples) 
 
 
 
 
 
 