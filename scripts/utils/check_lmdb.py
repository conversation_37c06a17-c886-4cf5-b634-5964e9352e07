"""
创建时间: 2023-11-19
功能: 检查LMDB数据库中的元数据，包括样本总数和可能存在的统计数据。
输入:
  - LMDB数据库的路径 (例如: `data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train`)，通常从 `configs/data_preprocessing.yaml` 中配置的 `data_preprocessing.output_path` 获取。
输出: 打印LMDB数据库中是否存在 'num_samples' 键及其值，以及 '__stats__' 键是否可读和反序列化。
原理:
  - 使用LMDB库以只读模式打开指定的数据库环境。
  - 尝试从数据库中获取特定的元数据键（'num_samples' 和 '__stats__'）。
  - 对于 'num_samples'，验证其是否存在并解码为整数。
  - 对于 '__stats__'，验证其是否存在并尝试使用 `pickle` 进行反序列化，以确认数据完整性。
处理方法:
  - 接收LMDB数据库的路径作为参数。
  - 检查给定路径是否存在，若不存在则报告错误。
  - 安全地打开LMDB环境，并启动一个只读事务。
  - 分别查询 'num_samples' 和 '__stats__' 键，并根据查询结果打印相应的成功/失败信息。
  - 对 '__stats__' 的反序列化操作进行异常捕获，以处理数据损坏或格式不匹配的情况。
  - 脚本会自动尝试从 `configs/main_config.yaml` 中加载默认的LMDB路径进行检查。
"""
import lmdb
import pickle
import yaml
from pathlib import Path

def check_lmdb_metadata(db_path):
    """
    检查LMDB数据库中的元数据是否存在且可读。
    """
    print(f"--- 正在检查 LMDB 数据库: {db_path} ---")
    
    if not Path(db_path).exists():
        print(f"错误: 数据库路径不存在: {db_path}")
        return

    try:
        env = lmdb.open(db_path, readonly=True, lock=False)
    except lmdb.Error as e:
        print(f"错误: 无法打开LMDB环境: {e}")
        return

    with env.begin(write=False) as txn:
        # 1. 检查样本总数
        num_samples_bytes = txn.get('num_samples'.encode('ascii'))
        if num_samples_bytes:
            num_samples = int(num_samples_bytes.decode())
            print(f"✅ 成功找到 'num_samples' 键，值为: {num_samples}")
        else:
            print("❌ 未找到 'num_samples' 键。")

        # 2. 检查统计数据 (在旧代码中可能以'__stats__'存在)
        stats_bytes = txn.get('__stats__'.encode('ascii'))
        if stats_bytes:
            try:
                stats_data = pickle.loads(stats_bytes)
                print("✅ 成功找到并反序列化 '__stats__' 键。")
                # print("  统计数据内容:")
                # print(yaml.dump(stats_data, indent=2))
            except pickle.UnpicklingError as e:
                print(f"❌ 找到 '__stats__' 键，但反序列化失败: {e}")
        else:
            print("❌ 未找到 '__stats__' 键。")

    env.close()
    print("\n--- 检查完成 ---")

if __name__ == "__main__":
    # 使用配置文件中的路径
    try:
        with open('configs/main_config.yaml', 'r') as f: # Changed config.yaml to main_config.yaml
            config = yaml.safe_load(f)
        lmdb_path = config['data_preprocessing']['output_path']
        check_lmdb_metadata(lmdb_path)
    except FileNotFoundError:
        print("错误: `configs/main_config.yaml` 未找到。请在项目根目录运行此脚本。") # Changed config.yaml to main_config.yaml
    except KeyError:
        print("错误: 无法在 `configs/main_config.yaml` 中找到 'output_path'。") # Changed config.yaml to main_config.yaml 
 
 
 
 
 
 