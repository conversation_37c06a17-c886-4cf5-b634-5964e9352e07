"""
创建时间: 2023-11-19
功能: 读取并分析地表覆盖GeoTIFF文件，找出其中所有唯一的像素值，以确定地表覆盖的类别。
输入:
  - `environment/landcover_aligned.tif` (或其他GeoTIFF文件，通过 `tif_path` 参数传入)。
输出: 打印地表覆盖文件中所有唯一的像素值（地表覆盖类别）及其总数。
原理:
  - 使用 `rasterio` 库打开GeoTIFF文件。
  - 如果栅格数据已分块，则通过迭代 `src.block_windows` 逐块读取数据，以避免一次性加载大文件造成内存溢出。
  - 如果栅格数据未分块，则一次性读取整个图像。
  - 使用 `numpy.unique` 函数从读取的数据中高效地提取所有唯一的像素值。
  - 使用集合（set）来存储唯一值，提高查找效率。
处理方法:
  - 接受一个GeoTIFF文件路径作为输入。
  - 根据文件是否分块，选择合适的读取策略（整图读取或逐块读取）。
  - 遍历图像数据，收集所有唯一的像素值。
  - 对收集到的唯一值进行排序并打印，提供清晰的地表覆盖类别列表和总数。
  - 包含错误处理机制，捕获文件I/O错误或其它异常，并向用户报告。
"""
import rasterio
import numpy as np
from tqdm import tqdm

def find_unique_landcover_values(tif_path):
    """
    读取一个GeoTIFF文件，并找出其中所有唯一的像素值，以确定地表覆盖的类别。
    """
    print(f"--- 正在分析地表覆盖文件: {tif_path} ---")
    try:
        with rasterio.open(tif_path) as src:
            # 检查栅格数据是否分块
            if not src.is_tiled:
                print("栅格未分块，将一次性读取整个图像，这可能会花费一些时间...")
                data = src.read(1)
                unique_values = np.unique(data)
            else:
                print("栅格已分块，将逐块处理...")
                # 使用集合来高效存储唯一值
                unique_values = set()
                # 迭代处理每个数据块
                block_windows = list(src.block_windows(1))
                for ji, window in tqdm(block_windows, desc="扫描数据块", total=len(block_windows)):
                    data = src.read(1, window=window)
                    unique_values.update(np.unique(data).tolist())
            
            sorted_unique_values = sorted(list(unique_values))
            print("\n--- 分析完成 ---")
            print(f"共找到 {len(sorted_unique_values)} 个唯一的地表覆盖类别。")
            print("所有唯一值如下:")
            print(sorted_unique_values)

    except rasterio.errors.RasterioIOError as e:
        print(f"错误: 无法打开或读取文件: {tif_path}。")
        print(f"详情: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    landcover_file = "environment/landcover_aligned.tif"
    find_unique_landcover_values(landcover_file) 
 
 
 
 
 
 
import numpy as np
from tqdm import tqdm

def find_unique_landcover_values(tif_path):
    """
    读取一个GeoTIFF文件，并找出其中所有唯一的像素值，以确定地表覆盖的类别。
    """
    print(f"--- 正在分析地表覆盖文件: {tif_path} ---")
    try:
        with rasterio.open(tif_path) as src:
            # 检查栅格数据是否分块
            if not src.is_tiled:
                print("栅格未分块，将一次性读取整个图像，这可能会花费一些时间...")
                data = src.read(1)
                unique_values = np.unique(data)
            else:
                print("栅格已分块，将逐块处理...")
                # 使用集合来高效存储唯一值
                unique_values = set()
                # 迭代处理每个数据块
                block_windows = list(src.block_windows(1))
                for ji, window in tqdm(block_windows, desc="扫描数据块", total=len(block_windows)):
                    data = src.read(1, window=window)
                    unique_values.update(np.unique(data).tolist())
            
            sorted_unique_values = sorted(list(unique_values))
            print("\n--- 分析完成 ---")
            print(f"共找到 {len(sorted_unique_values)} 个唯一的地表覆盖类别。")
            print("所有唯一值如下:")
            print(sorted_unique_values)

    except rasterio.errors.RasterioIOError as e:
        print(f"错误: 无法打开或读取文件: {tif_path}。")
        print(f"详情: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    landcover_file = "environment/landcover_aligned.tif"
    find_unique_landcover_values(landcover_file) 
 
 
 
 
 
 