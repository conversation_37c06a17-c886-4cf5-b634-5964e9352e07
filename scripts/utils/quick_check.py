#!/usr/bin/env python3
"""
创建时间: 2023-11-19
功能: 快速检查和验证数据预处理流水线的结果以及数据集的准备情况，确保所有关键组件（如网格配置、意图标签、轨迹坐标范围和归一化参数）都符合预期，从而确认系统是否已准备好进行模型训练。
输入:
  - `src/configs/v1_grid_classifier_config.py` 中定义的 `config` 对象：提供地图边界、网格尺寸等与意图网格分类相关的配置参数。
  - LMDB数据集：通过 `src.data.datasets.TrajectoryDataset` 加载，其中包含预处理后的轨迹和环境数据。
  - `data/preprocessed/normalization_stats.pkl`：预先计算并保存的轨迹特征归一化统计量（均值和标准差）。
输出:
  - 当前使用的意图网格配置的详细信息，包括地图边界、网格尺寸和总网格数。
  - 数据集中前20个样本的意图标签的唯一性及具体值，用于评估标签的多样性和分布。
  - 原始轨迹数据中的未来轨迹（`future_traj_gt`）和目标点（`dest_gt`）的坐标范围（最小值和最大值），以验证数据的数值范围。
  - 轨迹坐标的归一化均值和标准差，以确认归一化过程的正确性。
  - 最终的结论，指示意图分类问题是否已解决以及系统是否已为训练做好准备。
原理:
  - 直接导入并利用 `config` 对象来访问和显示预设的网格化参数。
  - 实例化 `TrajectoryDataset`，它会从指定的LMDB路径加载数据，并根据配置进行必要的变换。脚本通过遍历数据集的前几个样本来检查意图标签和坐标数据。
  - 通过 `pickle` 库加载预计算的归一化统计文件，直接获取并打印归一化参数。
  - 通过检查数据集样本中意图标签的唯一数量来判断意图分类是否成功修复，期望有多个独特标签。
  - 利用NumPy的 `min()` 和 `max()` 方法检查坐标张量的范围，确保数据在合理区间内。
处理方法:
  - 脚本设计为独立运行，无需命令行参数，直接执行即可。
  - 使用 `print` 语句提供清晰的分步检查报告，便于开发者快速定位问题。
  - 通过 `numpy.set_printoptions` 优化数值输出的精度和可读性。
  - 提供了明确的“成功”或“失败”提示，基于对意图标签多样性的检查结果。
  - 本脚本作为开发过程中的一个快速验证点，旨在提供即时反馈，加速调试迭代。
"""
import torch
import pickle
from src.configs.v1_grid_classifier_config import config
from src.data.datasets import TrajectoryDataset

def quick_check():
    print("🚀 快速检查修复结果")
    print("="*50)
    
    # 1. 检查新的网格配置
    print(f"新配置:")
    print(f"  地图边界: {config.MAP_WORLD_BOUNDS}")
    print(f"  网格尺寸: {config.INTENT_GRID_SIZE}")
    print(f"  总网格数: {config.INTENT_GRID_SIZE[0] * config.INTENT_GRID_SIZE[1]}")
    
    # 2. 检查意图标签多样性
    train_dataset = TrajectoryDataset(config.LMDB_PATH, config)
    
    print(f"\n检查前20个样本的意图标签:")
    intent_labels = []
    for i in range(20):
        sample = train_dataset[i]
        intent_labels.append(sample['intent_label_gt'].item())
    
    unique_labels = set(intent_labels)
    print(f"独特标签数: {len(unique_labels)}")
    print(f"标签: {sorted(unique_labels)}")
    
    # 3. 检查坐标范围
    print(f"\n检查坐标范围:")
    sample = train_dataset[0]
    print(f"future_traj_gt坐标范围: [{sample['future_traj_gt'][:,:2].min():.3f}, {sample['future_traj_gt'][:,:2].max():.3f}]")
    print(f"dest_gt范围: [{sample['dest_gt'].min():.3f}, {sample['dest_gt'].max():.3f}]")
    
    # 4. 检查归一化参数
    with open('data/preprocessed/normalization_stats.pkl', 'rb') as f:
        stats = pickle.load(f)
    
    print(f"\n归一化参数:")
    print(f"坐标均值: {stats['mean'][:2]}")
    print(f"坐标标准差: {stats['std'][:2]}")
    
    if len(unique_labels) > 1:
        print(f"\n✅ 意图分类问题已修复！")
    else:
        print(f"\n❌ 意图分类问题未解决")
    
    print(f"\n🎯 系统已准备好进行训练！")

if __name__ == '__main__':
    quick_check() 