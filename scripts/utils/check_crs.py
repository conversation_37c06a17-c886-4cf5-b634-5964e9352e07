"""
创建时间: 2023-11-19
功能: 检查轨迹数据和环境数据的坐标参考系（CRS）是否匹配。
输入:
  - `environment/` 目录下的任意 `.tif` 环境地图文件。
  - `trajectories/` 目录下的任意 `.csv` 轨迹数据文件。
输出: 打印CRS检查结果，包括TIF文件CRS、CSV文件假定CRS，并给出CRS是否匹配的结论及修复建议。
原理:
  - 使用 `rasterio` 库读取TIF文件的CRS。
  - 使用 `pandas` 和 `geopandas` 库读取CSV轨迹数据，并假定其初始CRS为WGS84 (EPSG:4326)，然后转换为GeoDataFrame。
  - 比较两种数据的CRS是否一致。
处理方法:
  - 自动查找 `environment/` 和 `trajectories/` 目录下的第一个 `.tif` 和 `.csv` 文件。
  - 打开并解析TIF文件的CRS信息。
  - 读取CSV文件中的 'x' 和 'y' 坐标，构建地理点，并转换为带有CRS的GeoDataFrame。
  - 打印详细的CRS信息和CRS类型。
  - 如果CRS不匹配，提供详细的错误信息和修复建议，指导用户如何在数据预处理阶段统一CRS。
"""
import pandas as pd
import rasterio
import geopandas as gpd
from shapely.geometry import Point
import os

def check_coordinate_systems():
    """
    检查并报告轨迹数据和环境数据的坐标参考系（CRS）。
    """
    print("开始检查坐标参考系...")

    # --- 1. 确定文件路径 ---
    try:
        # 选择一个TIF文件
        tif_dir = 'environment'
        tif_file = next((f for f in os.listdir(tif_dir) if f.endswith('.tif')), None)
        if not tif_file:
            print("错误：在 'environment' 目录下未找到TIF文件。")
            return
        tif_path = os.path.join(tif_dir, tif_file)
        print(f"  - 使用环境地图: {tif_path}")

        # 选择一个CSV文件
        csv_dir = 'trajectories'
        csv_file = next((f for f in os.listdir(csv_dir) if f.endswith('.csv')), None)
        if not csv_file:
            print("错误：在 'trajectories' 目录下未找到CSV文件。")
            return
        csv_path = os.path.join(csv_dir, csv_file)
        print(f"  - 使用轨迹文件: {csv_path}")

    except FileNotFoundError as e:
        print(f"错误：找不到数据目录 - {e}。请确保 'environment' 和 'trajectories' 目录存在于项目根目录。")
        return

    # --- 2. 读取并分析TIF文件的CRS ---
    try:
        with rasterio.open(tif_path) as src:
            tif_crs = src.crs
            print(f"\n[环境数据] TIF文件的坐标参考系 (CRS):")
            print(f"  - {tif_crs}")
            if tif_crs.is_projected:
                print("  - 类型: 投影坐标系")
            elif tif_crs.is_geographic:
                print("  - 类型: 地理坐标系")
    except Exception as e:
        print(f"\n错误：读取TIF文件 '{tif_path}' 时发生错误: {e}")
        return

    # --- 3. 读取并分析CSV文件的CRS ---
    try:
        # 使用pandas读取CSV
        df = pd.read_csv(csv_path)

        # 检查是否存在 'x' 和 'y' 列
        if 'x' not in df.columns or 'y' not in df.columns:
            print(f"\n错误：CSV文件 '{csv_path}' 中未找到 'x' 和 'y' 坐标列。")
            print(f"  - 实际列名: {df.columns.tolist()}")
            return

        # 假设CSV中的坐标是WGS84 (EPSG:4326)，这是GPS的标准格式
        # 将其转换为GeoDataFrame
        geometry = [Point(xy) for xy in zip(df['x'], df['y'])]
        gdf = gpd.GeoDataFrame(df, geometry=geometry, crs="EPSG:4326")
        csv_crs = gdf.crs
        
        print(f"\n[轨迹数据] CSV文件假定的坐标参考系 (CRS):")
        print(f"  - {csv_crs} (WGS84)")
        if csv_crs.is_projected:
            print("  - 类型: 投影坐标系")
        elif csv_crs.is_geographic:
            print("  - 类型: 地理坐标系")

    except Exception as e:
        print(f"\n错误：处理CSV文件 '{csv_path}' 时发生错误: {e}")
        print("  - 请确保CSV文件格式正确。")
        return

    # --- 4. 比较CRS并给出结论 ---
    print("\n---[ 结论 ]---")
    if tif_crs == csv_crs:
        print("✅ 坐标参考系一致。问题可能出在其他地方。")
    else:
        print("❌ 坐标参考系不匹配！这是导致预处理失败的根本原因。")
        print("  - TIF CRS: ", tif_crs)
        print("  - CSV CRS (假定): ", csv_crs)
        print("\n---[ 修复建议 ]---")
        print("1. 确认轨迹数据的真实CRS：'x'和'y'列名表明它很可能是一个投影坐标系，而不是WGS84。请与数据提供方确认其EPSG代码。")
        print("2. 统一坐标系：在数据预处理脚本 (`src/data_preprocessing/preprocess_to_lmdb.py`) 中，")
        print("   必须将在CSV中读取的坐标点转换到与TIF文件相同的CRS (`{tif_crs}`)。")
        print("   首先用真实的CRS创建GeoDataFrame，然后使用 `to_crs()` 方法进行转换。")
        print("   例如: `gdf = gpd.GeoDataFrame(df, geometry=geometry, crs='EPSG:XXXXX').to_crs('{tif_crs}')`")

if __name__ == '__main__':
    check_coordinate_systems() 
 
 
 
 
 
 
import pandas as pd
import rasterio
import geopandas as gpd
from shapely.geometry import Point
import os

def check_coordinate_systems():
    """
    检查并报告轨迹数据和环境数据的坐标参考系（CRS）。
    """
    print("开始检查坐标参考系...")

    # --- 1. 确定文件路径 ---
    try:
        # 选择一个TIF文件
        tif_dir = 'environment'
        tif_file = next((f for f in os.listdir(tif_dir) if f.endswith('.tif')), None)
        if not tif_file:
            print("错误：在 'environment' 目录下未找到TIF文件。")
            return
        tif_path = os.path.join(tif_dir, tif_file)
        print(f"  - 使用环境地图: {tif_path}")

        # 选择一个CSV文件
        csv_dir = 'trajectories'
        csv_file = next((f for f in os.listdir(csv_dir) if f.endswith('.csv')), None)
        if not csv_file:
            print("错误：在 'trajectories' 目录下未找到CSV文件。")
            return
        csv_path = os.path.join(csv_dir, csv_file)
        print(f"  - 使用轨迹文件: {csv_path}")

    except FileNotFoundError as e:
        print(f"错误：找不到数据目录 - {e}。请确保 'environment' 和 'trajectories' 目录存在于项目根目录。")
        return

    # --- 2. 读取并分析TIF文件的CRS ---
    try:
        with rasterio.open(tif_path) as src:
            tif_crs = src.crs
            print(f"\n[环境数据] TIF文件的坐标参考系 (CRS):")
            print(f"  - {tif_crs}")
            if tif_crs.is_projected:
                print("  - 类型: 投影坐标系")
            elif tif_crs.is_geographic:
                print("  - 类型: 地理坐标系")
    except Exception as e:
        print(f"\n错误：读取TIF文件 '{tif_path}' 时发生错误: {e}")
        return

    # --- 3. 读取并分析CSV文件的CRS ---
    try:
        # 使用pandas读取CSV
        df = pd.read_csv(csv_path)

        # 检查是否存在 'x' 和 'y' 列
        if 'x' not in df.columns or 'y' not in df.columns:
            print(f"\n错误：CSV文件 '{csv_path}' 中未找到 'x' 和 'y' 坐标列。")
            print(f"  - 实际列名: {df.columns.tolist()}")
            return

        # 假设CSV中的坐标是WGS84 (EPSG:4326)，这是GPS的标准格式
        # 将其转换为GeoDataFrame
        geometry = [Point(xy) for xy in zip(df['x'], df['y'])]
        gdf = gpd.GeoDataFrame(df, geometry=geometry, crs="EPSG:4326")
        csv_crs = gdf.crs
        
        print(f"\n[轨迹数据] CSV文件假定的坐标参考系 (CRS):")
        print(f"  - {csv_crs} (WGS84)")
        if csv_crs.is_projected:
            print("  - 类型: 投影坐标系")
        elif csv_crs.is_geographic:
            print("  - 类型: 地理坐标系")

    except Exception as e:
        print(f"\n错误：处理CSV文件 '{csv_path}' 时发生错误: {e}")
        print("  - 请确保CSV文件格式正确。")
        return

    # --- 4. 比较CRS并给出结论 ---
    print("\n---[ 结论 ]---")
    if tif_crs == csv_crs:
        print("✅ 坐标参考系一致。问题可能出在其他地方。")
    else:
        print("❌ 坐标参考系不匹配！这是导致预处理失败的根本原因。")
        print("  - TIF CRS: ", tif_crs)
        print("  - CSV CRS (假定): ", csv_crs)
        print("\n---[ 修复建议 ]---")
        print("1. 确认轨迹数据的真实CRS：'x'和'y'列名表明它很可能是一个投影坐标系，而不是WGS84。请与数据提供方确认其EPSG代码。")
        print("2. 统一坐标系：在数据预处理脚本 (`src/data_preprocessing/preprocess_to_lmdb.py`) 中，")
        print("   必须将在CSV中读取的坐标点转换到与TIF文件相同的CRS (`{tif_crs}`)。")
        print("   首先用真实的CRS创建GeoDataFrame，然后使用 `to_crs()` 方法进行转换。")
        print("   例如: `gdf = gpd.GeoDataFrame(df, geometry=geometry, crs='EPSG:XXXXX').to_crs('{tif_crs}')`")

if __name__ == '__main__':
    check_coordinate_systems() 
 
 
 
 
 
 