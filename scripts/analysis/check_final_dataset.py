#!/usr/bin/env python3
"""
检查最终生成的数据集结构和内容
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pickle
import lmdb
import logging

from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_lmdb_dataset():
    """检查LMDB数据集的详细信息"""
    logger.info("=== 检查LMDB数据集 ===")
    
    config = load_config('configs/main_config.yaml')
    data_path = config.data_preprocessing.output_path
    
    # 检查训练集
    train_lmdb_path = f"{data_path}/train"
    val_lmdb_path = f"{data_path}/val"
    
    for split_name, lmdb_path in [("训练集", train_lmdb_path), ("验证集", val_lmdb_path)]:
        if not os.path.exists(lmdb_path):
            logger.error(f"LMDB路径不存在: {lmdb_path}")
            continue
        
        logger.info(f"\n--- {split_name} ---")
        
        # 打开LMDB
        env = lmdb.open(lmdb_path, readonly=True, lock=False)
        
        # 获取样本数量
        with env.begin() as txn:
            cursor = txn.cursor()
            sample_count = 0
            sample_keys = []
            
            for key, _ in cursor:
                try:
                    key_int = int(key.decode())
                    sample_keys.append(key_int)
                    sample_count += 1
                except ValueError:
                    continue
        
        logger.info(f"{split_name}样本数量: {sample_count}")
        
        if sample_count == 0:
            logger.warning(f"{split_name}中没有样本数据")
            env.close()
            continue
        
        # 检查前几个样本的详细信息
        sample_keys.sort()
        num_check = min(3, len(sample_keys))
        
        for i in range(num_check):
            key = sample_keys[i]
            with env.begin() as txn:
                sample_data = txn.get(f'{key}'.encode())
                if sample_data is None:
                    continue
                
                sample = pickle.loads(sample_data)
                
                logger.info(f"\n样本 {key}:")
                logger.info(f"  文件路径: {sample.get('file_path', 'N/A')}")
                
                # 检查各个数据项的形状
                for data_key, data_value in sample.items():
                    if isinstance(data_value, np.ndarray):
                        logger.info(f"  {data_key}: {data_value.shape} {data_value.dtype}")
                    elif data_key not in ['file_path']:
                        logger.info(f"  {data_key}: {type(data_value)} = {data_value}")
                
                # 详细检查历史特征
                if 'history_features' in sample:
                    history_features = sample['history_features']
                    logger.info(f"  历史特征详细信息:")
                    logger.info(f"    形状: {history_features.shape}")
                    logger.info(f"    数据类型: {history_features.dtype}")
                    logger.info(f"    数值范围: [{history_features.min():.4f}, {history_features.max():.4f}]")
                    logger.info(f"    前3个时间步的特征:")
                    for t in range(min(3, history_features.shape[0])):
                        logger.info(f"      时间步{t}: x={history_features[t,0]:.4f}, y={history_features[t,1]:.4f}, dt={history_features[t,2]:.4f}")
                
                # 详细检查历史掩码
                if 'history_mask' in sample:
                    history_mask = sample['history_mask']
                    logger.info(f"  历史掩码详细信息:")
                    logger.info(f"    形状: {history_mask.shape}")
                    logger.info(f"    有效观测点数: {np.sum(history_mask == 1.0)}/{len(history_mask)}")
                    logger.info(f"    掩码模式: {history_mask}")
                
                # 详细检查预测轨迹
                if 'ground_truth_trajectory' in sample:
                    pred_traj = sample['ground_truth_trajectory']
                    logger.info(f"  预测轨迹详细信息:")
                    logger.info(f"    形状: {pred_traj.shape}")
                    logger.info(f"    数值范围: [{pred_traj.min():.4f}, {pred_traj.max():.4f}]")
                    logger.info(f"    起点: ({pred_traj[0,0]:.4f}, {pred_traj[0,1]:.4f})")
                    logger.info(f"    终点: ({pred_traj[-1,0]:.4f}, {pred_traj[-1,1]:.4f})")
                
                # 检查聚合信息
                if 'aggregation_info' in sample:
                    agg_info = sample['aggregation_info']
                    logger.info(f"  聚合信息:")
                    for key, value in agg_info.items():
                        logger.info(f"    {key}: {value}")
        
        env.close()

def check_normalization_stats():
    """检查归一化统计数据"""
    logger.info("\n=== 检查归一化统计数据 ===")
    
    config = load_config('configs/main_config.yaml')
    stats_path = f"{config.data_preprocessing.output_path}/normalization_stats.pkl"
    
    if not os.path.exists(stats_path):
        logger.error(f"归一化统计文件不存在: {stats_path}")
        return
    
    with open(stats_path, 'rb') as f:
        stats = pickle.load(f)
    
    logger.info("归一化统计数据:")
    for key, value in stats.items():
        if isinstance(value, dict):
            logger.info(f"  {key}:")
            for sub_key, sub_value in value.items():
                logger.info(f"    {sub_key}: {sub_value}")
        else:
            logger.info(f"  {key}: {value}")

def visualize_sample_data():
    """可视化样本数据"""
    logger.info("\n=== 可视化样本数据 ===")
    
    config = load_config('configs/main_config.yaml')
    data_path = config.data_preprocessing.output_path
    train_lmdb_path = f"{data_path}/train"
    
    if not os.path.exists(train_lmdb_path):
        logger.error(f"训练集LMDB不存在: {train_lmdb_path}")
        return
    
    # 打开LMDB并获取一个样本
    env = lmdb.open(train_lmdb_path, readonly=True, lock=False)
    
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        key, value = cursor.item()
        sample = pickle.loads(value)
    
    env.close()
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 历史轨迹和掩码
    ax1 = axes[0, 0]
    history_features = sample['history_features']
    history_mask = sample['history_mask']
    
    # 绘制完整历史轨迹
    ax1.plot(history_features[:, 0], history_features[:, 1], 'b-', alpha=0.5, label='完整历史')
    
    # 突出显示观测期
    observed_indices = np.where(history_mask == 1.0)[0]
    if len(observed_indices) > 0:
        ax1.scatter(history_features[observed_indices, 0], history_features[observed_indices, 1], 
                   c='red', s=50, alpha=0.8, label='观测期', zorder=5)
    
    ax1.set_title('历史轨迹和观测掩码')
    ax1.set_xlabel('X坐标 (归一化)')
    ax1.set_ylabel('Y坐标 (归一化)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # 2. 预测轨迹
    ax2 = axes[0, 1]
    pred_traj = sample['ground_truth_trajectory']
    ax2.plot(pred_traj[:, 0], pred_traj[:, 1], 'g-', linewidth=2, label='预测轨迹')
    ax2.scatter(pred_traj[0, 0], pred_traj[0, 1], c='green', s=100, marker='o', label='起点', zorder=5)
    ax2.scatter(pred_traj[-1, 0], pred_traj[-1, 1], c='red', s=100, marker='s', label='终点', zorder=5)
    
    ax2.set_title('预测轨迹')
    ax2.set_xlabel('X坐标 (归一化)')
    ax2.set_ylabel('Y坐标 (归一化)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')
    
    # 3. 时间间隔分布
    ax3 = axes[1, 0]
    delta_t = history_features[:, 2]
    ax3.hist(delta_t, bins=20, alpha=0.7, color='blue')
    ax3.set_title('时间间隔分布')
    ax3.set_xlabel('Delta_t (归一化)')
    ax3.set_ylabel('频次')
    ax3.grid(True, alpha=0.3)
    
    # 4. 掩码模式
    ax4 = axes[1, 1]
    time_steps = np.arange(len(history_mask))
    ax4.bar(time_steps, history_mask, alpha=0.7, color=['red' if m == 1.0 else 'blue' for m in history_mask])
    ax4.set_title('观测/间歇掩码模式')
    ax4.set_xlabel('时间步')
    ax4.set_ylabel('掩码值 (1=观测, 0=间歇)')
    ax4.set_ylim(-0.1, 1.1)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('dataset_visualization.png', dpi=300, bbox_inches='tight')
    logger.info("数据集可视化已保存: dataset_visualization.png")
    plt.show()

def main():
    """主函数"""
    try:
        # 1. 检查LMDB数据集
        check_lmdb_dataset()
        
        # 2. 检查归一化统计
        check_normalization_stats()
        
        # 3. 可视化样本数据
        visualize_sample_data()
        
        logger.info("\n=== 数据集检查完成 ===")
        
    except Exception as e:
        logger.error(f"检查过程中出现错误: {e}", exc_info=True)

if __name__ == "__main__":
    main()
