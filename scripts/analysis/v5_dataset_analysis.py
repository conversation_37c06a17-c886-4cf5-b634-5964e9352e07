#!/usr/bin/env python3
"""
V5模型数据集深入分析与可视化
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import torch
import numpy as np
import matplotlib.pyplot as plt
import pickle
import lmdb
from torch.utils.data import DataLoader

from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_v5_dataset_directly():
    """直接从LMDB加载V5使用的数据集"""
    print("=== 直接加载V5数据集 ===")
    
    config = load_config('configs/main_config.yaml')
    data_path = config.data_preprocessing.output_path
    
    # 加载归一化统计数据
    stats_path = f"{data_path}/normalization_stats.pkl"
    if os.path.exists(stats_path):
        with open(stats_path, 'rb') as f:
            norm_stats = pickle.load(f)
        print("✅ 归一化统计数据加载成功")
    else:
        print("❌ 归一化统计数据不存在")
        norm_stats = None
    
    # 加载训练集
    train_path = f"{data_path}/train"
    val_path = f"{data_path}/val"
    
    train_samples = []
    val_samples = []
    
    # 加载训练集样本
    if os.path.exists(train_path):
        env = lmdb.open(train_path, readonly=True, lock=False)
        with env.begin() as txn:
            cursor = txn.cursor()
            for key, value in cursor:
                sample = pickle.loads(value)
                train_samples.append(sample)
        env.close()
        print(f"✅ 训练集加载完成: {len(train_samples)}个样本")
    
    # 加载验证集样本
    if os.path.exists(val_path):
        env = lmdb.open(val_path, readonly=True, lock=False)
        with env.begin() as txn:
            cursor = txn.cursor()
            for key, value in cursor:
                sample = pickle.loads(value)
                val_samples.append(sample)
        env.close()
        print(f"✅ 验证集加载完成: {len(val_samples)}个样本")
    
    return train_samples, val_samples, norm_stats

def analyze_sample_structure(samples, dataset_name):
    """分析样本结构"""
    print(f"\n=== {dataset_name}样本结构分析 ===")
    
    if not samples:
        print("❌ 样本列表为空")
        return
    
    sample = samples[0]
    print(f"样本数量: {len(samples)}")
    print(f"样本键: {list(sample.keys())}")
    
    # 分析各个字段
    for key, value in sample.items():
        if isinstance(value, np.ndarray):
            print(f"  {key}: {value.shape} {value.dtype}")
            print(f"    数值范围: [{value.min():.4f}, {value.max():.4f}]")
            if len(value.shape) <= 2:  # 只对低维数组显示统计
                print(f"    均值: {value.mean():.4f}, 标准差: {value.std():.4f}")
        else:
            print(f"  {key}: {type(value)} = {value}")
    
    # 检查是否有history_mask
    if 'history_mask' in sample:
        print("✅ 包含history_mask字段")
    else:
        print("❌ 缺少history_mask字段")
        # 尝试从其他字段推断
        if 'debug_full_history_mask' in sample:
            print("  但包含debug_full_history_mask字段")

def analyze_feature_distributions(samples, dataset_name):
    """分析特征分布"""
    print(f"\n=== {dataset_name}特征分布分析 ===")
    
    if not samples:
        return
    
    # 收集所有历史特征
    all_history_features = []
    all_trajectories = []
    all_destinations = []
    
    for sample in samples[:100]:  # 只分析前100个样本
        if 'history_features' in sample:
            all_history_features.append(sample['history_features'])
        if 'ground_truth_trajectory' in sample:
            all_trajectories.append(sample['ground_truth_trajectory'])
        if 'ground_truth_destination' in sample:
            all_destinations.append(sample['ground_truth_destination'])
    
    if all_history_features:
        # 合并所有特征
        combined_features = np.concatenate(all_history_features, axis=0)
        print(f"历史特征统计 (形状: {combined_features.shape}):")
        
        feature_names = ['x', 'y', 'Δt', 'v_n', 'v_e', 'cos_θ', 'sin_θ', 'a_x', 'a_y']
        for i in range(min(9, combined_features.shape[1])):
            feat_data = combined_features[:, i]
            non_zero_mask = feat_data != 0
            if np.any(non_zero_mask):
                non_zero_data = feat_data[non_zero_mask]
                print(f"  {feature_names[i]}: 非零值{len(non_zero_data)}个, "
                      f"范围[{non_zero_data.min():.4f}, {non_zero_data.max():.4f}], "
                      f"均值{non_zero_data.mean():.4f}")
            else:
                print(f"  {feature_names[i]}: 全为零")
    
    if all_trajectories:
        combined_trajectories = np.concatenate(all_trajectories, axis=0)
        print(f"预测轨迹统计 (形状: {combined_trajectories.shape}):")
        print(f"  X坐标: 范围[{combined_trajectories[:, 0].min():.4f}, {combined_trajectories[:, 0].max():.4f}]")
        print(f"  Y坐标: 范围[{combined_trajectories[:, 1].min():.4f}, {combined_trajectories[:, 1].max():.4f}]")
    
    return all_history_features, all_trajectories, all_destinations

def create_dataset_visualizations(train_samples, val_samples, norm_stats):
    """创建数据集可视化"""
    print(f"\n=== 创建数据集可视化 ===")
    
    # 1. 样本数量分布
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 样本数量
    axes[0, 0].bar(['训练集', '验证集'], [len(train_samples), len(val_samples)], 
                   color=['blue', 'orange'], alpha=0.7)
    axes[0, 0].set_title('数据集样本数量')
    axes[0, 0].set_ylabel('样本数')
    for i, v in enumerate([len(train_samples), len(val_samples)]):
        axes[0, 0].text(i, v + 10, str(v), ha='center', va='bottom')
    
    # 2. 特征维度分析
    if train_samples:
        sample = train_samples[0]
        if 'history_features' in sample:
            hist_shape = sample['history_features'].shape
            axes[0, 1].bar(['时间步', '特征维度'], [hist_shape[0], hist_shape[1]], 
                          color=['green', 'red'], alpha=0.7)
            axes[0, 1].set_title('历史特征维度')
            axes[0, 1].set_ylabel('数量')
            for i, v in enumerate([hist_shape[0], hist_shape[1]]):
                axes[0, 1].text(i, v + 5, str(v), ha='center', va='bottom')
        
        if 'ground_truth_trajectory' in sample:
            traj_shape = sample['ground_truth_trajectory'].shape
            axes[0, 2].bar(['预测时间步', '坐标维度'], [traj_shape[0], traj_shape[1]], 
                          color=['purple', 'brown'], alpha=0.7)
            axes[0, 2].set_title('预测轨迹维度')
            axes[0, 2].set_ylabel('数量')
            for i, v in enumerate([traj_shape[0], traj_shape[1]]):
                axes[0, 2].text(i, v + 2, str(v), ha='center', va='bottom')
    
    # 3. 特征分布可视化
    if train_samples and 'history_features' in train_samples[0]:
        # 收集前50个样本的特征
        features_sample = []
        for i, sample in enumerate(train_samples[:50]):
            if 'history_features' in sample:
                features_sample.append(sample['history_features'])
        
        if features_sample:
            combined_features = np.concatenate(features_sample, axis=0)
            
            # 位置特征分布
            x_data = combined_features[:, 0][combined_features[:, 0] != 0]
            y_data = combined_features[:, 1][combined_features[:, 1] != 0]
            
            if len(x_data) > 0 and len(y_data) > 0:
                axes[1, 0].hist2d(x_data, y_data, bins=50, alpha=0.7, cmap='Blues')
                axes[1, 0].set_title('位置特征分布 (X-Y)')
                axes[1, 0].set_xlabel('X坐标 (归一化)')
                axes[1, 0].set_ylabel('Y坐标 (归一化)')
            
            # 速度特征分布
            if combined_features.shape[1] >= 5:
                vel_n = combined_features[:, 3][combined_features[:, 3] != 0]
                vel_e = combined_features[:, 4][combined_features[:, 4] != 0]
                
                if len(vel_n) > 0:
                    axes[1, 1].hist(vel_n, bins=30, alpha=0.7, color='green', label='北向速度')
                if len(vel_e) > 0:
                    axes[1, 1].hist(vel_e, bins=30, alpha=0.7, color='red', label='东向速度')
                axes[1, 1].set_title('速度特征分布')
                axes[1, 1].set_xlabel('速度 (归一化)')
                axes[1, 1].set_ylabel('频次')
                axes[1, 1].legend()
            
            # 时间间隔分布
            if combined_features.shape[1] >= 3:
                delta_t = combined_features[:, 2][combined_features[:, 2] != 0]
                if len(delta_t) > 0:
                    axes[1, 2].hist(delta_t, bins=30, alpha=0.7, color='orange')
                    axes[1, 2].set_title('时间间隔分布')
                    axes[1, 2].set_xlabel('Δt (归一化)')
                    axes[1, 2].set_ylabel('频次')
    
    plt.tight_layout()
    plt.savefig('v5_dataset_analysis.png', dpi=300, bbox_inches='tight')
    print("✅ 数据集分析图已保存: v5_dataset_analysis.png")
    plt.show()

def visualize_sample_trajectories(samples, num_samples=6):
    """可视化样本轨迹"""
    print(f"\n=== 可视化样本轨迹 ===")
    
    if not samples:
        print("❌ 没有样本可视化")
        return
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i in range(min(num_samples, len(samples))):
        sample = samples[i]
        ax = axes[i]
        
        # 获取数据
        if 'history_features' in sample and 'ground_truth_trajectory' in sample:
            history_features = sample['history_features']
            ground_truth = sample['ground_truth_trajectory']
            
            # 提取位置信息
            hist_x = history_features[:, 0]
            hist_y = history_features[:, 1]
            
            # 找到非零的历史点
            valid_hist = (hist_x != 0) | (hist_y != 0)
            if np.any(valid_hist):
                valid_indices = np.where(valid_hist)[0]
                ax.plot(hist_x[valid_indices], hist_y[valid_indices], 'b-', 
                       linewidth=2, alpha=0.7, label='历史轨迹')
                ax.scatter(hist_x[valid_indices[-1]], hist_y[valid_indices[-1]], 
                          c='blue', s=100, marker='o', label='历史终点', zorder=5)
            
            # 绘制预测轨迹
            ax.plot(ground_truth[:, 0], ground_truth[:, 1], 'g-', 
                   linewidth=2, alpha=0.8, label='真实轨迹')
            ax.scatter(ground_truth[0, 0], ground_truth[0, 1], 
                      c='green', s=100, marker='s', label='预测起点', zorder=5)
            ax.scatter(ground_truth[-1, 0], ground_truth[-1, 1], 
                      c='red', s=100, marker='*', label='目标终点', zorder=5)
            
            ax.set_title(f'样本 {i+1}')
            ax.set_xlabel('X坐标 (归一化)')
            ax.set_ylabel('Y坐标 (归一化)')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            ax.axis('equal')
        else:
            ax.text(0.5, 0.5, '数据不完整', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(f'样本 {i+1} (无数据)')
    
    plt.tight_layout()
    plt.savefig('v5_sample_trajectories.png', dpi=300, bbox_inches='tight')
    print("✅ 样本轨迹可视化已保存: v5_sample_trajectories.png")
    plt.show()

def main():
    """主函数"""
    print("🔍 开始V5模型数据集深入分析")
    
    # 1. 加载数据集
    train_samples, val_samples, norm_stats = load_v5_dataset_directly()
    
    # 2. 分析样本结构
    analyze_sample_structure(train_samples, "训练集")
    analyze_sample_structure(val_samples, "验证集")
    
    # 3. 分析特征分布
    train_features, train_trajectories, train_destinations = analyze_feature_distributions(train_samples, "训练集")
    val_features, val_trajectories, val_destinations = analyze_feature_distributions(val_samples, "验证集")
    
    # 4. 创建数据集可视化
    create_dataset_visualizations(train_samples, val_samples, norm_stats)
    
    # 5. 可视化样本轨迹
    visualize_sample_trajectories(train_samples, num_samples=6)
    
    print("\n🎉 V5数据集分析完成！")
    print("生成的文件:")
    print("  - v5_dataset_analysis.png: 数据集统计分析")
    print("  - v5_sample_trajectories.png: 样本轨迹可视化")

if __name__ == "__main__":
    main()
