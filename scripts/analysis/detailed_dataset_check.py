#!/usr/bin/env python3
"""
详细检查数据集内容和特征维度
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pickle
import lmdb
import logging

from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def detailed_sample_analysis():
    """详细分析样本内容"""
    logger.info("=== 详细样本分析 ===")
    
    config = load_config('configs/main_config.yaml')
    data_path = config.data_preprocessing.output_path
    train_lmdb_path = f"{data_path}/train"
    
    if not os.path.exists(train_lmdb_path):
        logger.error(f"训练集LMDB不存在: {train_lmdb_path}")
        return
    
    # 打开LMDB
    env = lmdb.open(train_lmdb_path, readonly=True, lock=False)
    
    # 获取前几个样本进行详细分析
    with env.begin() as txn:
        cursor = txn.cursor()
        sample_count = 0
        
        for key, value in cursor:
            if sample_count >= 3:  # 只检查前3个样本
                break
                
            sample = pickle.loads(value)
            sample_count += 1
            
            logger.info(f"\n=== 样本 {sample_count} 详细信息 ===")
            logger.info(f"文件路径: {sample.get('file_path', 'N/A')}")
            logger.info(f"事件索引: {sample.get('event_idx', 'N/A')}")
            
            # 检查历史特征
            if 'history_features' in sample:
                hist_feat = sample['history_features']
                logger.info(f"历史特征形状: {hist_feat.shape}")
                logger.info(f"历史特征数据类型: {hist_feat.dtype}")
                logger.info(f"历史特征数值范围: [{hist_feat.min():.4f}, {hist_feat.max():.4f}]")
                
                # 检查每个维度的统计信息
                logger.info("各维度统计信息:")
                feature_names = ['x', 'y', 'delta_t', 'vel_n', 'vel_e', 'cos_heading', 'sin_heading', 'acc_x', 'acc_y']
                for i in range(min(hist_feat.shape[1], len(feature_names))):
                    feat_data = hist_feat[:, i]
                    non_zero_mask = feat_data != 0
                    if np.any(non_zero_mask):
                        non_zero_data = feat_data[non_zero_mask]
                        logger.info(f"  {feature_names[i]}: 非零值数量={len(non_zero_data)}, 范围=[{non_zero_data.min():.4f}, {non_zero_data.max():.4f}], 均值={non_zero_data.mean():.4f}")
                    else:
                        logger.info(f"  {feature_names[i]}: 全为零")
                
                # 显示前几个时间步的数据
                logger.info("前5个时间步的特征值:")
                for t in range(min(5, hist_feat.shape[0])):
                    if np.any(hist_feat[t] != 0):  # 只显示非零的时间步
                        feat_str = ", ".join([f"{feature_names[i] if i < len(feature_names) else f'feat_{i}'}={hist_feat[t,i]:.4f}" 
                                            for i in range(hist_feat.shape[1])])
                        logger.info(f"  时间步{t}: {feat_str}")
            
            # 检查历史掩码
            if 'history_mask' in sample:
                hist_mask = sample['history_mask']
                logger.info(f"历史掩码形状: {hist_mask.shape}")
                valid_count = np.sum(hist_mask == 1.0)
                logger.info(f"有效观测点数: {valid_count}/{len(hist_mask)}")
                logger.info(f"掩码模式: {hist_mask[:20]}...")  # 显示前20个值
            
            # 检查预测轨迹
            if 'ground_truth_trajectory' in sample:
                pred_traj = sample['ground_truth_trajectory']
                logger.info(f"预测轨迹形状: {pred_traj.shape}")
                logger.info(f"预测轨迹数值范围: [{pred_traj.min():.4f}, {pred_traj.max():.4f}]")
                logger.info(f"起点: ({pred_traj[0,0]:.4f}, {pred_traj[0,1]:.4f})")
                logger.info(f"终点: ({pred_traj[-1,0]:.4f}, {pred_traj[-1,1]:.4f})")
            
            # 检查目标位置
            if 'ground_truth_destination' in sample:
                dest = sample['ground_truth_destination']
                logger.info(f"目标位置: ({dest[0]:.4f}, {dest[1]:.4f})")
            
            # 检查环境ROI
            if 'environment_roi' in sample:
                env_roi = sample['environment_roi']
                logger.info(f"环境ROI形状: {env_roi.shape}")
                logger.info(f"环境ROI数值范围: [{env_roi.min():.4f}, {env_roi.max():.4f}]")
            
            # 检查聚合信息
            if 'aggregation_info' in sample:
                agg_info = sample['aggregation_info']
                logger.info("聚合信息:")
                for key, value in agg_info.items():
                    logger.info(f"  {key}: {value}")
    
    env.close()

def create_feature_visualization():
    """创建特征可视化"""
    logger.info("=== 创建特征可视化 ===")
    
    config = load_config('configs/main_config.yaml')
    data_path = config.data_preprocessing.output_path
    train_lmdb_path = f"{data_path}/train"
    
    if not os.path.exists(train_lmdb_path):
        logger.error(f"训练集LMDB不存在: {train_lmdb_path}")
        return
    
    # 打开LMDB并获取一个样本
    env = lmdb.open(train_lmdb_path, readonly=True, lock=False)
    
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        key, value = cursor.item()
        sample = pickle.loads(value)
    
    env.close()
    
    # 创建详细的特征可视化
    fig, axes = plt.subplots(3, 3, figsize=(18, 15))
    axes = axes.flatten()
    
    history_features = sample['history_features']
    history_mask = sample['history_mask']
    pred_trajectory = sample['ground_truth_trajectory']
    
    feature_names = ['X坐标', 'Y坐标', '时间间隔', '北向速度', '东向速度', 
                    'cos(航向)', 'sin(航向)', 'X加速度', 'Y加速度']
    
    # 绘制各个特征的时间序列
    for i in range(min(9, history_features.shape[1])):
        ax = axes[i]
        
        # 获取特征数据
        feature_data = history_features[:, i]
        time_steps = np.arange(len(feature_data))
        
        # 绘制完整特征
        ax.plot(time_steps, feature_data, 'b-', alpha=0.5, label='完整历史')
        
        # 突出显示观测期
        observed_indices = np.where(history_mask == 1.0)[0]
        if len(observed_indices) > 0:
            ax.scatter(observed_indices, feature_data[observed_indices], 
                      c='red', s=20, alpha=0.8, label='观测期', zorder=5)
        
        ax.set_title(f'{feature_names[i] if i < len(feature_names) else f"特征{i}"}')
        ax.set_xlabel('时间步')
        ax.set_ylabel('特征值')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('detailed_feature_visualization.png', dpi=300, bbox_inches='tight')
    logger.info("详细特征可视化已保存: detailed_feature_visualization.png")
    plt.show()

def check_time_windows():
    """检查时间窗口配置"""
    logger.info("=== 检查时间窗口配置 ===")
    
    config = load_config('configs/main_config.yaml')
    
    logger.info("配置参数:")
    logger.info(f"  聚合间隔: {config.data_preprocessing.agg_interval_s}秒")
    logger.info(f"  观测窗口: {config.data_preprocessing.observation_horizon_s}秒")
    logger.info(f"  预测窗口: {config.data_preprocessing.prediction_horizon_s}秒")
    
    # 计算聚合后的点数
    agg_interval = config.data_preprocessing.agg_interval_s
    obs_points = config.data_preprocessing.observation_horizon_s // agg_interval
    pred_points = config.data_preprocessing.prediction_horizon_s // agg_interval
    
    logger.info("聚合后的点数:")
    logger.info(f"  历史点数: {obs_points}个 ({config.data_preprocessing.observation_horizon_s}秒 ÷ {agg_interval}秒)")
    logger.info(f"  预测点数: {pred_points}个 ({config.data_preprocessing.prediction_horizon_s}秒 ÷ {agg_interval}秒)")
    
    # 检查模型配置是否匹配
    logger.info("模型配置:")
    logger.info(f"  max_history_len: {config.model.max_history_len}")
    logger.info(f"  prediction_horizon: {config.model.prediction_horizon}")
    
    if config.model.max_history_len == obs_points:
        logger.info("✅ 历史长度配置匹配")
    else:
        logger.warning(f"⚠️ 历史长度配置不匹配: 期望{obs_points}, 实际{config.model.max_history_len}")
    
    if config.model.prediction_horizon == pred_points:
        logger.info("✅ 预测长度配置匹配")
    else:
        logger.warning(f"⚠️ 预测长度配置不匹配: 期望{pred_points}, 实际{config.model.prediction_horizon}")

def main():
    """主函数"""
    try:
        # 1. 检查时间窗口配置
        check_time_windows()
        
        # 2. 详细样本分析
        detailed_sample_analysis()
        
        # 3. 创建特征可视化
        create_feature_visualization()
        
        logger.info("=== 详细数据集检查完成 ===")
        
    except Exception as e:
        logger.error(f"检查过程中出现错误: {e}", exc_info=True)

if __name__ == "__main__":
    main()
