#!/usr/bin/env python3
"""
V5模型工作测试 - 确认代码能正常运行
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import lmdb
import pickle
import os

from src.models.trajectory_predictor_v5 import TrajectoryPredictorV5
from src.utils.config_loader import load_config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def main():
    print("🚀 V5模型完整测试")
    
    # 1. 加载配置和模型
    config = load_config('configs/main_config.yaml')
    config.model.max_history_len = 360
    config.model.prediction_horizon = 120
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    model = TrajectoryPredictorV5(config).to(device)
    checkpoint = torch.load('models/trajectory_predictor_v5_best.pth', map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"✅ 模型加载成功 (轮次: {checkpoint.get('epoch', 'unknown')}, 损失: {checkpoint.get('val_loss', 'unknown'):.4f})")
    
    # 2. 加载数据并进行预测
    data_path = config.data_preprocessing.output_path + '/val'
    env = lmdb.open(data_path, readonly=True, lock=False)
    
    predictions = []
    ground_truths = []
    losses = []
    
    criterion = nn.MSELoss()
    
    with env.begin() as txn:
        cursor = txn.cursor()
        cursor.first()
        
        sample_count = 0
        for key, value in cursor:
            if sample_count >= 10:  # 测试10个样本
                break
                
            try:
                sample_data = pickle.loads(value)
                
                # 准备数据
                def ensure_tensor(data):
                    if isinstance(data, torch.Tensor):
                        return data.float()
                    elif isinstance(data, np.ndarray):
                        return torch.from_numpy(data).float()
                    else:
                        return torch.tensor(data).float()
                
                history_features = ensure_tensor(sample_data['history_features']).unsqueeze(0).to(device)
                history_mask = torch.ones(1, history_features.shape[1], dtype=torch.bool).to(device)
                ground_truth_trajectory = ensure_tensor(sample_data['ground_truth_trajectory']).unsqueeze(0).to(device)
                ground_truth_destination = ensure_tensor(sample_data['ground_truth_destination']).unsqueeze(0).to(device)
                environment_roi = ensure_tensor(sample_data['environment_roi']).unsqueeze(0).to(device)
                
                # 模型预测
                with torch.no_grad():
                    predicted_trajectory = model(
                        history_features=history_features,
                        history_mask=history_mask,
                        ground_truth_destination=ground_truth_destination,
                        environment_roi=environment_roi
                    )
                
                # 计算损失
                loss = criterion(predicted_trajectory, ground_truth_trajectory)
                losses.append(loss.item())
                
                # 保存结果
                pred_np = predicted_trajectory[0].cpu().numpy()
                gt_np = ground_truth_trajectory[0].cpu().numpy()
                
                predictions.append(pred_np)
                ground_truths.append(gt_np)
                
                sample_count += 1
                print(f"  样本 {sample_count}: 损失 = {loss.item():.4f}")
                
            except Exception as e:
                print(f"样本处理失败: {e}")
                continue
    
    env.close()
    
    # 3. 计算性能指标
    avg_loss = np.mean(losses)
    
    all_distances = []
    final_distances = []
    
    for pred, gt in zip(predictions, ground_truths):
        distances = np.sqrt(np.sum((pred - gt) ** 2, axis=1))
        all_distances.extend(distances)
        final_distances.append(distances[-1])
    
    ade = np.mean(all_distances)
    fde = np.mean(final_distances)
    
    print(f"\n📊 性能统计:")
    print(f"  测试样本数: {len(predictions)}")
    print(f"  平均MSE损失: {avg_loss:.4f}")
    print(f"  平均距离误差(ADE): {ade:.4f}")
    print(f"  最终距离误差(FDE): {fde:.4f}")
    
    # 4. 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 损失分布
    axes[0, 0].hist(losses, bins=8, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('损失分布')
    axes[0, 0].set_xlabel('MSE损失')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].axvline(avg_loss, color='red', linestyle='--', label=f'平均: {avg_loss:.4f}')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 距离误差分布
    axes[0, 1].hist(all_distances, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0, 1].set_title(f'距离误差分布 (ADE: {ade:.4f})')
    axes[0, 1].set_xlabel('距离误差')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].axvline(ade, color='red', linestyle='--', label=f'ADE: {ade:.4f}')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 最终距离误差
    axes[0, 2].hist(final_distances, bins=8, alpha=0.7, color='orange', edgecolor='black')
    axes[0, 2].set_title(f'最终距离误差 (FDE: {fde:.4f})')
    axes[0, 2].set_xlabel('最终距离误差')
    axes[0, 2].set_ylabel('频次')
    axes[0, 2].axvline(fde, color='red', linestyle='--', label=f'FDE: {fde:.4f}')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 轨迹可视化示例
    for i in range(3):
        ax = axes[1, i]
        if i < len(predictions):
            pred = predictions[i]
            gt = ground_truths[i]
            
            ax.plot(gt[:, 0], gt[:, 1], 'g-', linewidth=2, label='真实轨迹', alpha=0.8)
            ax.plot(pred[:, 0], pred[:, 1], 'r--', linewidth=2, label='预测轨迹', alpha=0.8)
            ax.scatter(gt[0, 0], gt[0, 1], c='green', s=100, marker='s', label='起点', zorder=5)
            ax.scatter(gt[-1, 0], gt[-1, 1], c='green', s=100, marker='*', label='真实终点', zorder=5)
            ax.scatter(pred[-1, 0], pred[-1, 1], c='red', s=100, marker='*', label='预测终点', zorder=5)
            
            final_error = np.sqrt(np.sum((pred[-1] - gt[-1]) ** 2))
            ax.set_title(f'轨迹示例 {i+1} (FDE: {final_error:.3f})')
            ax.set_xlabel('X坐标')
            ax.set_ylabel('Y坐标')
            if i == 0:
                ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            ax.axis('equal')
    
    plt.tight_layout()
    plt.savefig('v5_working_test_results.png', dpi=300, bbox_inches='tight')
    print(f"\n✅ 可视化已保存: v5_working_test_results.png")
    
    # 5. 保存详细结果
    results_summary = f"""
V5模型测试结果总结
==================

模型信息:
- 训练轮次: {checkpoint.get('epoch', 'unknown')}
- 验证损失: {checkpoint.get('val_loss', 'unknown'):.4f}
- 模型参数: {sum(p.numel() for p in model.parameters()):,}

测试结果:
- 测试样本数: {len(predictions)}
- 平均MSE损失: {avg_loss:.4f}
- 平均距离误差(ADE): {ade:.4f}
- 最终距离误差(FDE): {fde:.4f}
- 损失标准差: {np.std(losses):.4f}
- 距离误差标准差: {np.std(all_distances):.4f}

误差分布:
- 50%误差 < {np.percentile(all_distances, 50):.4f}
- 75%误差 < {np.percentile(all_distances, 75):.4f}
- 90%误差 < {np.percentile(all_distances, 90):.4f}
- 95%误差 < {np.percentile(all_distances, 95):.4f}
"""
    
    with open('v5_test_summary.txt', 'w', encoding='utf-8') as f:
        f.write(results_summary)
    
    print(results_summary)
    print("✅ 结果总结已保存: v5_test_summary.txt")
    print("\n🎉 V5模型完整测试成功完成!")

if __name__ == "__main__":
    main()
