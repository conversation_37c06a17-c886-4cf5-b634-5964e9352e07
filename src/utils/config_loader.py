# 创建时间: 2025-07-21 00:00:00+08:00
# 功能: 封装配置加载和更新逻辑。
# 输入: YAML配置文件路径。
# 输出: 合并后的配置字典。
# 原理: 使用PyYAML库加载YAML文件，并提供递归更新功能。
# 处理方法: 读取基础配置，如果提供覆盖配置，则递归合并。

import yaml
import os
from omegaconf import OmegaConf, MISSING # 导入 OmegaConf
from omegaconf import DictConfig # 导入 DictConfig
from pathlib import Path
from typing import Union

def load_config(*config_paths: str) -> DictConfig:
    """
    加载并合并多个YAML配置文件。
    第一个路径作为基础配置，后续路径的配置会合并到基础配置上。
    """
    base_config = OmegaConf.load(config_paths[0])

    for i in range(1, len(config_paths)):
        current_config = OmegaConf.load(config_paths[i])
        base_config = OmegaConf.merge(base_config, current_config)
    
    return base_config

def update_config(base_config: DictConfig, new_config: DictConfig):
    """
    递归地将 new_config 中的键值对合并到 base_config 中。
    如果键在两者中都存在，new_config 中的值会覆盖 base_config 中的值。
    """
    for key, value in new_config.items():
        if key in base_config:
            if isinstance(base_config[key], DictConfig) and isinstance(value, DictConfig):
                update_config(base_config[key], value)  # 递归合并嵌套的 DictConfig
            else:
                base_config[key] = value  # 覆盖现有值
        else:
            base_config[key] = value  # 添加新值

# 示例用法 (在实际项目中可能不会直接在这里调用，而是作为其他脚本的工具函数)
if __name__ == "__main__":
    # 创建一些示例配置文件
    config_a = {"model": {"name": "MyModel", "layers": 3}, "training": {"lr": 0.001}}
    config_b = {"model": {"layers": 5, "optimizer": "Adam"}, "data": {"path": "/data"}}
    config_c = {"training": {"batch_size": 32}, "system": {"device": "cuda"}}

    with open("config_a.yaml", "w") as f: yaml.dump(config_a, f)
    with open("config_b.yaml", "w") as f: yaml.dump(config_b, f)
    with open("config_c.yaml", "w") as f: yaml.dump(config_c, f)

    # 加载并合并
    merged_config = load_config("config_a.yaml", "config_b.yaml", "config_c.yaml")
    print("Merged Config:")
    print(OmegaConf.to_yaml(merged_config))

    # 清理示例文件
    os.remove("config_a.yaml")
    os.remove("config_b.yaml")
    os.remove("config_c.yaml") 