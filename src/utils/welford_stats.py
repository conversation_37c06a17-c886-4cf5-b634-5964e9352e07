# 创建时间: 2024-07-30
# 功能: 实现了Welford's算法，用于在线（online）和并行（parallel）计算数据的均值和标准差。
# 输入: 数据点或两个Welford聚合器。
# 输出: 更新的Welford聚合器或最终的均值和标准差。
# 原理: Welford's算法是一种数值稳定的在线计算方差的方法，它避免了在计算过程中出现大的中间平方和，从而减少了浮点误差。
# 处理方法: 提供了更新单个数据点、合并多个聚合器以及从聚合器中提取最终统计量的方法。

def update_welford_mean_std(existing_aggregate, newValue):
    """
    Welford's online algorithm for mean and std.
    existing_aggregate: (count, mean, M2) tuple or list.
    newValue: New data point.
    Returns updated (count, mean, M2).
    """
    (count, mean, M2) = existing_aggregate
    count += 1
    delta = newValue - mean
    mean += delta / count
    delta2 = newValue - mean
    M2 += delta * delta2
    return (count, mean, M2)

def combine_welford_mean_std(agg1, agg2):
    """
    Combines two Welford aggregates.
    agg1: (count1, mean1, M2_1)
    agg2: (count2, mean2, M2_2)
    Returns combined (count, mean, M2).
    """
    (count1, mean1, M2_1) = agg1
    (count2, mean2, M2_2) = agg2
    
    count = count1 + count2
    if count == 0: # Handle case where both aggregates are empty
        return (0, 0.0, 0.0)

    delta = mean2 - mean1
    mean = (count1 * mean1 + count2 * mean2) / count
    M2 = M2_1 + M2_2 + delta**2 * count1 * count2 / count
    return (count, mean, M2)

def finalize_welford_mean_std(existing_aggregate):
    """
    Finalizes the Welford aggregate to compute mean and std.
    existing_aggregate: (count, mean, M2)
    Returns (mean, std).
    """
    (count, mean, M2) = existing_aggregate
    if count < 1:
        return (float('nan'), float('nan'))
    elif count == 1:
        return (mean, 0.0) # Standard deviation is 0 for a single data point
    else:
        std = (M2 / (count - 1))**0.5
        return (mean, std) 