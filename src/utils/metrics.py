"""
轨迹预测评估指标
"""

import numpy as np

def calculate_ade(predicted_trajectories, ground_truth_trajectories):
    """
    计算平均位移误差 (Average Displacement Error)
    
    Args:
        predicted_trajectories: (batch_size, seq_len, 2) - 预测轨迹
        ground_truth_trajectories: (batch_size, seq_len, 2) - 真实轨迹
        
    Returns:
        float: 平均ADE
    """
    # 计算每个时间步的欧几里得距离
    distances = np.linalg.norm(predicted_trajectories - ground_truth_trajectories, axis=2)
    
    # 计算每个轨迹的平均距离，然后计算所有轨迹的平均值
    ade = np.mean(distances)
    
    return ade

def calculate_fde(predicted_trajectories, ground_truth_trajectories):
    """
    计算最终位移误差 (Final Displacement Error)
    
    Args:
        predicted_trajectories: (batch_size, seq_len, 2) - 预测轨迹
        ground_truth_trajectories: (batch_size, seq_len, 2) - 真实轨迹
        
    Returns:
        float: 平均FDE
    """
    # 计算最终时间步的欧几里得距离
    final_distances = np.linalg.norm(
        predicted_trajectories[:, -1, :] - ground_truth_trajectories[:, -1, :], 
        axis=1
    )
    
    # 计算所有轨迹的平均最终距离
    fde = np.mean(final_distances)
    
    return fde

def calculate_trajectory_metrics(predicted_trajectories, ground_truth_trajectories):
    """
    计算轨迹预测的所有指标
    
    Args:
        predicted_trajectories: (batch_size, seq_len, 2) - 预测轨迹
        ground_truth_trajectories: (batch_size, seq_len, 2) - 真实轨迹
        
    Returns:
        dict: 包含各种指标的字典
    """
    ade = calculate_ade(predicted_trajectories, ground_truth_trajectories)
    fde = calculate_fde(predicted_trajectories, ground_truth_trajectories)
    
    return {
        'ADE': ade,
        'FDE': fde
    }
