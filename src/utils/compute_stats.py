import numpy as np
import rasterio
from typing import <PERSON><PERSON>

def compute_raster_stats(raster_path: str) -> <PERSON><PERSON>[float, float]:
    """
    高效计算大型地理栅格文件的均值和标准差，忽略nodata值。
    
    Args:
        raster_path (str): 栅格文件路径.

    Returns:
        tuple[float, float]: (均值, 标准差).
    """
    with rasterio.open(raster_path) as src:
        nodata_value = src.nodata
        
        # 使用分块处理来优化内存使用
        # <PERSON><PERSON><PERSON>'s algorithm for online variance
        count = 0
        mean = 0.0
        m2 = 0.0

        for _, window in src.block_windows(1):
            block = src.read(1, window=window)
            
            if nodata_value is not None:
                # 过滤掉 nodata 值
                valid_pixels = block[block != nodata_value].flatten()
            else:
                valid_pixels = block.flatten()

            if valid_pixels.size == 0:
                continue

            for x in valid_pixels:
                count += 1
                delta = x - mean
                mean += delta / count
                m2 += delta * (x - mean)

    if count < 2:
        return (mean, 0.0) if count == 1 else (0.0, 0.0)

    variance = m2 / (count - 1)
    std_dev = np.sqrt(variance)
    
    return float(mean), float(std_dev) 
 
 
 
 
 
 