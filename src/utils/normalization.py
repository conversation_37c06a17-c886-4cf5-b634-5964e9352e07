'''
@Time    : 2024/07/29
<AUTHOR> AI Assistant
@File    : normalization.py
@Description : 包含数据加载、归一化和反归一化工具函数，用于处理轨迹和环境特征。
@用途 :
  本模块提供了一系列实用函数，旨在支持数据预处理和模型推理过程中对数值特征的标准化操作：
  - 加载预先计算好的归一化统计参数（均值和标准差）。
  - 对轨迹特征（如速度、加速度、曲率）进行Z-score归一化。
  - 对环境ROI（如DEM、坡度）的连续通道进行Z-score归一化，同时保留One-Hot编码的地表覆盖通道不变。
  - 将归一化后的轨迹数据反归一化回原始坐标。
@原理 :
  - `load_normalization_stats`: 读取 `pickle` 格式存储的归一化统计字典，并递归地将其中的Numpy数组转换为PyTorch张量，以便在模型中直接使用。
  - `normalize_features`: 对输入的Numpy特征数组应用Z-score标准化，即 `(features - mean) / std`，并处理标准差为零的情况。
  - `normalize_environment_roi`: 特别为环境ROI设计，它识别并归一化连续数值通道，而对One-Hot编码的地表覆盖通道保持原样，最后将归一化后的连续特征与原始One-Hot特征拼接。
  - `denormalize_trajectory`: 执行Z-score标准化的逆操作，将归一化后的轨迹坐标恢复到原始的地理坐标系。
@输入数据 :
  - `load_normalization_stats`: 归一化统计文件的路径（通常是`.pkl`文件）。
  - `denormalize_trajectory`: 归一化后的轨迹数组，以及轨迹X和Y坐标的均值和标准差。
  - `normalize_features`: 原始特征数组，以及每个特征对应的均值和标准差列表。
  - `normalize_environment_roi`: 原始环境ROI数组，以及DEM、坡度、坡向的均值和标准差，以及地表覆盖类别的数量。
@输出数据 :
  - `load_normalization_stats`: 包含均值和标准差的字典，其中Numpy数组已转换为PyTorch张量。
  - `denormalize_trajectory`: 反归一化后的原始轨迹数组。
  - `normalize_features`: 归一化后的特征数组。
  - `normalize_environment_roi`: 归一化后的环境ROI数组。
@处理方法 :
  这些函数是独立的工具，可以在数据加载、数据预处理管道的末端，以及模型推理前对输入数据进行即时归一化，确保数据格式和范围与模型的训练预期一致。
'''
import pickle
import os
import torch
import numpy as np

def _recursive_to_tensor(stats_dict):
    """
    递归地将字典中所有numpy数组转换为PyTorch张量。
    """
    for key, value in stats_dict.items():
        if isinstance(value, dict):
            # 如果值是字典，则递归调用
            _recursive_to_tensor(value)
        elif isinstance(value, np.ndarray):
            # 如果值是numpy数组，则进行转换
            stats_dict[key] = torch.from_numpy(value).float()
        elif isinstance(value, torch.Tensor):
            # 如果已经是Tensor，确保是float类型
            stats_dict[key] = value.float()
    return stats_dict

def load_normalization_stats(stats_path):
    """
    加载数据归一化所需的均值和标准差统计文件。
    支持嵌套字典结构。
    """
    if not os.path.exists(stats_path):
        raise FileNotFoundError(f"Normalization stats file not found at: {stats_path}")
    
    with open(stats_path, 'rb') as f:
        stats = pickle.load(f)
    
    # 使用递归函数处理可能嵌套的字典
    return _recursive_to_tensor(stats)

def denormalize_trajectory(normalized_traj, mean_x, std_x, mean_y, std_y):
    """
    将归一化后的轨迹反归一化为原始坐标（米）。
    Args:
        normalized_traj (np.ndarray): (T, 2) 的归一化轨迹。
        mean_x, std_x, mean_y, std_y (float): x和y坐标的均值和标准差。
    Returns:
        np.ndarray: (T, 2) 的原始轨迹。
    """
    # 确保输入是NumPy数组
    normalized_traj = np.asarray(normalized_traj)

    if normalized_traj.ndim == 1: # 处理 (2,) 形状的单个坐标点
        denormalized_traj = np.zeros_like(normalized_traj, dtype=np.float32)
        denormalized_traj[0] = normalized_traj[0] * std_x + mean_x
        denormalized_traj[1] = normalized_traj[1] * std_y + mean_y
    elif normalized_traj.ndim == 2 and normalized_traj.shape[1] == 2: # 处理 (T, 2) 形状的轨迹
        denormalized_traj = np.zeros_like(normalized_traj, dtype=np.float32)
        denormalized_traj[:, 0] = normalized_traj[:, 0] * std_x + mean_x
        denormalized_traj[:, 1] = normalized_traj[:, 1] * std_y + mean_y
    elif normalized_traj.ndim == 3 and normalized_traj.shape[2] == 2: # 新增：处理 (B, T, 2) 形状的轨迹
        batch_size, seq_len, _ = normalized_traj.shape
        denormalized_traj = np.zeros_like(normalized_traj, dtype=np.float32)
        denormalized_traj[:, :, 0] = normalized_traj[:, :, 0] * std_x + mean_x
        denormalized_traj[:, :, 1] = normalized_traj[:, :, 1] * std_y + mean_y
    else:
        raise ValueError(f"不支持的轨迹形状: {normalized_traj.shape}. 期望 (2,), (T, 2) 或 (B, T, 2).")

    return denormalized_traj

def normalize_features(features: np.ndarray, means: list, stds: list) -> np.ndarray:
    """
    对Numpy特征数组进行归一化。
    Args:
        features (np.ndarray): (T, F) 或 (F,) 的特征数组。
        means (list): 每个特征的均值列表，长度与特征维度F匹配。
        stds (list): 每个特征的标准差列表，长度与特征维度F匹配。
    Returns:
        np.ndarray: 归一化后的特征数组。
    """
    # 确保均值和标准差是numpy数组以便广播
    means_np = np.array(means, dtype=np.float32)
    stds_np = np.array(stds, dtype=np.float32)

    # 避免除以零
    stds_np[stds_np == 0] = 1.0
    
    return (features - means_np) / stds_np

def denormalize_by_stats(normalized_value: np.ndarray, mean: float, std: float) -> np.ndarray:
    """
    根据均值和标准差反归一化值。
    Args:
        normalized_value (np.ndarray): 归一化后的值数组。
        mean (float): 原始数据的均值。
        std (float): 原始数据的标准差。
    Returns:
        np.ndarray: 反归一化后的值数组。
    """
    # 避免除以零的标准差，如果std为0，则意味着所有值都相同，直接返回mean
    if std == 0:
        return np.full_like(normalized_value, mean)
    return (normalized_value * std) + mean



