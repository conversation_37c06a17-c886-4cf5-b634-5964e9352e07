import rasterio
import matplotlib.pyplot as plt
import numpy as np
import argparse
import os

def visualize_raster(file_path, output_path, title, cmap='gray'):
    """
    Visualizes a raster file and saves it as a PNG image.
    """
    # 在尝试导入和使用中文字体之前，确保系统已安装所需字体，
    # 例如：sudo apt-get install fonts-wqy-microhei
    # 并清除matplotlib缓存：rm -f ~/.cache/matplotlib/fontlist-v330.json
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei'] 
    plt.rcParams['axes.unicode_minus'] = False # 解决负号显示问题

    with rasterio.open(file_path) as src:
        data = src.read(1)
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 12))
        
        im = ax.imshow(data, cmap=cmap)
        
        ax.set_title(title, fontsize=22)
        ax.set_xlabel('X 像素坐标', fontsize=20)
        ax.set_ylabel('Y 像素坐标', fontsize=20)
        
        cbar = fig.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('数值 (1=平坦, 0=非平坦)', fontsize=20)
        
        plt.tight_layout()
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        plt.savefig(output_path, dpi=150)
        print(f"可视化图像已保存到: {output_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="栅格数据可视化工具")
    parser.add_argument('--input', type=str, required=True, help="输入的栅格文件路径 (例如 .tif)")
    parser.add_argument('--output', type=str, required=True, help="输出的PNG图像文件路径")
    parser.add_argument('--title', type=str, default="栅格数据可视化", help="图像标题")
    parser.add_argument('--cmap', type=str, default='gray', help="Matplotlib colormap")
    
    args = parser.parse_args()
    
    visualize_raster(args.input, args.output, args.title, args.cmap) 
 
 
 
 
 
 
import matplotlib.pyplot as plt
import numpy as np
import argparse
import os

def visualize_raster(file_path, output_path, title, cmap='gray'):
    """
    Visualizes a raster file and saves it as a PNG image.
    """
    # 在尝试导入和使用中文字体之前，确保系统已安装所需字体，
    # 例如：sudo apt-get install fonts-wqy-microhei
    # 并清除matplotlib缓存：rm -f ~/.cache/matplotlib/fontlist-v330.json
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei'] 
    plt.rcParams['axes.unicode_minus'] = False # 解决负号显示问题

    with rasterio.open(file_path) as src:
        data = src.read(1)
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 12))
        
        im = ax.imshow(data, cmap=cmap)
        
        ax.set_title(title, fontsize=22)
        ax.set_xlabel('X 像素坐标', fontsize=20)
        ax.set_ylabel('Y 像素坐标', fontsize=20)
        
        cbar = fig.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('数值 (1=平坦, 0=非平坦)', fontsize=20)
        
        plt.tight_layout()
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        plt.savefig(output_path, dpi=150)
        print(f"可视化图像已保存到: {output_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="栅格数据可视化工具")
    parser.add_argument('--input', type=str, required=True, help="输入的栅格文件路径 (例如 .tif)")
    parser.add_argument('--output', type=str, required=True, help="输出的PNG图像文件路径")
    parser.add_argument('--title', type=str, default="栅格数据可视化", help="图像标题")
    parser.add_argument('--cmap', type=str, default='gray', help="Matplotlib colormap")
    
    args = parser.parse_args()
    
    visualize_raster(args.input, args.output, args.title, args.cmap) 
 
 
 
 
 
 