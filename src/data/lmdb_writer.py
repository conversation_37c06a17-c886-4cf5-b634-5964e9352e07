import lmdb
import pickle
import logging

class LMDBWriter:
    def __init__(self, path: str, map_size: int, commit_batch_size: int = 1000):
        """
        初始化LMDB写入器。
        
        Args:
            path (str): LMDB数据库的路径。
            map_size (int): 数据库的最大容量（以字节为单位）。
            commit_batch_size (int): 每多少个样本提交一次事务。
    """
        self.path = path
        self.map_size = map_size
        self.commit_batch_size = commit_batch_size
        
        self.env = lmdb.open(self.path, map_size=self.map_size)
        self.txn = self.env.begin(write=True)
        
        self.samples_in_batch = 0
        self.total_samples_written = 0

    def add_sample(self, key: str, sample: dict):
        """
        向数据库中添加一个样本。
        """
        key_bytes = key.encode('ascii')
        value_bytes = pickle.dumps(sample)
        
        self.txn.put(key_bytes, value_bytes)
        
        self.samples_in_batch += 1
        self.total_samples_written += 1

        if self.samples_in_batch >= self.commit_batch_size:
            self.commit()

    def commit(self):
        """
        提交当前事务并开启一个新事务。
        """
        if self.txn:
            self.txn.commit()
            logging.info(f"LMDB事务提交，累计写入 {self.total_samples_written} 个样本。")
            self.txn = self.env.begin(write=True)
            self.samples_in_batch = 0
    
    def close(self):
        """
        提交所有剩余的更改并关闭数据库环境。
        """
        if self.samples_in_batch > 0:
            self.commit()
        
        self.env.close()
        logging.info("LMDB写入器已关闭。") 