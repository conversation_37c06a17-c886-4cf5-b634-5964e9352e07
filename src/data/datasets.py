# -*- coding: utf-8 -*-
"""
创建时间: 2025/07/18
功能: 定义了一个名为 `LMDBDataset` 的 PyTorch `Dataset` 类，用于高效地从LMDB数据库加载、预处理和组织轨迹数据样本。它为模型训练提供了数据接口，负责将存储在LMDB中的二进制数据反序列化，并转换为PyTorch张量。
输入:
  - `lmdb_path` (str): 存储预处理后数据的LMDB数据库路径（例如：`data/processed_lmdb_.../train` 或 `val`）。
输出:
  - `__getitem__` 方法返回一个字典，其中包含以下PyTorch张量：
    - `history`: 归一化后的历史轨迹特征序列 (形状: `(T_obs_agg, F_hist)`)。
    - `environment_roi`: 环境特征图序列 (形状: `(T_obs_agg, C_env, H_roi, W_roi)`)。
    - `ground_truth_destination`: 归一化后的真实目的地坐标 (形状: `(2,)`)。
    - `ground_truth_trajectory`: 归一化后的真实未来轨迹坐标序列 (形状: `(T_pred_agg, 2)`)。
    - `original_history_points`: 原始历史轨迹点序列。
原理及处理方法:
  - **LMDB环境初始化:** 在数据集初始化时，使用 `lmdb` 库以只读模式打开指定的LMDB数据库环境，并获取所有数据键的列表，从而确定数据集的总长度。
  - **数据样本获取 (`__getitem__`):** 当 `DataLoader` 请求一个样本时，该方法根据索引从LMDB中获取对应的序列化二进制数据。
  - **数据反序列化与转换:** 使用 `pickle.loads()` 将二进制数据反序列化为Python字典对象，然后将字典中包含的NumPy数组（如历史轨迹、环境ROI、真实目的地和未来轨迹）转换为PyTorch张量（`torch.from_numpy().float()`）。
  - **`collate_fn` 静态方法:** 提供一个可选的 `collate_fn`，用于在批处理时过滤掉任何可能为 `None` 的无效样本，然后使用 `default_collate` 将有效样本组合成批。
  - **日志记录:** 包含日志信息，用于指示数据集的初始化状态和样本数量。
"""
import torch
from torch.utils.data import Dataset, default_collate
import lmdb
import pickle
import numpy as np
import logging
import os

class LMDBDataset(Dataset):
    """
    用于从LMDB数据库加载轨迹和环境数据的PyTorch数据集类。
    LMDB中的数据样本已经包含了预处理和**归一化**后的数据（如轨迹和目的地）。
    环境ROI作为原始像素数据存储，**未进行Z-score归一化**。
    """
    def __init__(self, config, lmdb_type: str = 'train', return_mask: bool = False):
        """
        Args:
            config (omegaconf.dictconfig.DictConfig): 合并后的配置对象。
            lmdb_type (str): 要加载的LMDB数据集类型 ('train' 或 'val')。
            return_mask (bool): 是否返回 history_mask (为V3模型设计)。
        """
        self.config = config
        # 优先从模型专属配置的 data.path 中获取路径，如果不存在，则回退到主配置
        if 'data' in config and 'path' in config.data:
            base_path = config.data.path
        else:
            base_path = config.data_preprocessing.output_path
            
        self.lmdb_path = os.path.join(base_path, lmdb_type)
        self.return_mask = return_mask
        
        if not os.path.exists(self.lmdb_path):
            raise FileNotFoundError(f"错误：LMDB路径不存在: {self.lmdb_path}。请确认数据预处理是否已为 '{lmdb_type}' 集成功生成数据。")
        
        self.env = lmdb.open(self.lmdb_path, readonly=True, lock=False, readahead=False, meminit=False)
        with self.env.begin(write=False) as txn:
            # 优化：对于大的LMDB数据库，直接获取所有键可能耗费大量内存和时间。
            # 更好的做法是依赖于LMDB的内部机制，通过txn.cursor()进行迭代，或者在预处理时存储一个key_list。
            # 这里我们假设key的数量可以接受。
            self.keys = [key for key, _ in txn.cursor()]
            self.length = len(self.keys)
            
        logging.info(f"LMDBDataset 初始化: {self.lmdb_path} 包含 {self.length} 个样本。")

    def __len__(self):
        return self.length

    def __getitem__(self, idx):
        with self.env.begin(write=False) as txn:
            try:
                byteflow = txn.get(self.keys[idx])
                sample_data = pickle.loads(byteflow)

                # 处理可能已经是torch张量或numpy数组的数据
                def ensure_tensor(data):
                    if isinstance(data, torch.Tensor):
                        return data.float()
                    elif isinstance(data, np.ndarray):
                        return torch.from_numpy(data).float()
                    else:
                        return torch.tensor(data).float()

                # 动态生成history_mask（如果不存在）
                history_features = ensure_tensor(sample_data['history_features'])

                if 'history_mask' in sample_data:
                    # 如果数据中已有mask，直接使用
                    history_mask = ensure_tensor(sample_data['history_mask'])
                else:
                    # 动态生成mask：基于非零特征判断有效时间步
                    # 策略：如果某个时间步的任何特征非零，则认为该时间步有效
                    history_mask = torch.any(history_features != 0, dim=1).bool()

                sample = {
                    'history_features': history_features,
                    'history_mask': history_mask,
                    'ground_truth_trajectory': ensure_tensor(sample_data['ground_truth_trajectory']),
                    'ground_truth_destination': ensure_tensor(sample_data['ground_truth_destination']),
                    'original_history_points': sample_data['original_history_points'],  # 保持原始格式
                    'environment_roi': ensure_tensor(sample_data['environment_roi'])
                }
                return sample

            except Exception as e:
                logging.error(f"Error loading sample at index {idx}: {e}")
                # 返回None，让collate_fn处理
                return None

    @staticmethod
    def collate_fn(batch):
        """
        自定义的collate_fn来处理批次数据。
        过滤掉任何None的样本，并处理变长序列。
        """
        batch = [b for b in batch if b is not None]
        if not batch:
            return None

        # 对于V3模型，需要特殊处理变长序列
        try:
            return default_collate(batch)
        except RuntimeError as e:
            if "resize" in str(e) or "size" in str(e):
                # 处理变长序列的情况
                return LMDBDataset._collate_variable_length(batch)
            else:
                raise e

    @staticmethod
    def _collate_variable_length(batch):
        """
        处理变长序列的collate函数
        """
        import torch.nn.utils.rnn as rnn_utils

        # 分别处理每个字段
        collated = {}

        for key in batch[0].keys():
            if key == 'original_history_points':
                # 保持原始格式
                collated[key] = [sample[key] for sample in batch]
            elif key in ['history_features', 'history_mask', 'environment_roi']:
                # 对于序列数据，使用pad_sequence
                sequences = [sample[key] for sample in batch]
                if len(sequences[0].shape) > 1:
                    # 多维序列，需要特殊处理
                    max_len = max(seq.shape[0] for seq in sequences)
                    padded_sequences = []
                    for seq in sequences:
                        if seq.shape[0] < max_len:
                            # 创建填充
                            pad_shape = (max_len - seq.shape[0],) + seq.shape[1:]
                            pad = torch.zeros(pad_shape, dtype=seq.dtype)
                            padded_seq = torch.cat([seq, pad], dim=0)
                        else:
                            padded_seq = seq
                        padded_sequences.append(padded_seq)
                    collated[key] = torch.stack(padded_sequences)
                else:
                    # 一维序列
                    collated[key] = rnn_utils.pad_sequence(sequences, batch_first=True)
            else:
                # 对于固定长度的数据，使用默认collate
                collated[key] = default_collate([sample[key] for sample in batch])

        return collated
 
 
 
 
 
 