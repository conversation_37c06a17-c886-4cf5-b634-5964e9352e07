"""
创建时间: 2024-07-22
功能: 环境数据处理器，用于加载环境GIS数据（DEM、地表覆盖、坡度、坡向），并根据轨迹点提取指定大小的环境ROI（Region of Interest）。
输入:
  - 轨迹文件路径或手动边界框 (用于_load_super_window)
  - 环境GIS数据的路径字典 (包含dem, landcover, slope, aspect的tif文件路径)
  - 配置参数 (config字典)
  - 轨迹点的X, Y坐标 (用于get_environment_roi)
  - ROI的像素尺寸 (roi_size_pixels)
  - 环境超级窗口数据和其仿射变换 (super_window_transform)
输出:
  - _load_super_window: DEM、地表覆盖、坡度、坡向的超级窗口NumPy数组，以及超级窗口的仿射变换。
  - get_environment_roi: 多通道环境ROI张量 (C, H, W) 和原始地表覆盖ROI数组。
原理及处理方法:
  - _load_super_window:
    1. 根据轨迹的地理范围或手动提供的边界框，扩展一个带有额外边距的地理范围。
    2. 使用rasterio库打开并读取该地理范围内所有环境GIS图层的像素数据，形成"超级窗口"。
    3. 计算并返回超级窗口的仿射变换，用于后续坐标转换。
  - get_environment_roi:
    1. 将输入的轨迹点X, Y坐标转换为其在超级窗口中的像素行/列坐标。
    2. 根据roi_size_pixels计算裁剪区域的起始和结束像素索引。
    3. 从已加载的超级窗口中裁剪出对应位置的DEM、地表覆盖、坡度、坡向ROI。
    4. 对裁剪出的地表覆盖ROI进行One-Hot编码，使其成为多通道特征。
    5. 对坡向ROI进行正弦和余弦转换，以处理角度的周期性。
    6. 将所有环境特征（DEM, Slope, Aspect_sin, Aspect_cos, One-Hot Land Cover）堆叠成一个多通道的张量(C, H, W)，并返回原始地表覆盖ROI。
"""
import os
import time
import logging
import rasterio
from rasterio.windows import Window
import numpy as np
import pandas as pd
from typing import Tuple, Optional
from pathlib import Path

def _load_super_window(
    trajectory_file: Optional[str] = None,
    env_paths: dict = None,
    config: dict = None,
    margin_m: float = 0.0,
    debug: bool = False,
    manual_bbox: Optional[dict] = None
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, rasterio.Affine]:
    """
    根据单条轨迹的地理范围或手动提供的边界框，加载一个包含缓冲区（margin）的环境地图"超级窗口"。
    包含DEM, 地表覆盖, 坡度, 坡向。
    """
    if trajectory_file is None and manual_bbox is None:
        raise ValueError("必须提供 'trajectory_file' 或 'manual_bbox' 中的一个来加载超级窗口。")

    if manual_bbox:
        min_x, max_x = manual_bbox['min_x'], manual_bbox['max_x']
        min_y, max_y = manual_bbox['min_y'], manual_bbox['max_y']
    else:
        # 读取轨迹文件获取边界
        traj_df = pd.read_csv(trajectory_file)
        min_x, max_x = traj_df['x'].min(), traj_df['x'].max()
        min_y, max_y = traj_df['y'].min(), traj_df['y'].max()

        # 扩展边界以包含margin
        min_x -= margin_m
        max_x += margin_m
        min_y -= margin_m
        max_y += margin_m

    # dem_path = Path(config['data_preprocessing']['environment_path']) / config['data_preprocessing']['env_maps']['dem']
    # land_cover_path = Path(config['data_preprocessing']['environment_path']) / config['data_preprocessing']['env_maps']['landcover']
    # slope_path = Path(config['data_preprocessing']['environment_path']) / config['data_preprocessing']['env_maps']['slope']
    # aspect_path = Path(config['data_preprocessing']['environment_path']) / config['data_preprocessing']['env_maps']['aspect']

    # 使用 env_paths 字典中的完整路径
    dem_path = env_paths['dem']
    land_cover_path = env_paths['landcover']
    slope_path = env_paths['slope']
    aspect_path = env_paths['aspect']

    # 打开DEM文件以获取转换信息
    with rasterio.open(dem_path) as src:
        super_window_transform = src.transform
        # 根据扩展后的物理边界，计算需要读取的像素窗口
        rows_start, cols_start = src.index(min_x, max_y) # 左上角
        rows_end, cols_end = src.index(max_x, min_y) # 右下角

        # 确定读取窗口的行和列范围
        min_row, max_row = min(rows_start, rows_end), max(rows_start, rows_end)
        min_col, max_col = min(cols_start, cols_end), max(cols_start, cols_end)

        # 增加一个小的缓冲区以确保覆盖，并避免因浮点精度导致边缘裁剪问题
        buffer_pixels = 2 # 增加2像素的缓冲区
        min_row = max(0, min_row - buffer_pixels)
        max_row = min(src.height, max_row + buffer_pixels)
        min_col = max(0, min_col - buffer_pixels)
        max_col = min(src.width, max_col + buffer_pixels)
        
        # 确保窗口至少为1x1像素
        if max_row <= min_row: max_row = min_row + 1
        if max_col <= min_col: max_col = min_col + 1

        super_window_read_window = Window.from_slices((min_row, max_row), (min_col, max_col))
        
        # 从超级窗口的实际范围重新计算仿射变换
        super_window_transform = src.window_transform(super_window_read_window)

        dem_super_window = src.read(1, window=super_window_read_window)
    
    # 读取地表覆盖
    with rasterio.open(land_cover_path) as src:
        land_cover_super_window = src.read(1, window=super_window_read_window)

    # 读取坡度
    with rasterio.open(slope_path) as src:
        slope_super_window = src.read(1, window=super_window_read_window)

    # 读取坡向
    with rasterio.open(aspect_path) as src:
        aspect_super_window = src.read(1, window=super_window_read_window)
    
    if debug:
        # 调试信息中不再使用 trajectory_file.name，因为它可能为None
        source_info = f"manual_bbox={manual_bbox}" if manual_bbox else f"file={Path(trajectory_file).name}"
        logging.info(f"Loaded super window for {source_info} with shape: DEM {dem_super_window.shape}, LC {land_cover_super_window.shape}, Slope {slope_super_window.shape}, Aspect {aspect_super_window.shape}")
        logging.info(f"Super window transform: {super_window_transform}")
        logging.info(f"Super window extent: {min_x}, {min_y}, {max_x}, {max_y}")

    return dem_super_window, land_cover_super_window, slope_super_window, aspect_super_window, super_window_transform

def get_environment_roi(
    x_pos: float,
    y_pos: float,
    roi_size_pixels: int,
    dem_super_window: np.ndarray,
    land_cover_super_window: np.ndarray,
    slope_super_window: np.ndarray,
    aspect_super_window: np.ndarray,
    super_window_transform: rasterio.Affine,
    config: dict
) -> Optional[Tuple[np.ndarray, np.ndarray]]:
    # (这个函数在当前流程中未被直接使用，但为了完整性一并修正)
    row, col = rasterio.transform.rowcol(super_window_transform, x_pos, y_pos)

    half_roi = roi_size_pixels // 2
    row_start, row_end = row - half_roi, row + half_roi + (roi_size_pixels % 2)
    col_start, col_end = col - half_roi, col + half_roi + (roi_size_pixels % 2)

    dem_roi = dem_super_window[row_start:row_end, col_start:col_end]
    land_cover_roi = land_cover_super_window[row_start:row_end, col_start:col_end]
    slope_roi = slope_super_window[row_start:row_end, col_start:col_end]
    aspect_roi = aspect_super_window[row_start:row_end, col_start:col_end]
    
    if dem_roi.size == 0 or land_cover_roi.size == 0 or slope_roi.size == 0 or aspect_roi.size == 0:
        logging.warning(f"Warning: One or more ROI windows are empty for center ({x_pos}, {y_pos}). Skipping this ROI.")
        return None

    # --- 修正配置访问 ---
    landcover_classes = config.data_preprocessing.environment.landcover_classes
    num_classes = len(landcover_classes)
    land_cover_one_hot = np.zeros((land_cover_roi.shape[0], land_cover_roi.shape[1], num_classes), dtype=np.float32)
    for i, cls in enumerate(landcover_classes):
        land_cover_one_hot[land_cover_roi == cls, i] = 1.0
    land_cover_one_hot = land_cover_one_hot.transpose(2, 0, 1)

    aspect_sin_roi = np.sin(np.deg2rad(aspect_roi))
    aspect_cos_roi = np.cos(np.deg2rad(aspect_roi))

    env_roi_tensor = np.concatenate([
        np.expand_dims(dem_roi, axis=0),
        np.expand_dims(slope_roi, axis=0),
        np.expand_dims(aspect_sin_roi, axis=0),
        np.expand_dims(aspect_cos_roi, axis=0),
        land_cover_one_hot
    ], axis=0)

    return env_roi_tensor, land_cover_roi

def get_environment_roi_for_trajectory(
    trajectory_df: pd.DataFrame,
    env_paths: dict,
    config: dict,
    normalization_stats: dict  # 新增参数，接收完整的统计数据
) -> Optional[np.ndarray]:
    """为整个轨迹DataFrame中的每个点高效提取环境ROI，并进行归一化。"""
    
    # --- 修正配置访问 ---
    margin_m = config.data_preprocessing.environment.super_window_margin_m
    
    dem_super_window, lc_super_window, slope_super_window, aspect_super_window, super_window_transform = _load_super_window(
        manual_bbox={
            'min_x': trajectory_df['x'].min(), 'max_x': trajectory_df['x'].max(),
            'min_y': trajectory_df['y'].min(), 'max_y': trajectory_df['y'].max()
        },
        env_paths=env_paths,
        config=config,
        margin_m=margin_m
    )
    
    # --- 修正配置访问 ---
    roi_size_pixels = config.data_preprocessing.environment.roi_size_m // config.data_preprocessing.environment.grid_resolution_m
    landcover_classes = config.data_preprocessing.environment.landcover_classes
    num_land_cover_classes = len(landcover_classes)
    num_channels = 4 + num_land_cover_classes
    
    all_rois = np.zeros((len(trajectory_df), num_channels, roi_size_pixels, roi_size_pixels), dtype=np.float32)
    half_roi = roi_size_pixels // 2
    
    xs, ys = trajectory_df['x'].values, trajectory_df['y'].values
    rows, cols = rasterio.transform.rowcol(super_window_transform, xs, ys)
    
    for i in range(len(trajectory_df)):
        row, col = rows[i], cols[i]
        row_start, row_end = row - half_roi, row + half_roi + (roi_size_pixels % 2)
        col_start, col_end = col - half_roi, col + half_roi + (roi_size_pixels % 2)
        
        # 修改边界检查：允许部分ROI，但确保至少有一些重叠
        row_start_clipped = max(0, row_start)
        row_end_clipped = min(dem_super_window.shape[0], row_end)
        col_start_clipped = max(0, col_start)
        col_end_clipped = min(dem_super_window.shape[1], col_end)

        # 如果裁剪后的ROI太小，跳过
        if (row_end_clipped - row_start_clipped < roi_size_pixels // 2 or
            col_end_clipped - col_start_clipped < roi_size_pixels // 2):
            continue
        else:
            # 使用裁剪后的边界提取ROI
            dem_roi = dem_super_window[row_start_clipped:row_end_clipped, col_start_clipped:col_end_clipped]
            lc_roi = lc_super_window[row_start_clipped:row_end_clipped, col_start_clipped:col_end_clipped]
            slope_roi = slope_super_window[row_start_clipped:row_end_clipped, col_start_clipped:col_end_clipped]
            aspect_roi = aspect_super_window[row_start_clipped:row_end_clipped, col_start_clipped:col_end_clipped]

            # 如果ROI大小不是目标大小，需要填充或调整
            target_size = roi_size_pixels
            if dem_roi.shape != (target_size, target_size):
                # 创建目标大小的数组，用边界值填充
                dem_roi_padded = np.full((target_size, target_size), dem_roi.mean(), dtype=np.float32)
                lc_roi_padded = np.full((target_size, target_size), lc_roi.flatten()[0], dtype=lc_roi.dtype)
                slope_roi_padded = np.full((target_size, target_size), slope_roi.mean(), dtype=np.float32)
                aspect_roi_padded = np.full((target_size, target_size), aspect_roi.mean(), dtype=np.float32)

                # 计算在目标数组中的位置
                start_row = (target_size - dem_roi.shape[0]) // 2
                start_col = (target_size - dem_roi.shape[1]) // 2
                end_row = start_row + dem_roi.shape[0]
                end_col = start_col + dem_roi.shape[1]

                # 复制实际数据到中心位置
                dem_roi_padded[start_row:end_row, start_col:end_col] = dem_roi
                lc_roi_padded[start_row:end_row, start_col:end_col] = lc_roi
                slope_roi_padded[start_row:end_row, start_col:end_col] = slope_roi
                aspect_roi_padded[start_row:end_row, start_col:end_col] = aspect_roi

                # 使用填充后的ROI
                dem_roi = dem_roi_padded
                lc_roi = lc_roi_padded
                slope_roi = slope_roi_padded
                aspect_roi = aspect_roi_padded

            # --- 新增：对DEM和Slope进行Z-score归一化 ---
            dem_roi = (dem_roi - normalization_stats['dem_mean']) / (normalization_stats['dem_std'] + 1e-6)
            slope_roi = (slope_roi - normalization_stats['slope_mean']) / (normalization_stats['slope_std'] + 1e-6)

            lc_one_hot = np.zeros((lc_roi.shape[0], lc_roi.shape[1], num_land_cover_classes), dtype=np.float32)
            for j, cls in enumerate(landcover_classes):
                lc_one_hot[lc_roi == cls, j] = 1.0
            lc_one_hot = lc_one_hot.transpose(2, 0, 1)

            aspect_sin_roi = np.sin(np.deg2rad(aspect_roi))
            aspect_cos_roi = np.cos(np.deg2rad(aspect_roi))

            roi_tensor = np.concatenate([
                np.expand_dims(dem_roi, axis=0),
                np.expand_dims(slope_roi, axis=0),
                np.expand_dims(aspect_sin_roi, axis=0),
                np.expand_dims(aspect_cos_roi, axis=0),
                lc_one_hot
            ], axis=0)
            
            all_rois[i] = roi_tensor

    return all_rois 