"""
创建时间: 2024-07-22
功能: 轨迹数据处理器，用于计算轨迹的运动学特征，如速度、加速度、航向和曲率。
输入:
  - 包含'x'、'y'坐标和可选'timestamp_ms'列的Pandas DataFrame。
输出:
  - 包含新增运动学特征列的Pandas DataFrame。
原理及处理方法:
  1. 数据类型转换与缺失值处理：确保'x'、'y'列为数值类型，并使用前向/后向填充处理可能的缺失值。
  2. 速度计算：通过计算'x'和'y'坐标的一阶差分来获取x和y方向的速度分量。
  3. 加速度计算：通过计算速度分量的一阶差分来获取x和y方向的加速度分量。
  4. 航向计算：使用arctan2函数基于速度分量计算瞬时航向（弧度），并使用unwrap函数处理2π的周期性跳变，使其连续。
  5. 曲率计算：根据航向的变化率和瞬时速度计算曲率。为避免除以零，对速度接近零的情况进行特殊处理。
  6. 航向正弦和余弦分量：从航向计算正弦和余弦分量，以方便模型处理角度信息。
"""
import pandas as pd
import numpy as np

def calculate_kinematic_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算并添加运动学特征，如速度、加速度和航向。
    此函数不进行重采样，直接基于原始时间步长计算。
    """
    # 假设原始DataFrame的索引是时间戳，或者可以通过相邻行计算dt
    # 如果没有明确的 dt，则假设为单位时间步长。
    # 这里我们只考虑 diff() 操作，它隐式地假设等间隔时间步。
    
    # 确保在计算速度前，x和y是数值类型
    df['x'] = pd.to_numeric(df['x'], errors='coerce').fillna(method='ffill').fillna(method='bfill')
    df['y'] = pd.to_numeric(df['y'], errors='coerce').fillna(method='ffill').fillna(method='bfill')

    # 保留原始的时间戳列（如果存在）
    original_timestamp = None
    if 'timestamp_ms' in df.columns: # 将 'timestamp' 改为 'timestamp_ms'
        original_timestamp = df['timestamp_ms'].copy()

    # 速度
    df['velocity_x'] = df['x'].diff().fillna(0)
    df['velocity_y'] = df['y'].diff().fillna(0)

    # 加速度
    df['acceleration_x'] = df['velocity_x'].diff().fillna(0)
    df['acceleration_y'] = df['velocity_y'].diff().fillna(0)

    # 航向 (heading) - 注意这里计算的是点与点之间的瞬时航向，聚合时会重新计算窗口航向
    df['heading'] = np.arctan2(df['velocity_y'], df['velocity_x']).fillna(0)
    # 处理航向从 360 度到 0 度的突变，使其连续 (针对瞬时航向，对聚合航向影响不大)
    df['heading'] = np.unwrap(df['heading'])
    
    # 新增：计算航向的正弦和余弦分量
    df['heading_sin'] = np.sin(df['heading'])
    df['heading_cos'] = np.cos(df['heading'])

    # 曲率 (curvature) - 基于瞬时速度和航向变化率
    speed = np.sqrt(df['velocity_x']**2 + df['velocity_y']**2)
    # 避免除以零，将速度接近零的曲率设为零
    df['curvature'] = np.where(speed > 1e-6, df['heading'].diff().fillna(0) / speed, 0)

    # 重新添加原始的时间戳列
    if original_timestamp is not None:
        df['timestamp_ms'] = original_timestamp # 将 'timestamp' 改为 'timestamp_ms'

    return df 