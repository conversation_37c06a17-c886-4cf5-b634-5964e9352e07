import torch
from typing import List, Dict, Any

def custom_collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    自定义的collate_fn，用于处理包含可变长度张量的批次数据。
    
    对于'original_history_points'，它将张量保留在列表中，而不是尝试堆叠它们。
    对于所有其他键，它使用PyTorch的默认堆叠行为。
    """
    # 将可变长度的字段名提取出来
    variable_len_keys = ['original_history_points']
    
    # 初始化输出字典
    collated_batch = {}
    
    # 获取第一个样本的所有键
    keys = batch[0].keys()
    
    for key in keys:
        if key == 'environment_roi':
            # --- 手动处理 environment_roi ---
            rois = [d[key] for d in batch]
            # 假设每个roi的形状都是 (3600, 13, 9, 9)
            # 将它们在第0维拼接，然后重塑
            batch_size = len(batch)
            collated_batch[key] = torch.cat(rois, dim=0).view(batch_size, 3600, 13, 9, 9)
        elif key in variable_len_keys:
            # 对于可变长度的字段，直接将它们收集到一个列表中
            collated_batch[key] = [d[key] for d in batch]
        else:
            # 对于其他固定长度的字段，使用默认的堆叠方法
            collated_batch[key] = torch.stack([d[key] for d in batch], 0)
            
    return collated_batch 