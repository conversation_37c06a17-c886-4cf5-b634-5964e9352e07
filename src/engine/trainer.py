# -*- coding: utf-8 -*-
"""
创建时间: 2024-07-21
功能: 实现模型训练和验证的核心逻辑。`Trainer` 类封装了整个训练过程的循环、优化器管理、损失计算、模型参数更新、日志记录以及模型检查点的保存，旨在提供一个通用且可配置的训练框架。
输入:
  - `model`: PyTorch模型实例 (`nn.Module`)，待训练的模型。
  - `criterion`: 损失函数或损失函数字典，用于计算模型输出与真实标签之间的差异。对于MDN模型，这可能是一个包含多个MDN损失函数的字典。
  - `optimizer`: PyTorch优化器实例 (`torch.optim.Optimizer`)，用于更新模型参数。
  - `device`: 指定模型和数据将运行的计算设备（`torch.device` 对象，通常是 'cpu' 或 'cuda'）。
  - `train_loader`: 训练数据加载器 (`torch.utils.data.DataLoader`)，提供训练批次数据。
  - `val_loader`: 验证数据加载器 (`torch.utils.data.DataLoader`)，提供验证批次数据。
  - `writer`: TensorBoard `SummaryWriter` 实例，用于记录训练指标和可视化。
  - `checkpoint_dir`: 字符串，模型检查点（checkpoint）的保存目录。
  - `normalization_stats`: 字典，包含轨迹和目标特征的归一化均值和标准差，用于在损失计算前对真实轨迹进行反归一化（或确保归一化一致性）。
  - `config`: 完整的配置字典或对象，包含训练所需的所有超参数（如批次大小、学习率、损失权重、梯度裁剪阈值等）。
输出:
  - 训练和验证过程中的平均损失值（标量）。
  - 训练过程中的各项指标（如损失），记录到TensorBoard日志中。
  - 在训练过程中定期或根据性能指标保存的模型检查点文件，包括模型状态、优化器状态和当前Epoch数。
原理及处理方法:
  - **`__init__` 方法:** 初始化Trainer实例，接收模型、损失函数、优化器、数据加载器、TensorBoard writer、检查点目录、归一化统计和配置等所有必要组件。
  - **`train_one_epoch` 方法:**
    1. 将模型设置为训练模式 (`self.model.train()`)。
    2. 遍历 `train_loader` 中的每个批次数据。
    3. 将数据移动到指定设备 (`self.device`)。
    4. 清零优化器梯度 (`self.optimizer.zero_grad()`)。
    5. 根据 `model_type`（'V1' 或 'V5'），执行模型的特定前向传播逻辑。
       - 对于V1模型：计算意图分类损失（交叉熵）和轨迹回归损失（L1损失），并根据配置中的权重组合总损失。
       - 对于V5模型：计算轨迹MDN损失和目的地MDN损失（使用 `src.engine.loss` 中定义的MDN损失函数），并根据配置中的权重组合总损失。支持教师强制（teacher forcing）机制。
    6. 执行反向传播 (`loss.backward()`)。
    7. 根据配置，选择性地进行梯度裁剪 (`torch.nn.utils.clip_grad_norm_`) 以防止梯度爆炸。
    8. 更新模型参数 (`self.optimizer.step()`)。
    9. 记录当前批次的损失，并更新Tqdm进度条。计算并记录每个Epoch的平均训练损失到TensorBoard。
  - **`validate_one_epoch` 方法:**
    1. 将模型设置为评估模式 (`self.model.eval()`)，禁用梯度计算 (`torch.no_grad()`) 以节省内存和计算。
    2. 遍历 `val_loader` 中的每个批次数据。
    3. 将数据移动到指定设备。
    4. 执行模型前向传播并计算验证损失，逻辑与 `train_one_epoch` 类似，但V5模型在验证时教师强制比例为0。
    5. 记录当前批次的验证损失，并更新Tqdm进度条。计算并记录每个Epoch的平均验证损失到TensorBoard。
  - **数据归一化一致性:** 在 `train_one_epoch` 和 `validate_one_epoch` 中，会临时将真实轨迹（`ground_truth_trajectory`）根据 `normalization_stats` 进行归一化，以确保损失计算是在归一化后的空间中进行，与模型的预测输出保持一致。
  - **日志和进度:** 使用 `logging` 模块记录关键信息，并利用 `tqdm` 提供可视化的训练和验证进度条，提升用户体验和调试便利性。
  - **模块化设计:** 将训练流程的不同部分（模型、数据、优化器、损失）解耦为独立的组件，并通过Trainer类进行协调，使得代码结构清晰，易于扩展和维护不同类型的模型和训练策略。
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard.writer import SummaryWriter
from tqdm import tqdm
import logging
import os
import pickle
import numpy as np
import torch.nn.functional as F
from src.utils.normalization import denormalize_trajectory # 新增：导入denormalize_trajectory

# 移除 gmm_nll_loss，因为它不再适用于当前模型设计

class Trainer:
    """
    负责执行模型训练和验证循环的核心类。
    """
    def __init__(self, model, optimizer, criterion, train_loader, val_loader, device, writer, config, normalization_stats, current_trial_number=None):
        self.model = model
        self.optimizer = optimizer
        self.criterion = criterion # 可能是字典，根据模型类型不同而异
        self.device = device
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.writer = writer # 使用传入的 writer 实例
        self.config = config
        self.normalization_stats = normalization_stats # 仍然通过参数传递，因为这是数据相关的，不是模型本身配置
        self.current_trial_number = current_trial_number # Optuna trial number

        # 检查并获取模型名称
        if hasattr(self.config, 'model') and hasattr(self.config.model, 'name'):
        self.model_name = self.config.model.name
        else:
            # 作为备选方案，直接从模型对象获取类名
            self.model_name = self.model.__class__.__name__
        
        # 初始化最佳验证损失为一个非常大的值，以及早停计数器
        self.best_val_loss = float('inf')
        self.current_best_val_loss_epoch = -1
        self.patience_counter = 0

        # 初始化PECNet模型时，不再需要传递环境ROI的参数，因为将在Trainer中处理
        if self.model_name == 'PECNet':
            # 重新实例化模型，以确保其接受正确的env_feature_dim
            self.model = model.__class__(
                history_feature_dim=config.model.history_feature_dim,
                endpoint_dim=config.model.endpoint_dim,
                env_feature_dim=config.model.env_feature_dim, # 恢复为从配置中直接获取的env_feature_dim
                output_len=config.model.output_len,
                hidden_dim=config.model.hidden_dim,
                dropout_prob=config.model.dropout_prob,
                num_layers=config.model.num_layers
                # 不再传递 c_env, roi_h, roi_w, env_encoder_output_dim
            ).to(self.device)

        logging.info("Trainer 初始化完成。")

    def _move_batch_to_device(self, batch):
        """将批次中的所有张量移动到指定设备，跳过非张量类型。"""
        device_batch = {}
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                device_batch[key] = value.to(self.device)
            else:
                # 对于非张量类型（例如'original_history_points'的列表），直接保留原样
                device_batch[key] = value
        return device_batch

    # KL散度损失函数，作为私有方法
    def _kl_divergence_loss(self, mu, logvar):
        return -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())

    def train_one_epoch(self, epoch):
        self.model.train()
        total_loss = 0
        # Initialize individual losses for epoch average
        self.total_reconstruction_loss_traj = 0
        self.total_reconstruction_loss_endpoint = 0
        self.total_kl_loss = 0
        
        progress_bar = tqdm(self.train_loader, desc=f"Epoch {epoch+1} [训练]")
        for batch_idx, batch in enumerate(progress_bar):
            batch = self._move_batch_to_device(batch)

            if batch is None:
                logging.warning(f"Epoch {epoch+1}, Batch {batch_idx}: 跳过空批次数据。")
                continue
            
            # 将数据移动到设备
            history_data = batch['history_features'].to(self.device)
            environment_roi_data = batch['environment_roi'].to(self.device)
            ground_truth_destination = batch['ground_truth_destination'].to(self.device)
            ground_truth_trajectory = batch['ground_truth_trajectory'].to(self.device)
            
            # 清零梯度
            self.optimizer.zero_grad()

            # 根据模型类型进行前向传播和损失计算
            if self.model_name == 'GridClassifierV1': # 使用 self.model_name 替换 model_type
                # V1 模型输出: intent_logits, gmm_params_flat, predicted_goal_coords
                model_output = self.model(history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory) # 传入归一化后的轨迹
                intent_logits = model_output['intent_logits']
                gmm_params_flat = model_output['gmm_params_flat']
                # 提取预测的轨迹（这里是GMM模式下的最佳轨迹）
                predicted_trajectory = self.model.get_best_mode_trajectory(gmm_params_flat) # (B, pred_len_agg, 2)

                # 将真实目的地转换为网格标签
                # ground_truth_destination_abs = denormalize_trajectory(ground_truth_destination, self.normalization_stats, is_destination=True)
                ground_truth_grid_labels = self.model.coords_to_grid(ground_truth_destination) # 传入归一化后的真实目的地

                # 将真实未来轨迹反归一化，以便计算L1损失
                # 先将 CUDA tensor 移动到 CPU，然后转换为 NumPy，因为 denormalize_trajectory 期望 NumPy 输入
                denormalized_gt_trajectory_np = denormalize_trajectory(
                    ground_truth_trajectory.cpu().numpy(),
                    self.normalization_stats['target_x_mean'].item(),
                    self.normalization_stats['target_x_std'].item(),
                    self.normalization_stats['target_y_mean'].item(),
                    self.normalization_stats['target_y_std'].item()
                )
                # 将 NumPy 数组转换回 PyTorch Tensor 并移动到设备
                denormalized_gt_trajectory = torch.from_numpy(denormalized_gt_trajectory_np).to(self.device)

                # 计算损失
                # 意图分类损失 (交叉熵)
                intent_loss = self.criterion['intent_loss'](intent_logits, ground_truth_grid_labels)
                
                # 轨迹回归损失 (L1 Loss)
                # 注意：这里我们比较的是反归一化后的预测轨迹和真实轨迹
                traj_loss = self.criterion['trajectory_loss'](predicted_trajectory, denormalized_gt_trajectory)

                # 总损失
                loss = (self.config.training.LAMBDA_INTENT * intent_loss + 
                        self.config.training.LAMBDA_TRAJ * traj_loss)

                # 记录单个损失分量
                self.total_reconstruction_loss_traj += traj_loss.item()
                self.total_reconstruction_loss_endpoint += intent_loss.item() # 这里用intent_loss表示endpoint/intent相关损失

            elif self.model_name == 'IntentGuidedModel': # 新增 V2 意图引导模型处理逻辑
                # IntentGuidedModel 输出: intent_logits, gmm_params_flat, predicted_goal_coords
                model_output = self.model(history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory)
                intent_logits = model_output['intent_logits']
                gmm_params_flat = model_output['gmm_params_flat']

                # 从GMM参数中提取预测轨迹 - 不再需要，直接使用gmm_params_flat计算NLL损失
                # predicted_trajectory = self.model.get_best_mode_trajectory(gmm_params_flat)
                
                # 意图分类损失 (交叉熵)
                target_grid_indices = self.model.coords_to_grid(ground_truth_destination)
                intent_loss = self.criterion['intent_ce'](intent_logits, target_grid_indices) # 使用字典中的交叉熵损失

                # 轨迹回归损失 (GMM NLL损失)
                # ground_truth_trajectory 已经是归一化过的
                traj_loss = self.criterion['trajectory_nll'](gmm_params_flat, ground_truth_trajectory, self.model) # 使用字典中的GMM NLL损失

                # 组合损失
                loss = self.config.training.LAMBDA_INTENT * intent_loss + self.config.training.LAMBDA_TRAJ * traj_loss

            elif self.model_name == 'MDN_Predictor_V5': # 修正为正确的V5 MDN模型名称
                # 这里假设 'V5' 实际上对应的是 'AutoregressiveMDNPredictorModelV4' 或其他MDN模型
                # 如果有其他MDN模型，也需要在这里添加条件判断
                # V5 模型输出: mdn_params_sequence, destination_mdn_params
                # 教师强制比例从 config 中获取
                teacher_forcing_ratio = self.config.training.get('teacher_forcing_ratio', 0.5)
                mdn_params_sequence, destination_mdn_params = self.model(history_data, 
                                                                         ground_truth_trajectory, 
                                                                         ground_truth_destination, 
                                                                         teacher_forcing_ratio)
                
                # 轨迹MDN损失
                trajectory_loss = self.criterion['trajectory_nll'](mdn_params_sequence, ground_truth_trajectory, self.model)
                # 目的地MDN损失
                destination_loss = self.criterion['destination_nll'](destination_mdn_params, ground_truth_destination, self.model, is_destination_mdn=True)
                
                # 组合损失 (可以根据config中的权重调整)
                traj_loss_weight = self.config.training.get('trajectory_loss_weight', 1.0)
                dest_loss_weight = self.config.training.get('destination_loss_weight', 1.0)
                loss = traj_loss_weight * trajectory_loss + dest_loss_weight * destination_loss

            elif self.model_name == 'cnn_rnn_fusion': # 新增 cnn_rnn_fusion 模型的处理逻辑
                # 这里需要根据 cnn_rnn_fusion 模型的实际输入和输出进行调整
                # 假设 cnn_rnn_fusion 模型也需要 history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory
                # 并且输出也是 intent_logits 和 gmm_params_flat (或者类似的结构)
                model_output = self.model(history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory)
                
                # 假设 cnn_rnn_fusion 模型也有 intent_logits 和 gmm_params_flat
                # 如果模型输出结构不同，这里需要进一步细化
                intent_logits = model_output['intent_logits']
                gmm_params_flat = model_output['gmm_params_flat']

                # 意图分类损失 (交叉熵)
                target_grid_indices = self.model.coords_to_grid(ground_truth_destination) # 假设 cnn_rnn_fusion 也有 coords_to_grid
                intent_loss = F.cross_entropy(intent_logits, target_grid_indices)

                # 轨迹回归损失 (L1损失或MDN损失)
                # 如果 cnn_rnn_fusion 使用MDN进行轨迹预测，这里需要调用MDN损失函数
                # 如果使用L1Loss，则保持与V1类似
                # 这里需要根据 cnn_rnn_fusion 模型的具体输出和损失定义来调整
                predicted_trajectory = self.model.get_best_mode_trajectory(gmm_params_flat) # 假设 cnn_rnn_fusion 也有 get_best_mode_trajectory
                traj_loss = self.criterion(predicted_trajectory, ground_truth_trajectory)

                # 组合损失
                loss = self.config.training.LAMBDA_INTENT * intent_loss + self.config.training.LAMBDA_TRAJ * traj_loss
            
            elif self.model_name == 'EndToEndModel': # 添加 EndToEndModel 的处理逻辑
                # EndToEndModel 的 forward 方法期望 ground_truth_trajectory 为绝对坐标
                # 因此，需要反归一化 ground_truth_trajectory 和 ground_truth_destination
                # mean_tensor = torch.tensor(self.normalization_stats['target_x_mean']).unsqueeze(0).unsqueeze(0) # 假设 mean_tensor 是 (1, 1, 2)
                # std_tensor = torch.tensor(self.normalization_stats['target_x_std']).unsqueeze(0).unsqueeze(0) # 假设 std_tensor 是 (1, 1, 2)
                # gt_trajectory_abs = (ground_truth_trajectory * std_tensor) + mean_tensor
                # 对于 destination，假设是 (B, 2) 形状，mean_tensor 和 std_tensor 也是 (2,) 或可广播
                # gt_destination_abs = (ground_truth_destination * std_tensor) + mean_tensor

                # model_output = self.model(history_data, environment_roi_data, gt_trajectory_abs) # 传入绝对坐标的真值
                
                # destination_params = model_output['destination_params']
                # predicted_trajectory_abs = model_output['predicted_trajectory'] # 模型直接输出绝对坐标

                # 损失计算:
                # destination_nll 期望归一化后的目的地作为目标
                # destination_loss = self.criterion['destination_nll'](destination_params, ground_truth_destination, self.model, is_destination_mdn=True)
                
                # trajectory_loss 期望绝对坐标的预测和目标
                # trajectory_loss = self.criterion['trajectory_loss'](predicted_trajectory_abs, gt_trajectory_abs) # 传入绝对坐标的真值
                
                # loss = self.config.training.destination_loss_weight * destination_loss + \
                #        self.config.training.trajectory_loss_weight * trajectory_loss

                # 移除此部分逻辑，因为 EndToEndModel 的损失计算方式与上述不同
                # 假设 EndToEndModel 的 forward 方法直接输出归一化后的轨迹和目的地
                model_output = self.model(history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory)
                
                destination_params = model_output['destination_params']
                predicted_trajectory_normalized = model_output['predicted_trajectory']
                predicted_destination_normalized = model_output['predicted_destination']

                # 损失计算:
                # destination_nll 期望归一化后的目的地作为目标
                destination_loss = self.criterion['destination_nll'](destination_params, ground_truth_destination, self.model, is_destination_mdn=True)
                
                # trajectory_loss 期望归一化后的预测轨迹作为目标
                trajectory_loss = self.criterion['trajectory_loss'](predicted_trajectory_normalized, ground_truth_trajectory)
                
                loss = self.config.training.destination_loss_weight * destination_loss + \
                       self.config.training.trajectory_loss_weight * trajectory_loss

            elif self.model_name == 'PECNet': # 新增 PECNet 模型的处理逻辑
                # PECNet 的 forward 方法期望 ground_truth_destination 和 ground_truth_trajectory 已经归一化
                # training 模式下会进行重参数化采样，validation 模式下会使用均值
                
                # 对 environment_roi_data 进行平均池化，生成聚合后的环境特征
                environment_features_pooled_hw = environment_roi_data.mean(dim=(-1, -2))
                environment_features_aggregated = environment_features_pooled_hw.mean(dim=1) # 形状: (batch_size, env_feature_dim)

                model_output = self.model(history_data,
                                          environment_features=environment_features_aggregated, # 传入聚合后的环境特征
                                          ground_truth_destination=ground_truth_destination,
                                          ground_truth_trajectory=ground_truth_trajectory,
                                          is_train=True) # train_one_epoch 总是训练模式
                
                # 调试: 检查模型输出
                predicted_endpoint_mean = model_output['predicted_endpoint_mean']
                predicted_endpoint_logvar = model_output['predicted_endpoint_logvar']
                predicted_trajectories_sampled = model_output['predicted_trajectories_sampled'] # 获取采样的轨迹

                if torch.isnan(predicted_endpoint_mean).any() or torch.isinf(predicted_endpoint_mean).any():
                    logging.error(f"训练: predicted_endpoint_mean 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}") # 调试日志
                if torch.isnan(predicted_endpoint_logvar).any() or torch.isinf(predicted_endpoint_logvar).any():
                    logging.error(f"训练: predicted_endpoint_logvar 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")
                if torch.isnan(predicted_trajectories_sampled).any() or torch.isinf(predicted_trajectories_sampled).any():
                    logging.error(f"训练: predicted_trajectories_sampled 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")

                logging.debug(f"训练: pred_endpoint_mean range: [{torch.min(predicted_endpoint_mean).item():.4f}, {torch.max(predicted_endpoint_mean).item():.4f}], mean: {torch.mean(predicted_endpoint_mean).item():.4f}, Trial {self.current_trial_number}")
                logging.debug(f"训练: pred_endpoint_logvar range: [{torch.min(predicted_endpoint_logvar).item():.4f}, {torch.max(predicted_endpoint_logvar).item():.4f}], mean: {torch.mean(predicted_endpoint_logvar).item():.4f}, Trial {self.current_trial_number}")
                logging.debug(f"训练: pred_traj_sampled range: [{torch.min(predicted_trajectories_sampled).item():.4f}, {torch.max(predicted_trajectories_sampled).item():.4f}], mean: {torch.mean(predicted_trajectories_sampled).item():.4f}, Trial {self.current_trial_number}")

                # 计算每个采样轨迹与真实轨迹的重建损失 (Min-of-N Loss)
                # predicted_trajectories_sampled 形状: (num_samples, batch_size, output_len, 2)
                # ground_truth_trajectory 形状: (batch_size, output_len, 2)
                # 为了计算每个样本的Min-of-N损失，需要扩展 ground_truth_trajectory 的维度以匹配 predicted_trajectories_sampled
                gt_traj_expanded = ground_truth_trajectory.unsqueeze(0) # 形状: (1, batch_size, output_len, 2)
                
                # 计算所有采样轨迹的MSE损失
                # 结果形状: (num_samples, batch_size)
                all_sample_traj_losses = torch.mean(
                    self.criterion['reconstruction'](predicted_trajectories_sampled, gt_traj_expanded).view(predicted_trajectories_sampled.shape[0], -1),
                    dim=1
                ) # 这里需要确保criterion返回的是每个样本的损失
                
                # 选择每个批次中最小的损失
                reconstruction_loss_traj = torch.min(all_sample_traj_losses, dim=0)[0].mean() # 对批次取平均

                # 端点重建损失 (使用均值计算)
                reconstruction_loss_endpoint = self.criterion['reconstruction'](predicted_endpoint_mean, ground_truth_destination).mean()
                
                # KL散度损失
                kl_loss = self.criterion['kl_divergence'](predicted_endpoint_mean, predicted_endpoint_logvar)

                # 调试: 检查损失值
                if torch.isnan(reconstruction_loss_traj).any() or torch.isinf(reconstruction_loss_traj).any():
                    logging.error(f"训练: reconstruction_loss_traj 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")
                if torch.isnan(reconstruction_loss_endpoint).any() or torch.isinf(reconstruction_loss_endpoint).any():
                    logging.error(f"训练: reconstruction_loss_endpoint 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")
                if torch.isnan(kl_loss).any() or torch.isinf(kl_loss).any():
                    logging.error(f"训练: kl_loss 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")

                logging.debug(f"训练: Loss components - traj_rec: {reconstruction_loss_traj.item():.4f}, endpoint_rec: {reconstruction_loss_endpoint.item():.4f}, kl: {kl_loss.item():.4f}, Trial {self.current_trial_number}")

                # 组合损失 (权重从 config 中获取)
                # reconstruction_weight = self.config.training.get('reconstruction_loss_weight', 1.0) # 这个权重现在被拆分
                trajectory_reconstruction_weight = self.config.training.get('trajectory_reconstruction_weight', 1.0)
                endpoint_reconstruction_weight = self.config.training.get('endpoint_reconstruction_weight', 1.0)
                kl_weight = self.config.training.get('kl_loss_weight', 0.0001) # KL损失通常权重较小，进一步降低默认值

                # 使用新的独立权重组合损失
                loss = trajectory_reconstruction_weight * reconstruction_loss_traj + \
                       endpoint_reconstruction_weight * reconstruction_loss_endpoint + \
                       kl_weight * kl_loss

            elif self.model_name == 'MaskedAutoregressiveModel': # 新增 V3 模型的处理逻辑
                history_mask = batch['history_mask'].to(self.device)
                
                # V3 模型直接输出归一化后的轨迹 - 使用关键字参数明确传递
                predicted_trajectory = self.model(
                    history_trajectory=history_data,
                    environment_roi=environment_roi_data,
                    history_mask=history_mask,
                    ground_truth_trajectory=ground_truth_trajectory
                )
                
                # 使用单一的MSE损失函数
                loss = self.criterion(predicted_trajectory, ground_truth_trajectory)

            else:
                raise ValueError(f"未知的模型类型: {self.model_name}")

            # 反向传播
            loss.backward()
            
            # 梯度裁剪 (可选，根据config决定是否使用)
            if self.config.training.gradient_clipping.enabled: # 从 config.training 获取梯度裁剪阈值
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.training.gradient_clipping.max_norm)

            # 更新模型参数
            self.optimizer.step()

            total_loss += loss.item()
            # Accumulate individual losses for epoch average
            if self.model_name == 'PECNet':
                self.total_reconstruction_loss_traj += reconstruction_loss_traj.item()
                self.total_reconstruction_loss_endpoint += reconstruction_loss_endpoint.item()
                self.total_kl_loss += kl_loss.item()

            progress_bar.set_postfix(loss=f'{loss.item():.4f}')

        avg_loss = total_loss / len(self.train_loader)
        logging.info(f"Epoch {epoch+1} 训练平均损失: {avg_loss:.4f}")
        self.writer.add_scalar('Loss/Train/Total', avg_loss, epoch) # Change this
        
        if self.model_name == 'PECNet':
            avg_reconstruction_loss_traj = self.total_reconstruction_loss_traj / len(self.train_loader)
            avg_reconstruction_loss_endpoint = self.total_reconstruction_loss_endpoint / len(self.train_loader)
            avg_kl_loss = self.total_kl_loss / len(self.train_loader)
            self.writer.add_scalar('Loss/Train/Trajectory_Reconstruction', avg_reconstruction_loss_traj, epoch)
            self.writer.add_scalar('Loss/Train/Endpoint_Reconstruction', avg_reconstruction_loss_endpoint, epoch)
            self.writer.add_scalar('Loss/Train/KL_Divergence', avg_kl_loss, epoch)

        return avg_loss

    def validate_one_epoch(self, epoch):
        self.model.eval()
        total_val_loss = 0
        # Initialize individual losses for epoch average
        self.total_reconstruction_loss_traj = 0
        self.total_reconstruction_loss_endpoint = 0
        self.total_kl_loss = 0
        
        with torch.no_grad():
            progress_bar = tqdm(self.val_loader, desc=f"Epoch {epoch+1} [验证]")
            for batch_idx, batch in enumerate(progress_bar):
                batch = self._move_batch_to_device(batch)

                if batch is None:
                    logging.warning(f"Epoch {epoch+1}, Batch {batch_idx}: 跳过空批次验证数据。")
                    continue

                # 将数据移动到设备
                history_data = batch['history_features'].to(self.device)
                environment_roi_data = batch['environment_roi'].to(self.device)
                ground_truth_destination = batch['ground_truth_destination'].to(self.device)
                ground_truth_trajectory = batch['ground_truth_trajectory'].to(self.device)

                                  # 清零梯度
                self.optimizer.zero_grad()
                
                # 根据模型类型进行前向传播和损失计算
                if self.model_name == 'GridClassifierV1': # 使用 self.model_name 替换 model_type
                    model_output = self.model(history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory) # 传入归一化后的轨迹
                    intent_logits = model_output['intent_logits']
                    gmm_params_flat = model_output['gmm_params_flat']
                    
                    predicted_trajectory = self.model.get_best_mode_trajectory(gmm_params_flat) # (B, pred_len_agg, 2)

                    ground_truth_grid_labels = self.model.coords_to_grid(ground_truth_destination)
                    denormalized_gt_trajectory_np = denormalize_trajectory(
                        ground_truth_trajectory.cpu().numpy(),
                        self.normalization_stats['target_x_mean'].item(),
                        self.normalization_stats['target_x_std'].item(),
                        self.normalization_stats['target_y_mean'].item(),
                        self.normalization_stats['target_y_std'].item()
                    )
                    # 将 NumPy 数组转换回 PyTorch Tensor 并移动到设备
                    denormalized_gt_trajectory = torch.from_numpy(denormalized_gt_trajectory_np).to(self.device)

                    # 计算损失
                    intent_loss = self.criterion['intent_loss'](intent_logits, ground_truth_grid_labels)
                    traj_loss = self.criterion['trajectory_loss'](predicted_trajectory, denormalized_gt_trajectory)
                    # kl_loss = self._kl_divergence_loss(model_output['mu'], model_output['logvar']) # 移除此行

                    loss = (self.config.training.LAMBDA_INTENT * intent_loss + 
                            self.config.training.LAMBDA_TRAJ * traj_loss)
                            # + self.config.training.kl_loss_weight * kl_loss) # 移除KL损失项

                    self.total_reconstruction_loss_traj += traj_loss.item()
                    self.total_reconstruction_loss_endpoint += intent_loss.item()
                    # self.total_kl_loss += kl_loss.item() # 移除此行

                elif self.model_name == 'IntentGuidedModel': # 新增 V2 意图引导模型处理逻辑
                    model_output = self.model(history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory)
                    intent_logits = model_output['intent_logits']
                    gmm_params_flat = model_output['gmm_params_flat']

                    # 从GMM参数中提取预测轨迹 - 不再需要，直接使用gmm_params_flat计算NLL损失
                    # predicted_trajectory = self.model.get_best_mode_trajectory(gmm_params_flat)
                    
                    # 意图分类损失 (交叉熵)
                    target_grid_indices = self.model.coords_to_grid(ground_truth_destination)
                    intent_loss = self.criterion['intent_ce'](intent_logits, target_grid_indices) # 使用字典中的交叉熵损失

                    # 轨迹回归损失 (GMM NLL损失)
                    # ground_truth_trajectory 已经是归一化过的
                    traj_loss = self.criterion['trajectory_nll'](gmm_params_flat, ground_truth_trajectory, self.model) # 使用字典中的GMM NLL损失

                    # 组合损失
                    loss = self.config.training.LAMBDA_INTENT * intent_loss + self.config.training.LAMBDA_TRAJ * traj_loss

                elif self.model_name == 'MDN_Predictor_V5': # 使用 config.model.name 作为类型
                    # V5 模型，验证时 teacher_forcing_ratio=0
                    mdn_params_sequence, destination_mdn_params = self.model(history_data, 
                                                                             ground_truth_trajectory, 
                                                                             ground_truth_destination, 
                                                                             teacher_forcing_ratio=0.0)
                    trajectory_loss = self.criterion['trajectory_nll'](mdn_params_sequence, ground_truth_trajectory, self.model)
                    destination_loss = self.criterion['destination_nll'](destination_mdn_params, ground_truth_destination, self.model, is_destination_mdn=True)
                    traj_loss_weight = self.config.training.get('trajectory_loss_weight', 1.0)
                    dest_loss_weight = self.config.training.get('destination_loss_weight', 1.0)
                    loss = traj_loss_weight * trajectory_loss + dest_loss_weight * destination_loss

                elif self.model_name == 'cnn_rnn_fusion': # 新增 cnn_rnn_fusion 模型的处理逻辑
                    model_output = self.model(history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory)
                    intent_logits = model_output['intent_logits']
                    gmm_params_flat = model_output['gmm_params_flat']

                    intent_loss = F.cross_entropy(intent_logits, self.model.coords_to_grid(ground_truth_destination))
                    predicted_trajectory = self.model.get_best_mode_trajectory(gmm_params_flat)
                    traj_loss = self.criterion(predicted_trajectory, ground_truth_trajectory)

                    loss = self.config.training.LAMBDA_INTENT * intent_loss + self.config.training.LAMBDA_TRAJ * traj_loss

                elif self.model_name == 'EndToEndModel': # 添加 EndToEndModel 的处理逻辑
                    # EndToEndModel 的 forward 方法期望 ground_truth_trajectory 为绝对坐标
                    # 因此，需要反归一化 ground_truth_trajectory 和 ground_truth_destination
                    # mean_tensor = torch.tensor(self.normalization_stats['target_x_mean']).unsqueeze(0).unsqueeze(0) # 假设 mean_tensor 是 (1, 1, 2)
                    # std_tensor = torch.tensor(self.normalization_stats['target_x_std']).unsqueeze(0).unsqueeze(0) # 假设 std_tensor 是 (1, 1, 2)
                    # gt_trajectory_abs = (ground_truth_trajectory * std_tensor) + mean_tensor
                    # 对于 destination，假设是 (B, 2) 形状，mean_tensor 和 std_tensor 也是 (2,) 或可广播
                    # gt_destination_abs = (ground_truth_destination * std_tensor) + mean_tensor

                    # model_output = self.model(history_data, environment_roi_data, gt_trajectory_abs) # 传入绝对坐标的真值
                    
                    # destination_params = model_output['destination_params']
                    # predicted_trajectory_abs = model_output['predicted_trajectory'] # 模型直接输出绝对坐标

                    # 损失计算:
                    # destination_nll 期望归一化后的目的地作为目标
                    # destination_loss = self.criterion['destination_nll'](destination_params, ground_truth_destination, self.model, is_destination_mdn=True)
                    
                    # trajectory_loss 期望绝对坐标的预测和目标
                    # trajectory_loss = self.criterion['trajectory_loss'](predicted_trajectory_abs, gt_trajectory_abs) # 传入绝对坐标的真值
                    
                    # loss = self.config.training.destination_loss_weight * destination_loss + \
                    #        self.config.training.trajectory_loss_weight * trajectory_loss

                    # 移除此部分逻辑，因为 EndToEndModel 的损失计算方式与上述不同
                    # 假设 EndToEndModel 的 forward 方法直接输出归一化后的轨迹和目的地
                    model_output = self.model(history_data, environment_roi_data, ground_truth_destination, ground_truth_trajectory)
                    
                    destination_params = model_output['destination_params']
                    predicted_trajectory_normalized = model_output['predicted_trajectory']
                    predicted_destination_normalized = model_output['predicted_destination']

                    # 损失计算:
                    # destination_nll 期望归一化后的目的地作为目标
                    destination_loss = self.criterion['destination_nll'](destination_params, ground_truth_destination, self.model, is_destination_mdn=True)
                    
                    # trajectory_loss 期望归一化后的预测轨迹作为目标
                    trajectory_loss = self.criterion['trajectory_loss'](predicted_trajectory_normalized, ground_truth_trajectory)
                    
                    loss = self.config.training.destination_loss_weight * destination_loss + \
                           self.config.training.trajectory_loss_weight * trajectory_loss

                elif self.model_name == 'PECNet': # 新增 PECNet 模型的处理逻辑 (验证阶段)
                    # PECNet 的 forward 方法期望 ground_truth_destination 和 ground_truth_trajectory 已经归一化
                    # validation 模式下会使用均值

                    # 对 environment_roi_data 进行平均池化，生成聚合后的环境特征
                    environment_features_pooled_hw = environment_roi_data.mean(dim=(-1, -2))
                    environment_features_aggregated = environment_features_pooled_hw.mean(dim=1) # 形状: (batch_size, env_feature_dim)

                    model_output = self.model(history_data,
                                              environment_features=environment_features_aggregated, # 传入聚合后的环境特征
                                              ground_truth_destination=ground_truth_destination,
                                              ground_truth_trajectory=ground_truth_trajectory,
                                              is_train=False) # validate_one_epoch 总是验证模式

                    # 调试: 检查模型输出 (验证阶段)
                    predicted_endpoint_mean = model_output['predicted_endpoint_mean']
                    predicted_endpoint_logvar = model_output['predicted_endpoint_logvar']
                    predicted_trajectories_sampled = model_output['predicted_trajectories_sampled']

                    if torch.isnan(predicted_endpoint_mean).any() or torch.isinf(predicted_endpoint_mean).any():
                        logging.error(f"验证: predicted_endpoint_mean 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")
                    if torch.isnan(predicted_endpoint_logvar).any() or torch.isinf(predicted_endpoint_logvar).any():
                        logging.error(f"验证: predicted_endpoint_logvar 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")
                    if torch.isnan(predicted_trajectories_sampled).any() or torch.isinf(predicted_trajectories_sampled).any():
                        logging.error(f"验证: predicted_trajectories_sampled 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")

                    logging.debug(f"验证: pred_endpoint_mean range: [{torch.min(predicted_endpoint_mean).item():.4f}, {torch.max(predicted_endpoint_mean).item():.4f}], mean: {torch.mean(predicted_endpoint_mean).item():.4f}, Trial {self.current_trial_number}")
                    logging.debug(f"验证: pred_endpoint_logvar range: [{torch.min(predicted_endpoint_logvar).item():.4f}, {torch.max(predicted_endpoint_logvar).item():.4f}], mean: {torch.mean(predicted_endpoint_logvar).item():.4f}, Trial {self.current_trial_number}")
                    logging.debug(f"验证: pred_traj_sampled range: [{torch.min(predicted_trajectories_sampled).item():.4f}, {torch.max(predicted_trajectories_sampled).item():.4f}], mean: {torch.mean(predicted_trajectories_sampled).item():.4f}, Trial {self.current_trial_number}")

                    # 计算每个采样轨迹与真实轨迹的重建损失 (Min-of-N Loss)
                    gt_traj_expanded = ground_truth_trajectory.unsqueeze(0) # 形状: (1, batch_size, output_len, 2)
                    all_sample_traj_losses = torch.mean(
                        self.criterion['reconstruction'](predicted_trajectories_sampled, gt_traj_expanded).view(predicted_trajectories_sampled.shape[0], -1),
                        dim=1
                    )
                    reconstruction_loss_traj = torch.min(all_sample_traj_losses, dim=0)[0].mean()

                    # 端点重建损失 (使用均值计算)
                    reconstruction_loss_endpoint = self.criterion['reconstruction'](predicted_endpoint_mean, ground_truth_destination).mean()

                    # KL散度损失
                    predicted_endpoint_logvar = model_output['predicted_endpoint_logvar'] # 验证阶段也需要
                    kl_loss = self.criterion['kl_divergence'](predicted_endpoint_mean, predicted_endpoint_logvar)

                    # 调试: 检查损失值 (验证阶段)
                    if torch.isnan(reconstruction_loss_traj).any() or torch.isinf(reconstruction_loss_traj).any():
                        logging.error(f"验证: reconstruction_loss_traj 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")
                    if torch.isnan(reconstruction_loss_endpoint).any() or torch.isinf(reconstruction_loss_endpoint).any():
                        logging.error(f"验证: reconstruction_loss_endpoint 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")
                    if torch.isnan(kl_loss).any() or torch.isinf(kl_loss).any():
                        logging.error(f"验证: kl_loss 包含 NaN/Inf. Epoch {epoch}, Trial {self.current_trial_number}")

                    logging.debug(f"验证: Loss components - traj_rec: {reconstruction_loss_traj.item():.4f}, endpoint_rec: {reconstruction_loss_endpoint.item():.4f}, kl: {kl_loss.item():.4f}, Trial {self.current_trial_number}")

                    # 组合损失 (权重从 config 中获取)
                    trajectory_reconstruction_weight = self.config.training.get('trajectory_reconstruction_weight', 1.0)
                    endpoint_reconstruction_weight = self.config.training.get('endpoint_reconstruction_weight', 1.0)
                    kl_weight = self.config.training.get('kl_loss_weight', 0.0001)

                    # 使用新的独立权重组合损失
                    loss = trajectory_reconstruction_weight * reconstruction_loss_traj + \
                           endpoint_reconstruction_weight * reconstruction_loss_endpoint + \
                           kl_weight * kl_loss

                elif self.model_name == 'MaskedAutoregressiveModel': # 新增 V3 模型的处理逻辑
                    history_mask = batch['history_mask'].to(self.device)

                    # 验证时，不使用教师强制 (在V3模型内部实现) - 使用关键字参数明确传递
                    predicted_trajectory = self.model(
                        history_trajectory=history_data,
                        environment_roi=environment_roi_data,
                        history_mask=history_mask,
                        ground_truth_trajectory=None  # 验证时不传递真实轨迹
                    )
                    
                    # 使用单一的MSE损失函数
                    loss = self.criterion(predicted_trajectory, ground_truth_trajectory)

                else:
                    raise ValueError(f"未知的模型类型: {self.model_name}")
                
                total_val_loss += loss.item()
                progress_bar.set_postfix(val_loss=f'{loss.item():.4f}')

            avg_val_loss = total_val_loss / len(self.val_loader)
            logging.info(f"Epoch {epoch+1} 验证平均损失: {avg_val_loss:.4f}")
            self.writer.add_scalar('Loss/Validation/Total', avg_val_loss, epoch) # 添加Total损失

            if self.model_name == 'PECNet': # 添加详细验证损失日志
                avg_val_reconstruction_loss_traj = total_val_reconstruction_loss_traj / len(self.val_loader)
                avg_val_reconstruction_loss_endpoint = total_val_reconstruction_loss_endpoint / len(self.val_loader)
                avg_val_kl_loss = total_val_kl_loss / len(self.val_loader)
                self.writer.add_scalar('Loss/Validation/Trajectory_Reconstruction', avg_val_reconstruction_loss_traj, epoch)
                self.writer.add_scalar('Loss/Validation/Endpoint_Reconstruction', avg_val_reconstruction_loss_endpoint, epoch)
                self.writer.add_scalar('Loss/Validation/KL_Divergence', avg_val_kl_loss, epoch)
            
            # 移除早停和模型保存逻辑
            # if avg_val_loss < self.best_val_loss: ...

            return avg_val_loss 