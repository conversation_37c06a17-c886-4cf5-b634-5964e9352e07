# -*- coding: utf-8 -*-
"""
创建时间: 2024-07-21
功能: 定义了用于轨迹预测模型训练的各种损失函数，特别是针对混合高斯模型 (MDN) 输出的负对数似然损失（NLL loss）。它支持针对单点（如目的地）和整个序列（如未来轨迹）的MDN损失计算，并提供了L1/MSE/Huber等通用回归损失的实现。
输入:
  - `outputs` (torch.Tensor): 模型的预测输出，通常是MDN参数 (pi, mu_x, mu_y, sigma_x, sigma_y, rho) 的组合，或直接的回归预测值。
  - `targets` (torch.Tensor): 真实的标签，例如真实的目的地坐标或真实的未来轨迹坐标序列。
  - `model`: 相关的MDN模型实例，当计算MDN损失时需要具有 `get_mixture_params` 方法和 `num_mixture` 属性。
  - `loss_type` (str): 对于 `CustomLoss`，指定损失类型（'l1', 'mse', 'huber'）。
  - `delta` (float): 对于 `Huber Loss`，指定 `delta` 参数，用于控制损失函数在误差较大时的行为。
输出:
  - 标量损失值 (torch.Tensor)。
原理及处理方法:
  - **`CustomLoss` 类:** 继承自 `nn.Module`，提供 L1 (MAE)、MSE 和 Huber 损失的实现。它作为一个通用的回归损失函数，允许根据训练需求选择不同的误差度量。
  - **`single_point_mdn_nll_loss` 函数:**
    - 专门用于计算单个二维点（例如预测目的地）的混合高斯分布负对数似然损失。
    - 从模型输出中解析出高斯混合模型的参数（混合系数 `pi`、均值 `mu_x`, `mu_y`、标准差 `sigma_x`, `sigma_y`、相关系数 `rho`）。
    - 计算每个混合成分在给定真实目标点下的对数概率密度，然后使用 Log-Sum-Exp 技巧（`torch.logsumexp`）计算所有混合成分的加权对数概率和，以提高数值稳定性。
    - 最终返回负的对数似然的均值作为损失值。对 `sigma_x`, `sigma_y`, `rho` 的数值稳定性进行了处理，并通过添加小常数 (`1e-6` 或 `1e-8`) 或 `clamp` 函数防止除以零、取对数负数或 `rho` 超出有效范围。
  - **`autoregressive_mdn_nll_loss` 函数:**
    - 用于计算整个轨迹序列的混合高斯分布负对数似然损失。其核心计算逻辑与 `single_point_mdn_nll_loss` 类似，但针对批次和序列维度进行展平处理，以便同时计算所有时间步的损失。同样包含了数值稳定性处理和NaN/Inf损失检查。
  - **模型参数解析:** 在MDN损失函数中，通过检查模型是否经过 `torch.compile` 优化，确保能够正确访问原始模型实例的 `get_mixture_params` 方法和 `num_mixture` 属性，以正确解析预测的MDN参数。
  - **数值稳定性与错误处理:** 在所有损失计算中，都特别关注了数值稳定性问题，例如在分母和对数运算中添加小常数。此外，当检测到损失值为NaN或Inf时，会发出警告并返回一个较大的默认值，以防止训练过程崩溃。
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
import logging

# 定义目标维度，与模型输出匹配
TARGET_DIM = 2 

class CustomLoss(nn.Module):
    """
    自定义损失函数，暂时实现为L1Loss。
    后续可以根据需要扩展为Huber Loss或其他复合损失。
    """
    def __init__(self, loss_type='l1', delta=1.0):
        super(CustomLoss, self).__init__()
        self.loss_type = loss_type
        self.delta = delta # 用于Huber Loss

    def forward(self, predictions, targets):
        if self.loss_type == 'l1':
            loss = F.l1_loss(predictions, targets)
        elif self.loss_type == 'mse':
            loss = F.mse_loss(predictions, targets)
        elif self.loss_type == 'huber':
            # Huber Loss: L_delta(x) = x^2/2 if |x| < delta, else delta * (|x| - delta/2)
            error = torch.abs(predictions - targets)
            quadratic = torch.min(error, torch.tensor(self.delta, device=error.device))
            linear = error - quadratic
            loss = 0.5 * quadratic**2 + self.delta * linear
            loss = torch.mean(loss)
        else:
            raise ValueError(f"Unsupported loss type: {self.loss_type}")
        return loss

def reconstruction_loss(prediction, target):
    """
    计算重建损失，默认为均方误差 (MSE)。
    """
    return F.mse_loss(prediction, target, reduction='none')

def kl_divergence_loss(mu, logvar):
    """
    计算高斯分布与标准正态分布之间的KL散度损失。
    """
    # -0.5 * sum(1 + log(sigma^2) - mu^2 - sigma^2)
    return -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())

def gmm_nll_loss_v2(gmm_params_flat, targets, model):
    """
    V2版本的GMM NLL损失函数，适配IntentGuidedModel。
    """
    # ... (原有 GMM NLL 逻辑)
    # 确保能够访问到原始模型 (如果使用了 torch.compile)
    if hasattr(model, '_orig_mod'): 
        model_ref = model._orig_mod
    else: 
        model_ref = model

    pi, mu, sigma = model_ref.get_gmm_params(gmm_params_flat)
    
    # ... (省略剩余的 GMM NLL 计算逻辑)

def single_point_mdn_nll_loss(outputs, targets, model, is_destination_mdn=True):
    """
    计算单点（例如目的地）的混合高斯分布负对数似然损失。
    Args:
        outputs (torch.Tensor): 模型预测的MDN参数, shape (B, 6 * num_mixture)
        targets (torch.Tensor): 真实的目标点, shape (B, TARGET_DIM)
        model: MDN模型实例 (需要有 get_mixture_params 和 num_mixture 属性)
        is_destination_mdn: bool, 指示是否为目的地MDN，影响 num_mixture 的选择。
    Returns:
        torch.Tensor: 标量损失值。
    """
    # 确保能够访问到原始模型 (如果使用了 torch.compile)
    if hasattr(model, '_orig_mod'): 
        model_ref = model._orig_mod
    else: 
        model_ref = model
    
    # 关键修复：检查新的、功能明确的接口是否存在
    if not hasattr(model_ref, 'get_trajectory_mixture_params') or not hasattr(model_ref, 'get_destination_mixture_params'):
         raise AttributeError("Model must have 'get_trajectory_mixture_params' and 'get_destination_mixture_params' for MDN loss.")

    # 关键修复：根据is_destination_mdn调用不同的、功能明确的接口
    if is_destination_mdn:
        pi, mu_x, mu_y, sigma_x, sigma_y, rho = model_ref.get_destination_mixture_params(outputs)
        num_mixture = model_ref.dest_num_mixture
    else:
        pi, mu_x, mu_y, sigma_x, sigma_y, rho = model_ref.get_trajectory_mixture_params(outputs)
        num_mixture = model_ref.num_mixture

    # targets_flat 形状是 (B, TARGET_DIM)
    x = targets[:, 0].unsqueeze(1).expand(-1, num_mixture) # 展开到 num_mixture
    y = targets[:, 1].unsqueeze(1).expand(-1, num_mixture)

    # 确保 sigma 和 rho 的数值稳定性
    sigma_x_stable = torch.clamp(sigma_x, min=1e-8) # 确保 sigma_x 严格为正
    sigma_y_stable = torch.clamp(sigma_y, min=1e-8) # 确保 sigma_y 严格为正
    rho_stable = torch.clamp(rho, min=-0.99999, max=0.99999) # 更严格地限制 rho 范围

    dx = (x - mu_x) / sigma_x_stable
    dy = (y - mu_y) / sigma_y_stable
    
    # 计算指数项
    z = dx**2 + dy**2 - 2 * rho_stable * dx * dy
    denom = 1 - rho_stable**2

    # 避免分母为零或负数，增加数值稳定性
    log_exp_term = -0.5 * z / (denom + 1e-12) # 将 1e-8 改为 1e-12，更小的值

    # 计算归一化常数
    log_2pi = math.log(2 * np.pi)
    log_sigma_prod = torch.log(sigma_x_stable) + torch.log(sigma_y_stable)
    # log_sqrt_denom = 0.5 * torch.log(denom + 1e-8) # 确保 denom 也是正数
    log_sqrt_denom = 0.5 * torch.log(denom + 1e-12) # 同样使用 1e-12

    log_norm_const = log_2pi + log_sigma_prod + log_sqrt_denom

    # 计算每个混合成分的对数概率密度
    log_prob_comp = log_exp_term - log_norm_const

    # 计算带混合权重的对数概率 (log-sum-exp for numerical stability)
    log_pi = torch.log(pi + 1e-12) # 确保pi非零，使用1e-12
    log_weighted_prob = log_pi + log_prob_comp
    log_total_prob = torch.logsumexp(log_weighted_prob, dim=1)
    
    # 负对数似然损失
    loss = -torch.mean(log_total_prob)

    if torch.isnan(loss) or torch.isinf(loss):
        logging.warning("Warning: NaN or Inf loss detected in single_point_mdn_nll_loss! Returning large value.")
        return torch.tensor(1e6, device=outputs.device, requires_grad=True)
    return loss

def autoregressive_mdn_nll_loss(outputs, targets, model):
    """
    计算自回归模型在整个序列上的混合高斯分布负对数似然损失。
    Args:
        outputs (torch.Tensor): 模型预测的MDN参数序列, shape (B, T_pred_agg, 6 * num_mixture)
        targets (torch.Tensor): 真实的未来轨迹序列, shape (B, T_pred_agg, TARGET_DIM)
        model: MDN模型实例 (需要有 get_mixture_params 和 num_mixture 属性)
    Returns:
        torch.Tensor: 标量损失值。
    """
    batch_size, pred_steps, _ = targets.shape
    # 确保能够访问到原始模型 (如果使用了 torch.compile)
    if hasattr(model, '_orig_mod'): 
        model_ref = model._orig_mod
    else: 
        model_ref = model
    
    # 关键修复：检查新的、功能明确的接口是否存在
    if not hasattr(model_ref, 'get_trajectory_mixture_params'):
         raise AttributeError("Model must have 'get_trajectory_mixture_params' for MDN loss.")

    # 展平以便于MDN参数解析和损失计算
    outputs_flat = outputs.reshape(batch_size * pred_steps, -1)
    targets_flat = targets.reshape(batch_size * pred_steps, TARGET_DIM)

    # 关键修复：调用新的、专门用于轨迹的接口
    pi, mu_x, mu_y, sigma_x, sigma_y, rho = model_ref.get_trajectory_mixture_params(outputs_flat)
    
    x = targets_flat[:, 0].unsqueeze(1).expand(-1, model_ref.num_mixture)
    y = targets_flat[:, 1].unsqueeze(1).expand(-1, model_ref.num_mixture)

    # 确保 sigma 和 rho 的数值稳定性
    sigma_x_stable = torch.clamp(sigma_x, min=1e-8) # 确保 sigma_x 严格为正
    sigma_y_stable = torch.clamp(sigma_y, min=1e-8) # 确保 sigma_y 严格为正
    rho_stable = torch.clamp(rho, min=-0.99999, max=0.99999) # 更严格地限制 rho 范围

    dx = (x - mu_x) / sigma_x_stable
    dy = (y - mu_y) / sigma_y_stable
    
    # 计算指数项
    z = dx**2 + dy**2 - 2 * rho_stable * dx * dy
    denom = 1 - rho_stable**2

    # 避免分母为零或负数，增加数值稳定性
    log_exp_term = -0.5 * z / (denom + 1e-12) # 将 1e-8 改为 1e-12，更小的值

    # 计算归一化常数
    log_2pi = math.log(2 * np.pi)
    log_sigma_prod = torch.log(sigma_x_stable) + torch.log(sigma_y_stable)
    # log_sqrt_denom = 0.5 * torch.log(denom + 1e-8) # 确保 denom 也是正数
    log_sqrt_denom = 0.5 * torch.log(denom + 1e-12) # 同样使用 1e-12

    log_norm_const = log_2pi + log_sigma_prod + log_sqrt_denom

    # 计算每个混合成分的对数概率密度
    log_prob_comp = log_exp_term - log_norm_const

    # 计算带混合权重的对数概率 (log-sum-exp for numerical stability)
    log_pi = torch.log(pi + 1e-12) # 确保pi非零，使用1e-12
    log_weighted_prob = log_pi + log_prob_comp
    log_total_prob = torch.logsumexp(log_weighted_prob, dim=1)
    
    # 负对数似然损失
    loss = -torch.mean(log_total_prob)

    if torch.isnan(loss) or torch.isinf(loss):
        logging.warning("Warning: NaN or Inf loss detected! Returning large value.")
        return torch.tensor(1e6, device=outputs.device, requires_grad=True)
    return loss
