#!/usr/bin/env python3
"""
轨迹预测评估指标
"""

import numpy as np
import torch

def calculate_ade(predicted_trajectory, ground_truth_trajectory):
    """
    计算平均位移误差 (Average Displacement Error)
    
    Args:
        predicted_trajectory: 预测轨迹 (seq_len, 2)
        ground_truth_trajectory: 真实轨迹 (seq_len, 2)
    
    Returns:
        float: ADE值
    """
    if isinstance(predicted_trajectory, torch.Tensor):
        predicted_trajectory = predicted_trajectory.cpu().numpy()
    if isinstance(ground_truth_trajectory, torch.Tensor):
        ground_truth_trajectory = ground_truth_trajectory.cpu().numpy()
    
    # 计算每个时间步的欧几里得距离
    distances = np.sqrt(np.sum((predicted_trajectory - ground_truth_trajectory) ** 2, axis=1))
    
    # 返回平均距离
    return np.mean(distances)

def calculate_fde(predicted_trajectory, ground_truth_trajectory):
    """
    计算最终位移误差 (Final Displacement Error)
    
    Args:
        predicted_trajectory: 预测轨迹 (seq_len, 2)
        ground_truth_trajectory: 真实轨迹 (seq_len, 2)
    
    Returns:
        float: FDE值
    """
    if isinstance(predicted_trajectory, torch.Tensor):
        predicted_trajectory = predicted_trajectory.cpu().numpy()
    if isinstance(ground_truth_trajectory, torch.Tensor):
        ground_truth_trajectory = ground_truth_trajectory.cpu().numpy()
    
    # 计算最后一个时间步的欧几里得距离
    final_distance = np.sqrt(np.sum((predicted_trajectory[-1] - ground_truth_trajectory[-1]) ** 2))
    
    return final_distance

def calculate_batch_metrics(predicted_trajectories, ground_truth_trajectories):
    """
    批量计算ADE和FDE
    
    Args:
        predicted_trajectories: 预测轨迹批次 (batch_size, seq_len, 2)
        ground_truth_trajectories: 真实轨迹批次 (batch_size, seq_len, 2)
    
    Returns:
        dict: 包含ADE和FDE列表的字典
    """
    if isinstance(predicted_trajectories, torch.Tensor):
        predicted_trajectories = predicted_trajectories.cpu().numpy()
    if isinstance(ground_truth_trajectories, torch.Tensor):
        ground_truth_trajectories = ground_truth_trajectories.cpu().numpy()
    
    batch_size = predicted_trajectories.shape[0]
    ade_list = []
    fde_list = []
    
    for i in range(batch_size):
        ade = calculate_ade(predicted_trajectories[i], ground_truth_trajectories[i])
        fde = calculate_fde(predicted_trajectories[i], ground_truth_trajectories[i])
        ade_list.append(ade)
        fde_list.append(fde)
    
    return {
        'ade': ade_list,
        'fde': fde_list,
        'avg_ade': np.mean(ade_list),
        'avg_fde': np.mean(fde_list)
    }

def calculate_trajectory_length(trajectory):
    """
    计算轨迹长度
    
    Args:
        trajectory: 轨迹 (seq_len, 2)
    
    Returns:
        float: 轨迹总长度
    """
    if isinstance(trajectory, torch.Tensor):
        trajectory = trajectory.cpu().numpy()
    
    # 计算相邻点之间的距离
    distances = np.sqrt(np.sum(np.diff(trajectory, axis=0) ** 2, axis=1))
    
    return np.sum(distances)

def calculate_speed_profile(trajectory, time_interval=1.0):
    """
    计算轨迹的速度剖面
    
    Args:
        trajectory: 轨迹 (seq_len, 2)
        time_interval: 时间间隔（秒）
    
    Returns:
        np.ndarray: 速度序列
    """
    if isinstance(trajectory, torch.Tensor):
        trajectory = trajectory.cpu().numpy()
    
    # 计算相邻点之间的距离
    distances = np.sqrt(np.sum(np.diff(trajectory, axis=0) ** 2, axis=1))
    
    # 计算速度 (距离/时间)
    speeds = distances / time_interval
    
    return speeds

def calculate_direction_changes(trajectory):
    """
    计算轨迹的方向变化
    
    Args:
        trajectory: 轨迹 (seq_len, 2)
    
    Returns:
        np.ndarray: 方向变化角度序列（弧度）
    """
    if isinstance(trajectory, torch.Tensor):
        trajectory = trajectory.cpu().numpy()
    
    if len(trajectory) < 3:
        return np.array([])
    
    # 计算相邻线段的方向向量
    vectors = np.diff(trajectory, axis=0)
    
    # 计算相邻向量之间的角度
    angles = []
    for i in range(len(vectors) - 1):
        v1 = vectors[i]
        v2 = vectors[i + 1]
        
        # 计算角度
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
        cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
        angle = np.arccos(cos_angle)
        angles.append(angle)
    
    return np.array(angles)

def evaluate_prediction_quality(predicted_trajectory, ground_truth_trajectory, 
                              time_interval=1.0, detailed=False):
    """
    综合评估预测质量
    
    Args:
        predicted_trajectory: 预测轨迹 (seq_len, 2)
        ground_truth_trajectory: 真实轨迹 (seq_len, 2)
        time_interval: 时间间隔（秒）
        detailed: 是否返回详细指标
    
    Returns:
        dict: 评估指标字典
    """
    # 基础指标
    ade = calculate_ade(predicted_trajectory, ground_truth_trajectory)
    fde = calculate_fde(predicted_trajectory, ground_truth_trajectory)
    
    metrics = {
        'ade': ade,
        'fde': fde
    }
    
    if detailed:
        # 轨迹长度比较
        pred_length = calculate_trajectory_length(predicted_trajectory)
        gt_length = calculate_trajectory_length(ground_truth_trajectory)
        
        # 速度剖面比较
        pred_speeds = calculate_speed_profile(predicted_trajectory, time_interval)
        gt_speeds = calculate_speed_profile(ground_truth_trajectory, time_interval)
        
        # 方向变化比较
        pred_directions = calculate_direction_changes(predicted_trajectory)
        gt_directions = calculate_direction_changes(ground_truth_trajectory)
        
        metrics.update({
            'predicted_length': pred_length,
            'ground_truth_length': gt_length,
            'length_ratio': pred_length / (gt_length + 1e-8),
            'avg_predicted_speed': np.mean(pred_speeds) if len(pred_speeds) > 0 else 0,
            'avg_ground_truth_speed': np.mean(gt_speeds) if len(gt_speeds) > 0 else 0,
            'avg_direction_change_pred': np.mean(pred_directions) if len(pred_directions) > 0 else 0,
            'avg_direction_change_gt': np.mean(gt_directions) if len(gt_directions) > 0 else 0
        })
    
    return metrics
