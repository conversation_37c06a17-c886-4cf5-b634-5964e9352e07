"""
训练脚本: V4 Attention with Relative Goal Embedding

功能:
- 训练一个基于注意力机制的自回归 MDN 轨迹预测器。
- 使用相对目标信息（相对坐标、距离）并通过 MLP 进行编码，然后与解码器状态进行注意力交互。
- 支持固定长度和可变长度预测训练。
- 脚本会自动检测输入数据维度，如果包含嵌入（34维），会屏蔽嵌入部分，只使用基础特征（26维）送入编码器。

输入:
- 预处理好的轨迹数据 (.npz 文件)，包含历史轨迹 (X_history)、未来轨迹 (y_future) 和 **真实目标点坐标 (true_goals)**。
  - 预期加载包含嵌入的数据 (34维特征)，脚本内部会处理。
- 包含标准化参数的 JSON 文件。
- 命令行参数指定的配置 (包括 goal_embedding_dim)。

输出:
- 训练好的模型检查点 (.pth 文件)。
- 训练和验证损失历史记录 (.json 文件)。
- 训练过程的损失曲线图 (.png 文件)。
- 训练总结日志 (追加到 models/training_summary_log.txt)。

核心方法:
- 使用 GRU 对历史轨迹的基础特征进行编码。
- 计算历史轨迹最后一点相对于真实目标点的 **相对坐标和距离**。
- 使用一个 **MLP (RelativeGoalEncoderMLP)** 对这些相对目标特征进行编码，得到目标嵌入。
- 解码器在每个时间步使用其当前隐藏状态作为查询（Query），编码器的所有时间步输出作为键（Key）和值（Value），计算 **注意力权重**。
- 将注意力加权的编码器输出与目标嵌入结合，融入解码过程。
- 每步预测未来位置的 MDN 参数。
- 使用 MDN 负对数似然损失进行训练。
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import json
import os
import math
import torch.nn.functional as F # Needed for Attention softmax
from tqdm import tqdm
import argparse
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import sys
import random # For teacher forcing
import datetime # <-- 添加导入
from torch.optim.lr_scheduler import ReduceLROnPlateau # Add import
import pickle # 添加导入

print("--- Script train_predictor_v4_attention_relative_goal.py started ---") # Updated script name
print(f"Raw arguments received: {sys.argv}")

# New: A simple dataset for passing indices to DataLoader in variable length mode
# class IndexDataset(Dataset):
#     def __init__(self, num_samples):
#         self.num_samples = num_samples

#     def __len__(self):
#         return self.num_samples

#     def __getitem__(self, idx):
#         return idx # Simply return the index

# GoalProcessor might not be needed if data has true_goals

"""
模块说明：(方案 V4: 注意力 + 相对目标)
训练自回归混合密度网络 (MDN) 模型用于轨迹预测。
使用Encoder-Decoder GRU架构，加入注意力机制和相对目标嵌入。

功能:
- 训练一个基于注意力机制的自回归 MDN 轨迹预测器。
- 使用相对目标信息（相对坐标、距离）并通过 MLP 进行编码，然后与解码器状态进行注意力交互。
- 支持固定长度和可变长度预测训练。
- 脚本会自动检测输入数据维度，如果包含嵌入（34维），会屏蔽嵌入部分，只使用基础特征（26维）送入编码器。

输入:
- 预处理好的轨迹数据 (.npz 文件)，包含历史轨迹 (X_history)、未来轨迹 (y_future) 和 **真实目标点坐标 (true_goals)**。
  - 预期加载包含嵌入的数据 (34维特征)，脚本内部会处理。
- 包含标准化参数的 JSON 文件。
- 命令行参数指定的配置 (包括 goal_embedding_dim)。

输出:
- 训练好的模型检查点 (.pth 文件)。
- 训练和验证损失历史记录 (.json 文件)。
- 训练过程的损失曲线图 (.png 文件)。
- 训练总结日志 (追加到 models/training_summary_log.txt)。

核心方法:
- 使用 GRU 对历史轨迹的基础特征进行编码。
- 计算历史轨迹最后一点相对于真实目标点的 **相对坐标和距离**。
- 使用一个 **MLP (RelativeGoalEncoderMLP)** 对这些相对目标特征进行编码，得到目标嵌入。
- 解码器在每个时间步使用其当前隐藏状态作为查询（Query），编码器的所有时间步输出作为键（Key）和值（Value），计算 **注意力权重**。
- 将注意力加权的编码器输出与目标嵌入结合，融入解码过程。
- 每步预测未来位置的 MDN 参数。
- 使用 MDN 负对数似然损失进行训练。
"""
# --- Constants ---
TARGET_DIM = 2
GOAL_DIM = 2 
# --- 新增: 定义输入维度常量 ---
INPUT_FEATURES_BASE = 22  # V1模型轨迹特征维度，与LMDB数据一致 (之前是26，现在是22)
EXPECTED_EMBEDDED_DIM = 22 # 与 INPUT_FEATURES_BASE 保持一致，因为V4将直接使用LMDB的轨迹特征，不再处理额外的嵌入。
# --- 添加缺失常量 ---
REL_GOAL_FEATURE_DIM = 3  # dx, dy, dist (改回3，因为forward中会计算dist)
GOAL_EMBEDDING_DIM = 8    # 目标嵌入维度
NUM_MIXTURE = 3           # 混合密度成分数量
# --------------------------

# --- Configuration Function (Adjusted for V4 to use LMDB) ---
def get_config(pred_minutes=40, min_pred_minutes=None, max_pred_minutes=None, batch_size=256): # 默认值适配LMDB预处理
    """
    返回模型和训练配置 (适配LMDB数据)
    """
    global PRED_STEPS
    config = {}

    # V4模型使用LMDB数据，不再有_embedded后缀
    # 数据路径直接指向我们预处理的LMDB目录
    base_lmdb_path = "data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq"
    norm_file_name = "normalization_stats.pkl"

    variable_length = False # V4暂时只支持固定长度，因为LMDB预处理是固定长度的

    # 确定预测步长 (固定长度模式)
    PRED_STEPS = int(pred_minutes * 60 / 10) # 40分钟 -> 240步
    length_status = f"{pred_minutes}min"

    model_save_dir = f"models/autoregressive_v4_attn_relgoal_mdn_{length_status}"

    config.update({
        "train_data_path": os.path.join(base_lmdb_path, "train"),
        "val_data_path": os.path.join(base_lmdb_path, "val"),
        "test_data_path": "", # V4目前没有测试集概念，或后续再处理
        "norm_path": os.path.join(base_lmdb_path, norm_file_name),
        "model_save_dir": model_save_dir,
        "model_save_name": "best_model.pth",
        "pred_minutes": pred_minutes,
        "pred_steps": PRED_STEPS,
        "variable_length": variable_length, # 硬编码为False
        "batch_size": batch_size,
        "learning_rate": 1e-5, # 降低学习率以提高稳定性
        "num_epochs": 100, 
        "teacher_forcing_ratio": 0.5,
        "scheduler_factor": 0.1,
        "scheduler_patience": 5,
        "early_stopping_patience": 10,
        "gradient_accumulation_steps": 2
    })

    # Common config
    config.update({
        "hidden_size": 128,
        "num_layers": 2,
        "num_mixture": NUM_MIXTURE,
        "output_params": 6 * NUM_MIXTURE,
        "decoder_input_dim": TARGET_DIM,
        "attention_size": 64,
        "goal_mlp_input_dim": REL_GOAL_FEATURE_DIM,
        "goal_mlp_hidden_dim": 32,
        "goal_embedding_dim": GOAL_EMBEDDING_DIM,
        "device": "cuda" if torch.cuda.is_available() else "cpu",
        "seed": 42,
        "dropout_prob": 0.2
    })

    return config

# --- Dataset Definition (Adapted to use LMDBDataset) ---
from src.data.datasets import LMDBDataset # 导入LMDBDataset

# 移除原来的 TrajectoryDataset 类定义，因为它不再被使用
# class TrajectoryDataset(Dataset):
#    ...


# --- V4 Specific Modules ---
# class RelativeGoalEncoderMLP(nn.Module):
#     # MLP to encode relative goal information (dx, dy, dist)
#     def __init__(self, input_dim=REL_GOAL_FEATURE_DIM, embedding_dim=GOAL_EMBEDDING_DIM):
#         super().__init__()
#         self.mlp = nn.Sequential(
#             nn.Linear(input_dim, embedding_dim * 2),
#             nn.ReLU(),
#             nn.Linear(embedding_dim * 2, embedding_dim)
#         )

#     def forward(self, relative_goal_features):
#         return self.mlp(relative_goal_features)

# class Attention(nn.Module):
#     # Bahdanau-style attention
#     def __init__(self, encoder_hidden_dim, decoder_hidden_dim):
#         super().__init__()
#         # Adjust W dimension if enc/dec hidden sizes differ
#         self.attn_W = nn.Linear(encoder_hidden_dim + decoder_hidden_dim, decoder_hidden_dim, bias=False)
#         self.attn_v = nn.Linear(decoder_hidden_dim, 1, bias=False)

#     def forward(self, decoder_hidden_last_layer, encoder_outputs):
#         batch_size = encoder_outputs.shape[0]
#         hist_len = encoder_outputs.shape[1]
#         decoder_hidden_repeated = decoder_hidden_last_layer.unsqueeze(1).repeat(1, hist_len, 1)
#         energy_input = torch.cat((decoder_hidden_repeated, encoder_outputs), dim=2)
#         energy = torch.tanh(self.attn_W(energy_input))
#         attention_scores = self.attn_v(energy).squeeze(2)
#         alpha = F.softmax(attention_scores, dim=1)
#         alpha_unsqueezed = alpha.unsqueeze(1)
#         context_vector = torch.bmm(alpha_unsqueezed, encoder_outputs).squeeze(1)
#         return context_vector, alpha


# --- AutoregressiveMDNPredictorModel for V4 (Based on baseline template) ---
# class AutoregressiveMDNPredictorModelV4(nn.Module):
#     def __init__(self, input_size, decoder_input_dim, hidden_size, num_layers, num_mixture, goal_embedding_dim, dropout_prob=0.2, norm_params=None):
#         super().__init__()
#         self.num_mixture = num_mixture
#         self.encoder_hidden_size = hidden_size
#         self.decoder_hidden_size = hidden_size
#         self.num_layers = num_layers
#         self.output_params_dim = 6 * num_mixture
#         self.goal_dim = GOAL_DIM
#         self.target_dim = TARGET_DIM # Should be same as decoder_input_dim
#         self.norm_params = norm_params # Store norm_params

#         # --- Add check for norm_params ---
#         if self.norm_params is None or not all(k in self.norm_params for k in ['target_mean', 'target_std']):
#             raise ValueError("AutoregressiveMDNPredictorModelV4 requires norm_params with 'target_mean' and 'target_std'")
#         # ---------------------------------

#         # Encoder GRU
#         self.encoder_rnn = nn.GRU(input_size, self.encoder_hidden_size, num_layers,
#                                   batch_first=True, dropout=dropout_prob if num_layers > 1 else 0)

#         # Relative Goal Embedding MLP
#         # Input dim is REL_GOAL_FEATURE_DIM (3), assuming normalized inputs
#         self.goal_embedder = RelativeGoalEncoderMLP(input_dim=REL_GOAL_FEATURE_DIM, embedding_dim=goal_embedding_dim)

#         # Attention Mechanism
#         self.attention = Attention(self.encoder_hidden_size, self.decoder_hidden_size)

#         # Decoder GRU
#         # Input: Previous Output (target_dim) + Context Vector (encoder_hidden_size) + Dynamic Goal Embedding (goal_embedding_dim)
#         decoder_rnn_input_dim = self.target_dim + self.encoder_hidden_size + goal_embedding_dim
#         self.decoder_rnn = nn.GRU(decoder_rnn_input_dim, self.decoder_hidden_size, num_layers,
#                                   batch_first=True, dropout=dropout_prob if num_layers > 1 else 0)

#         # Output FC layer
#         self.decoder_fc = nn.Linear(self.decoder_hidden_size, self.output_params_dim)

#     def forward(self, x_history, y_future_truth, true_goals, pred_steps, teacher_forcing_ratio=0.5):
#         """
#         V4 Forward Pass.
#         Args:
#             x_history: (batch, hist_len, input_size) - Normalized
#             y_future_truth: (batch, pred_steps, target_dim) - Normalized
#             true_goals: (batch, goal_dim) - UNNORMALIZED
#             pred_steps: int
#             teacher_forcing_ratio: float
#         Returns:
#             outputs: (batch, pred_steps, output_params_dim) - Normalized scale
#         """
#         batch_size = x_history.size(0)
#         device = x_history.device

#         # --- Retrieve norm_params ---
#         target_mean = self.norm_params['target_mean'] # pylint: disable=unsubscriptable-object
#         target_std = self.norm_params['target_std'] # pylint: disable=unsubscriptable-object
#         # ----------------------------

#         # --- Normalize true_goals ---
#         # true_goals_normalized = (true_goals - target_mean) / (target_std + 1e-8) # LMDB中已归一化，移除此处二次归一化
#         true_goals_normalized = true_goals # 直接使用已归一化的 true_goals
#         # ----------------------------

#         # --- 1. Encoding ---
#         encoder_outputs, encoder_hidden = self.encoder_rnn(x_history)
#         # encoder_outputs: (batch, hist_len, hidden_size)
#         # encoder_hidden: (num_layers, batch, hidden_size)

#         # --- 2. Decoding Initialization ---
#         decoder_hidden = encoder_hidden # Initialize with encoder final state
        
#         # --- Initialize current_pos in NORMALIZED space ---
#         # Last observed position *is* normalized
#         last_obs_pos_normalized = x_history[:, -1, :self.target_dim]
#         current_pos_normalized = last_obs_pos_normalized
#         # -------------------------------------------------

#         # Initial input for the first decoding step (predicting offset in normalized space)
#         decoder_input_step_normalized = torch.zeros(batch_size, self.target_dim, device=device)

#         outputs = torch.zeros(batch_size, pred_steps, self.output_params_dim, device=device)

#         # --- 3. Autoregressive Decoding Loop ---
#         for t in range(pred_steps):
#             # --- a. Dynamic Relative Goal Features (IN NORMALIZED SPACE) ---
#             dx_goal_norm = true_goals_normalized[:, 0] - current_pos_normalized[:, 0]
#             dy_goal_norm = true_goals_normalized[:, 1] - current_pos_normalized[:, 1]
#             # Calculate distance in normalized space (scale might be distorted, but consistent)
#             dist_goal_norm = torch.sqrt(dx_goal_norm**2 + dy_goal_norm**2 + 1e-6)
#             relative_goal_features_norm = torch.stack((dx_goal_norm, dy_goal_norm, dist_goal_norm), dim=1)
#             # --------------------------------------------------------------

#             # --- b. Embed Relative Goal Features ---
#             dynamic_goal_embedding = self.goal_embedder(relative_goal_features_norm)

#             # --- c. Attention Context Vector ---
#             decoder_hidden_last_layer = decoder_hidden[-1]
#             context_vector, _ = self.attention(decoder_hidden_last_layer, encoder_outputs)

#             # --- d. Prepare Decoder RNN Input ---
#             decoder_input_step_unsqueezed = decoder_input_step_normalized.unsqueeze(1)
#             context_vector_unsqueezed = context_vector.unsqueeze(1)
#             dynamic_goal_embedding_unsqueezed = dynamic_goal_embedding.unsqueeze(1)
#             rnn_input = torch.cat((decoder_input_step_unsqueezed,
#                                    context_vector_unsqueezed,
#                                    dynamic_goal_embedding_unsqueezed), dim=2)

#             # --- e. Decoder RNN Step ---
#             decoder_output, decoder_hidden = self.decoder_rnn(rnn_input, decoder_hidden)

#             # --- f. Get MDN Parameters ---
#             mdn_params_t = self.decoder_fc(decoder_output.squeeze(1))
#             outputs[:, t, :] = mdn_params_t

#             # --- g. Determine Next Input & Update NORMALIZED Position ---
#             use_teacher_forcing = random.random() < teacher_forcing_ratio
#             pi, mu_x_norm, mu_y_norm, _, _, _ = self.get_mixture_params(mdn_params_t)
#             _, best_k = torch.max(pi, dim=1)
#             mu_x_best_norm = mu_x_norm[torch.arange(batch_size), best_k]
#             mu_y_best_norm = mu_y_norm[torch.arange(batch_size), best_k]
#             # Predicted offset is in NORMALIZED space
#             predicted_offset_normalized = torch.stack([mu_x_best_norm, mu_y_best_norm], dim=1)

#             if use_teacher_forcing and y_future_truth is not None and t < y_future_truth.size(1):
#                 # True offset is already NORMALIZED
#                 next_input_offset_normalized = y_future_truth[:, t, :]
#                 # Update NORMALIZED position based on true NORMALIZED offset
#                 current_pos_normalized = current_pos_normalized + next_input_offset_normalized
#             else:
#                 # Use predicted NORMALIZED offset
#                 next_input_offset_normalized = predicted_offset_normalized.detach()
#                 # Update NORMALIZED position based on predicted NORMALIZED offset
#                 current_pos_normalized = current_pos_normalized + next_input_offset_normalized
            
#             # Input for *next* RNN step is the NORMALIZED offset
#             decoder_input_step_normalized = next_input_offset_normalized

#         return outputs

#     def get_mixture_params(self, mdn_params):
#         """将单步的网络输出分离成各个混合高斯参数"""
#         batch_size = mdn_params.shape[0]
#         params = mdn_params.reshape(batch_size, self.num_mixture, 6)
#         pi_logits, mu_x, mu_y, log_sigma_x, log_sigma_y, rho_tanh = \
#             params[:, :, 0], params[:, :, 1], params[:, :, 2], params[:, :, 3], params[:, :, 4], params[:, :, 5]
#         pi = torch.softmax(pi_logits, dim=1)
#         sigma_x = torch.exp(log_sigma_x)
#         sigma_y = torch.exp(log_sigma_y)
#         rho = torch.tanh(rho_tanh)
#         return pi, mu_x, mu_y, sigma_x, sigma_y, rho


# --- MDN Loss Function (Robust version from V4/V5) ---
# def autoregressive_mdn_nll_loss(outputs, targets, model):
#     """
#     计算自回归模型在整个序列上的混合高斯分布负对数似然损失
#     Args:
#         outputs (torch.Tensor): 模型预测的MDN参数序列, shape (B, T_pred_agg, 6 * num_mixture)
#         targets (torch.Tensor): 真实的未来轨迹序列, shape (B, T_pred_agg, TARGET_DIM)
#         model: MDN模型实例 (需要有 get_mixture_params 和 num_mixture 属性)
#     Returns:
#         torch.Tensor: 标量损失值。
#     """
#     batch_size, pred_steps, _ = targets.shape
#     if hasattr(model, '_orig_mod'): model_ref = model._orig_mod
#     else: model_ref = model
#     if not hasattr(model_ref, 'get_mixture_params') or not hasattr(model_ref, 'num_mixture'):
#          raise AttributeError("Model must have 'get_mixture_params' and 'num_mixture' for MDN loss.")

#     outputs_flat = outputs.reshape(batch_size * pred_steps, -1)
#     targets_flat = targets.reshape(batch_size * pred_steps, TARGET_DIM)

#     pi, mu_x, mu_y, sigma_x, sigma_y, rho = model_ref.get_mixture_params(outputs_flat)
    
#     # Add small epsilon to sigmas and clamp rho more strictly to avoid numerical issues
#     sigma_x_stable = sigma_x + 1e-6
#     sigma_y_stable = sigma_y + 1e-6
#     rho_stable = torch.clamp(rho, min=-0.999, max=0.999) # Slightly stricter clamp

#     x = targets_flat[:, 0].unsqueeze(1).expand(-1, model_ref.num_mixture)
#     y = targets_flat[:, 1].unsqueeze(1).expand(-1, model_ref.num_mixture)
    
#     dx = (x - mu_x) / sigma_x_stable
#     dy = (y - mu_y) / sigma_y_stable
    
#     z = dx**2 + dy**2 - 2 * rho_stable * dx * dy
#     denom = 1 - rho_stable**2

#     log_2pi = math.log(2 * np.pi)
#     log_sigma_prod = torch.log(sigma_x_stable) + torch.log(sigma_y_stable)
#     log_sqrt_denom = 0.5 * torch.log(denom + 1e-8) # Add epsilon here too
#     log_norm_const = log_2pi + log_sigma_prod + log_sqrt_denom
#     log_exp_term = -0.5 * z / (denom + 1e-8) # Add epsilon here too
#     log_prob_comp = log_exp_term - log_norm_const
#     log_pi = torch.log(pi + 1e-8) # Add epsilon here too
#     log_weighted_prob = log_pi + log_prob_comp
#     log_total_prob = torch.logsumexp(log_weighted_prob, dim=1)
#     loss = -torch.mean(log_total_prob)

#     if torch.isnan(loss) or torch.isinf(loss):
#         print("Warning: NaN or Inf loss detected!")
#         # Debugging prints for intermediate values
#         print(f"DEBUG_LOSS: loss={loss.item()}")
#         print(f"DEBUG_LOSS: pi_min={pi.min().item():.6f}, pi_max={pi.max().item():.6f}")
#         print(f"DEBUG_LOSS: mu_x_min={mu_x.min().item():.6f}, mu_x_max={mu_x.max().item():.6f}")
#         print(f"DEBUG_LOSS: mu_y_min={mu_y.min().item():.6f}, mu_y_max={mu_y.max().item():.6f}")
#         print(f"DEBUG_LOSS: sigma_x_min={sigma_x.min().item():.6f}, sigma_x_max={sigma_x.max().item():.6f}")
#         print(f"DEBUG_LOSS: sigma_y_min={sigma_y.min().item():.6f}, sigma_y_max={sigma_y.max().item():.6f}")
#         print(f"DEBUG_LOSS: rho_min={rho.min().item():.6f}, rho_max={rho.max().item():.6f}")
#         print(f"DEBUG_LOSS: denom_min={denom.min().item():.6f}, denom_max={denom.max().item():.6f}")
#         print(f"DEBUG_LOSS: log_prob_comp_min={log_prob_comp.min().item():.6f}, log_prob_comp_max={log_prob_comp.max().item():.6f}")
#         print(f"DEBUG_LOSS: log_total_prob_min={log_total_prob.min().item():.6f}, log_total_prob_max={log_total_prob.max().item():.6f}")
        
#         # Return a large finite loss to prevent AMP issues and continue debugging
#         return torch.tensor(1e6, device=outputs.device, requires_grad=True)
#     return loss


# --- Train Function (Adapted for V4 Model and Data) ---
def train(config):
    torch.manual_seed(config["seed"])
    np.random.seed(config["seed"])
    if config["device"] == "cuda":
        torch.cuda.manual_seed(config["seed"])

    device = torch.device(config["device"])
    print(f"Using device: {device}")

    scaler = None
    # if config["device"] == "cuda": # 暂时禁用AMP进行调试
    #     scaler = torch.amp.GradScaler(device='cuda') # pylint: disable=no-member
    #     print("启用自动混合精度 (AMP)")
    # else:
    #     print("AMP not enabled (not using CUDA).")
    print("AMP 已暂时禁用进行调试。")

    variable_length = config["variable_length"]
    if variable_length:
        min_pred_steps = config["min_pred_minutes"]
        max_pred_steps = config["max_pred_minutes"]
        print(f"使用可变长度预测模式: [{min_pred_steps}-{max_pred_steps}] 步")

    zero_out_embedding = config.get('zero_out_embedding', False)
    if zero_out_embedding:
        print("警告: 将在训练和验证期间将输入特征的嵌入部分置零 (仅当使用34维数据时有效)。")

    # 1. Load Dataset (Now requires true_goals)
    try:
        train_dataset = LMDBDataset(
            config["train_data_path"] # 仅传入lmdb_path
        )
        val_dataset = LMDBDataset(
            config["val_data_path"] # 仅传入lmdb_path
        )
    except Exception as e:
        print(f"Error loading dataset for V4: {e}")
        print(f"Ensure data at {config['train_data_path']} and {config['val_data_path']} contains the 'true_goals' key.")
        return None

    # --- 确定模型的实际输入大小 (直接使用预定义常量) ---
    # input_size = train_dataset.feature_dim # 移除此行
    input_size = INPUT_FEATURES_BASE # 直接使用常量
    print(f"确定的模型输入维度: {input_size}")
    if input_size != EXPECTED_EMBEDDED_DIM:
        print(f"警告: V4 期望输入维度 {EXPECTED_EMBEDDED_DIM}, 但加载的数据维度为 {input_size}。确保加载了正确的嵌入数据。")
    # -----------------------------------------------------

    # DataLoaders setup (Needs true_goals)
    if not variable_length:
        train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], shuffle=True, num_workers=4, pin_memory=True)
        val_loader = DataLoader(val_dataset, batch_size=config["batch_size"], shuffle=False, num_workers=4, pin_memory=True)
    else:
        train_indices_loader = DataLoader(IndexDataset(len(train_dataset)), batch_size=config["batch_size"], shuffle=True) # Used IndexDataset
        val_indices_loader = DataLoader(IndexDataset(len(val_dataset)), batch_size=config["batch_size"], shuffle=False) # Used IndexDataset

    print(f"Train samples: {len(train_dataset)}, Validation samples: {len(val_dataset)}")

    # --- Load Normalization Params for Model ---
    try:
        with open(config["norm_path"], 'rb') as f: # 修改为 'rb' 模式
            norm_params_dict = pickle.load(f) # 修改为 pickle.load
        # 转换为Tensor并移动到设备
        norm_params_tensors = {
            'target_mean': torch.tensor([norm_params_dict['target_x_mean'], norm_params_dict['target_y_mean']], device=device, dtype=torch.float32), # 修正：目标x,y的均值
            'target_std': torch.tensor([norm_params_dict['target_x_std'], norm_params_dict['target_y_std']], device=device, dtype=torch.float32), # 修正：目标x,y的标准差
        }
        # 确保std不为零
        norm_params_tensors['target_std'][norm_params_tensors['target_std'] < 1e-8] = 1.0
        print(f"Normalization parameters loaded for model from: {config['norm_path']}")
    except Exception as e:
        print(f"Error loading normalization parameters for model: {e}")
        return None
    # -----------------------------------------

    # --- Initialize Model, Optimizer, and Load Checkpoint if exists --- 
    model_save_path = os.path.join(config["model_save_dir"], config["model_save_name"])
    os.makedirs(config["model_save_dir"], exist_ok=True)

    model_input_dim = INPUT_FEATURES_BASE if input_size == EXPECTED_EMBEDDED_DIM else input_size
    print(f"模型将使用输入维度: {model_input_dim}")
    # model = AutoregressiveMDNPredictorModelV4(
    #     input_size=model_input_dim, # 使用计算出的维度
    #     decoder_input_dim=config["decoder_input_dim"],
    #     hidden_size=config["hidden_size"],
    #     num_layers=config["num_layers"],
    #     num_mixture=config["num_mixture"],
    #     goal_embedding_dim=config["goal_embedding_dim"], # V4 requires this
    #     dropout_prob=config["dropout_prob"],
    #     norm_params=norm_params_tensors
    # ).to(device)
    
    optimizer = optim.Adam(model.parameters(), lr=config["learning_rate"]) # pylint: disable=no-name-in-module
    scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=config["scheduler_factor"], 
                                patience=config["scheduler_patience"], verbose=True) # pylint: disable=bad-option-value, useless-suppression

    start_epoch = 0
    best_val_loss = float('inf')

    if os.path.exists(model_save_path):
        print(f"加载检查点: {model_save_path}")
        try:
            checkpoint = torch.load(model_save_path, map_location=device)
            # Handle potential compiled model state dict mismatch
            if "_orig_mod." in list(checkpoint['model_state_dict'].keys())[0]:
                 unwrapped_state_dict = {k.replace("_orig_mod.", ""): v 
                                         for k, v in checkpoint['model_state_dict'].items()}
                 model.load_state_dict(unwrapped_state_dict)
            else:
                model.load_state_dict(checkpoint['model_state_dict'])
            
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            best_val_loss = checkpoint.get('val_loss', float('inf'))
            start_epoch = checkpoint.get('epoch', -1) + 1
            print(f"模型和优化器状态已加载。将从 epoch {start_epoch} 继续训练。")
            print(f"已加载的最佳验证损失: {best_val_loss:.4f}")
        except Exception as e:
            print(f"加载检查点失败: {e}。将从头开始训练。")
            start_epoch = 0
            best_val_loss = float('inf')
            # 重新初始化模型，确保传递 norm_params
            # model = AutoregressiveMDNPredictorModelV4(
            #     input_size=model_input_dim, # 使用计算出的维度
            #     decoder_input_dim=config["decoder_input_dim"],
            #     hidden_size=config["hidden_size"],
            #     num_layers=config["num_layers"],
            #     num_mixture=config["num_mixture"],
            #     goal_embedding_dim=config["goal_embedding_dim"], 
            #     dropout_prob=config["dropout_prob"], 
            #     norm_params=norm_params_tensors # 传递 norm_params
            # ).to(device)
            optimizer = optim.Adam(model.parameters(), lr=config["learning_rate"]) # pylint: disable=no-name-in-module
            scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=config["scheduler_factor"], 
                                        patience=config["scheduler_patience"], verbose=True) # pylint: disable=bad-option-value, useless-suppression
    else:
        print(f"未找到检查点 {model_save_path}。将从头开始训练。")

    # --- Apply torch.compile AFTER potentially loading state dict ---
    # try:
    #     print("尝试应用 torch.compile...")
    #     model = torch.compile(model)
    #     print("torch.compile 应用成功！")
    # except Exception as e:
    #     print(f"警告: torch.compile 应用失败: {e}。将继续而不进行编译。")
    # --------------------------------------------------------------

    accumulation_steps = config.get("gradient_accumulation_steps", 1)

    # 4. Training Loop
    epochs_no_improve = 0
    teacher_forcing_ratio = config["teacher_forcing_ratio"]

    all_train_losses = []
    all_val_losses = []
    all_val_step_losses = []

    print(f"开始训练，从 epoch {start_epoch} 到 {config['num_epochs']} (保存至: {config['model_save_dir']})...")
    # --- Adjust loop range --- 
    for epoch in range(start_epoch, config["num_epochs"]):
        # --- Train --- 
        model.train()
        train_loss_epoch = 0.0

        if not variable_length:
            pred_steps = config["pred_steps"]
            progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']} [Train]")
            loader_len = len(train_loader)
        else:
            progress_bar = tqdm(train_indices_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']} [Train]")
            loader_len = len(train_indices_loader)

        for batch_idx, batch_content in enumerate(progress_bar):
            if not variable_length:
                # 从LMDBDataset批次中提取数据
                x_hist = batch_content['history'].to(device)
                y_future = batch_content['ground_truth_trajectory'].to(device) # LMDB中的未来轨迹
                true_goals = batch_content['ground_truth_destination'].to(device) # LMDB中的目标点
                
                # V4模型不使用 environment_roi_data，暂时不从batch中提取
                # environment_roi_data = batch_content['environment_roi'].to(device)

                # Zero out embedding if requested and applicable
                if zero_out_embedding and x_hist.shape[-1] == EXPECTED_EMBEDDED_DIM:
                    x_hist[:, :, 26:] = 0.0
            else:
                indices = batch_content
                # Ensure current_pred_steps is an int before arithmetic operations
                current_pred_steps = int(random.randint(min_pred_steps, max_pred_steps))
                # Unpack goal for variable length
                x_hist, y_future, true_goals = train_dataset.get_variable_length_batch(indices, current_pred_steps)
                pred_steps = current_pred_steps
                x_hist, y_future, true_goals = x_hist.to(device), y_future.to(device), true_goals.to(device)

            optimizer.zero_grad(set_to_none=True)

            # --- 修改: 切片输入特征 ---
            if input_size == EXPECTED_EMBEDDED_DIM:
                x_hist_input = x_hist[:, :, :INPUT_FEATURES_BASE]
            else:
                x_hist_input = x_hist
            # --------------------------

            # amp_enabled = scaler is not None and config["device"] == "cuda" # Ensure AMP is truly enabled
            # with torch.autocast(device_type=config["device"], dtype=torch.float16, enabled=amp_enabled):
            # mdn_params_sequence = model(x_hist_input, y_future, true_goals, pred_steps, teacher_forcing_ratio)
            # loss = autoregressive_mdn_nll_loss(mdn_params_sequence, y_future, model)

            # loss = loss / accumulation_steps # Scale loss

            # if amp_enabled and scaler is not None:
            #     scaler.scale(loss).backward()
            # else:
            loss.backward()
            
            loss = loss.item() / accumulation_steps
                
            train_loss_epoch += loss * accumulation_steps
            
            if (batch_idx + 1) % accumulation_steps == 0:
                # if amp_enabled and scaler is not None:
                #     scaler.step(optimizer)
                #     scaler.update()
                optimizer.step()
                optimizer.zero_grad(set_to_none=True)
            
            postfix_dict = {"loss": loss}
            if variable_length:
                postfix_dict["steps"] = pred_steps
            progress_bar.set_postfix(**postfix_dict)

        avg_train_loss = train_loss_epoch / loader_len
        all_train_losses.append(avg_train_loss)

        # --- Validate ---
        model.eval()
        val_loss_epoch = 0.0
        current_epoch_step_losses = {}
        current_epoch_step_losses['epoch'] = float(epoch + 1) # Explicitly set epoch as float

        with torch.no_grad():
            # amp_enabled = scaler is not None and config["device"] == "cuda"
            # with torch.autocast(device_type=config["device"], dtype=torch.float16, enabled=amp_enabled):
            if not variable_length:
                val_progress_bar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']} [Validate]")
                val_loader_len = len(val_loader)
                for batch_idx, batch_content in enumerate(val_progress_bar):
                    # 从LMDBDataset批次中提取数据
                    x_hist = batch_content['history'].to(device)
                    y_future = batch_content['ground_truth_trajectory'].to(device) # LMDB中的未来轨迹
                    true_goals = batch_content['ground_truth_destination'].to(device) # LMDB中的目标点

                    # V4模型不使用 environment_roi_data，暂时不从batch中提取
                    # environment_roi_data = batch_content['environment_roi'].to(device)
                    
                    if zero_out_embedding and x_hist.shape[-1] == EXPECTED_EMBEDDED_DIM:
                         x_hist[:, :, 26:] = 0.0
                    if input_size == EXPECTED_EMBEDDED_DIM:
                        x_hist_input = x_hist[:, :, :INPUT_FEATURES_BASE]
                    else:
                        x_hist_input = x_hist
                    # mdn_params_sequence = model(x_hist_input, y_future, true_goals, pred_steps, teacher_forcing_ratio=0.0)
                    # loss = autoregressive_mdn_nll_loss(mdn_params_sequence, y_future, model)
                    # current_loss = loss.item()
                    # val_loss_epoch += current_loss
                    # val_progress_bar.set_postfix(loss=f"{current_loss:.4f}")
                avg_val_loss = val_loss_epoch / val_loader_len if val_loader_len > 0 else float('inf')
            else: # Variable length validation
                min_p_steps = int(min_pred_steps) # Explicit cast
                max_p_steps = int(max_pred_steps) # Explicit cast
                val_steps = sorted(list(set([min_p_steps, (min_p_steps + max_p_steps) // 2, max_p_steps])))
                total_val_loss_sum = 0.0
                total_val_batches_count = 0
                for step in val_steps:
                    step_val_loss = 0.0
                    val_progress_bar = tqdm(val_indices_loader,
                                          desc=f"Epoch {epoch+1}/{config['num_epochs']} [Validate {step} steps]")
                    step_batches = 0
                    for batch_idx, indices in enumerate(val_progress_bar):
                        x_hist, y_future, true_goals = val_dataset.get_variable_length_batch(indices, step)
                        x_hist, y_future, true_goals = x_hist.to(device), y_future.to(device), true_goals.to(device)
                        if zero_out_embedding and x_hist.shape[-1] == EXPECTED_EMBEDDED_DIM:
                             x_hist[:, :, 26:] = 0.0
                        if input_size == EXPECTED_EMBEDDED_DIM:
                            x_hist_input = x_hist[:, :, :INPUT_FEATURES_BASE]
                        else:
                            x_hist_input = x_hist
                        # mdn_params_sequence = model(x_hist_input, y_future, true_goals, step, teacher_forcing_ratio=0.0)
                        # loss = autoregressive_mdn_nll_loss(mdn_params_sequence, y_future, model)
                        # current_loss = loss.item()
                        # step_val_loss += current_loss
                        # val_progress_bar.set_postfix(loss=f"{current_loss:.4f}")
                        step_batches += 1
                    if step_batches > 0:
                         step_avg_loss = step_val_loss / step_batches
                         current_epoch_step_losses[str(step)] = float(step_avg_loss) # Store as string key
                         print(f"Validate {step} steps avg loss: {step_avg_loss:.4f}")
                         total_val_loss_sum += step_avg_loss
                         total_val_batches_count += 1
                    else:
                         current_epoch_step_losses[str(step)] = float('inf') # Store as string key
                         print(f"Warning: No batches processed for validation step {step}")
                avg_val_loss = total_val_loss_sum / total_val_batches_count if total_val_batches_count > 0 else float('inf')

        all_val_losses.append(avg_val_loss)
        if variable_length:
            all_val_step_losses.append(current_epoch_step_losses)

        print(f"Epoch {epoch+1}/{config['num_epochs']} - Train Loss: {avg_train_loss:.4f}, Validation Loss: {avg_val_loss:.4f}")

        # --- Scheduler Step ---
        scheduler.step(avg_val_loss)

        # --- Early Stopping Check ---
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            epochs_no_improve = 0 # Reset counter
            model_state_dict_to_save = model.state_dict()
            if hasattr(model, '_orig_mod'):
                model_state_dict_to_save = model._orig_mod.state_dict()
            save_dict = {
                'epoch': epoch,
                'model_state_dict': model_state_dict_to_save,
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
                'config': config,
                'model_type': f'autoregressive_mdn_{config["model_save_dir"].split("/")[-1]}'
            }
            if variable_length:
                 save_dict['val_loss_per_step'] = current_epoch_step_losses
            torch.save(save_dict, model_save_path)
            print(f"Best model saved to {model_save_path} (Validation loss: {avg_val_loss:.4f})")
        else:
            epochs_no_improve += 1
            print(f"Validation loss did not improve for {epochs_no_improve} epoch(s).")
            if epochs_no_improve >= config["early_stopping_patience"]:
                print(f"Early stopping triggered after {epoch + 1} epochs.")
                break # Exit training loop

    # Save final logs
    loss_hist_path = os.path.join(config["model_save_dir"], "loss_history.json")
    try:
        with open(loss_hist_path, 'w') as f:
            json.dump({'train_losses': all_train_losses, 'val_losses': all_val_losses}, f, indent=2)
        print(f"Overall loss history saved to: {loss_hist_path}")
    except Exception as e:
        print(f"Error saving overall loss history: {e}")
    if variable_length and all_val_step_losses:
        step_loss_hist_path = os.path.join(config["model_save_dir"], "val_loss_per_step_history.json")
        try:
            with open(step_loss_hist_path, 'w') as f:
                json.dump(all_val_step_losses, f, indent=2)
            print(f"Validation step loss history saved to: {step_loss_hist_path}")
        except Exception as e:
            print(f"Error saving validation step loss history: {e}")

    # Plot loss curve (Fixed epoch range)
    plt.figure(figsize=(10, 6))
    actual_epochs = len(all_train_losses) # Get actual number of epochs run
    plt.plot(range(1, actual_epochs + 1), all_train_losses, label='Train Loss')
    plt.plot(range(1, actual_epochs + 1), all_val_losses, label='Validation Loss')
    plt.xlabel('Training Epoch')
    plt.ylabel('Loss Value (Negative Log Likelihood)')
    plt.title(f'Model V4 Training Process ({config["model_save_dir"].split("/")[-1]})') # Updated title
    plt.legend()
    plt.grid(True)
    loss_plot_path = os.path.join(config["model_save_dir"], "loss_curve.png")
    plt.savefig(loss_plot_path)
    print(f"Loss curve saved to: {loss_plot_path}")
    plt.close()

    # 修正缩进
    print("训练完成。")
    print(f"Best validation loss achieved: {best_val_loss:.4f}")
    print(f"Best model saved to: {model_save_path}")

    # --- Append Summary to Log File ---
    completion_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    model_version_name = config["model_save_dir"].split('/')[-1] # Extract name

    # Determine length description
    if config.get("variable_length", False):
        length_desc = f"Variable ({config['min_pred_minutes']}-{config['max_pred_minutes']} min)"
    else:
        length_desc = f"Fixed ({config['pred_minutes']} min)"
        
    summary_message = f"""
    =======================================================
    训练完成: {completion_time}
    模型版本: {model_version_name}
    训练配置: {length_desc}
    最佳验证损失 (Best Val Loss): {best_val_loss:.4f}
    最佳模型已保存至: {model_save_path}
    =======================================================
    """
    print(summary_message)

    log_file_path = os.path.join("models", "training_summary_log.txt")
    os.makedirs("models", exist_ok=True)

    try:
        with open(log_file_path, 'a') as log_f:
            log_f.write(summary_message + "\n")
        print(f"训练总结已追加到日志文件: {log_file_path}")
    except Exception as e:
        print(f"警告: 无法写入训练总结日志文件 {log_file_path}: {e}")


# --- Main Function (Adapted for V4) ---
def main():
    print("--- Entering main() function ---")
    parser = argparse.ArgumentParser(description='Train Autoregressive MDN (V4: Attention + Relative Goal) for Trajectory Prediction')
    parser.add_argument('--variable', action='store_true', help='Use variable length prediction mode')
    parser.add_argument('--no_embedding', action='store_true', help='Load data without embeddings (26-dim)')
    parser.add_argument('--zero_out_embedding', action='store_true',
                        help='Load 34-dim data but zero out the embedding part.')
    parser.add_argument('--minutes', type=int, default=40, choices=[5, 10, 15, 20, 30, 40], # 添加 40 到 choices
                        help='Single prediction length (minutes), only used if --variable is NOT set')
    parser.add_argument('--min_minutes', type=int, default=5,
                        help='Minimum prediction length (minutes), only used if --variable IS set')
    parser.add_argument('--max_minutes', type=int, default=15,
                        help='Maximum prediction length (minutes), only used if --variable IS set')
    parser.add_argument('--batch_size', type=int, default=256, # 默认值修改为 256
                        help='Training batch size')
    parser.add_argument('--train', action='store_true', help='Start model training')

    print("--- Parser defined, about to parse args --- ")
    try:
        args = parser.parse_args()
        print("--- Args parsed successfully --- ")
        print(f"Parsed args: {args}")
    except Exception as e:
        print(f"--- Error during parsing args: {e} ---")
        raise

    # Determine embedding usage based on flags for V4
    if args.no_embedding:
        use_embedding = False
        zero_out_embedding_flag = False
        print("Mode V4: --no_embedding specified. Will load 26-dim data.")
    elif args.zero_out_embedding:
        use_embedding = True
        zero_out_embedding_flag = True
        print("Mode V4: --zero_out_embedding specified. Will load 34-dim data and zero out last 8 dims.")
    else:
        use_embedding = True
        zero_out_embedding_flag = False
        print("Mode V4: Default. Will load 34-dim embedded data.")

    # Get config
    if args.variable:
        min_m = args.min_minutes
        max_m = args.max_minutes
        print(f"Mode V4: Variable Length ({min_m}-{max_m} min), Embedding Expected: {use_embedding}")
        config = get_config(min_pred_minutes=min_m, max_pred_minutes=max_m, batch_size=args.batch_size)
    else:
        m = args.minutes
        print(f"Mode V4: Fixed Length ({m} min), Embedding Expected: {use_embedding}")
        config = get_config(pred_minutes=m, batch_size=args.batch_size)

    config['zero_out_embedding'] = zero_out_embedding_flag

    print("--- Config --- ")
    for key, value in config.items():
        print(f"{key}: {value}")
    print("---------------")

    if args.train:
        train(config)
    else:
         print("Please use --train flag to start training.")
         parser.print_help()
    sys.exit(0) # Exit after successful completion or help message

if __name__ == "__main__":
    print("Starting Trajectory Predictor Training Script (V4)...")
    main() 