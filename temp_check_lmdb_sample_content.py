# -*- coding: utf-8 -*-
"""
创建时间: 2025-07-25
功能: 检查从LMDB加载的数据样本内容，特别是是否存在NaN或Inf值，以及数据范围是否合理。
输入: 
    - LMDB数据集 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/train)
    - 归一化统计文件 (data/processed_lmdb_obs_5min_pred_40min_v2_with_roi_seq/normalization_stats.pkl)
    - 配置文件 (configs/main_config.yaml, configs/models/pecnet_model.yaml)
输出: 
    - 打印数据样本的统计信息（min, max, mean），并报告是否检测到NaN/Inf。
处理方法:
    - 加载LMDBDataset并使用DataLoader读取批次。
    - 对每个张量检查NaN/Inf，并计算基本统计量。
作者: AI Assistant
"""

import torch
import os
import sys
import pickle
import numpy as np
import logging
from omegaconf import OmegaConf
from torch.utils.data import DataLoader

# 将项目根目录添加到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.utils.config_loader import load_config
from src.data.datasets import LMDBDataset

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def check_lmdb_samples():
    logging.info("--- 开始检查LMDB数据样本内容 ---")

    # 1. 加载配置
    main_config_path = 'configs/main_config.yaml'
    model_config_path = 'configs/models/pecnet_model.yaml'
    config = load_config(main_config_path, model_config_path)
    logging.info("配置加载成功。")

    # 2. 加载归一化统计数据 (尽管LMDBDataset声称已归一化，但这里需要它来了解预期范围)
    stats_path = config.data_params.stats_path_v2
    if not os.path.exists(stats_path):
        logging.error(f"错误: 归一化统计文件未找到: {stats_path}。请先运行数据预处理脚本。")
        sys.exit(1)
    with open(stats_path, 'rb') as f:
        normalization_stats = pickle.load(f)
    logging.info("归一化统计数据加载成功。")

    # 3. 初始化LMDBDataset和DataLoader
    try:
        train_dataset = LMDBDataset(config, lmdb_type='train')
        train_loader = DataLoader(
            train_dataset,
            batch_size=4, # 只加载少量样本进行检查
            shuffle=False,
            num_workers=0, # 不使用多进程，方便调试
            collate_fn=LMDBDataset.collate_fn
        )
        logging.info(f"训练集样本数: {len(train_dataset)}")
    except Exception as e:
        logging.error(f"LMDB数据集加载失败: {e}")
        sys.exit(1)

    # 4. 遍历并检查样本
    sample_count = 0
    for i, batch in enumerate(train_loader):
        if batch is None: # collate_fn 可能会返回 None
            logging.warning(f"批次 {i} 为空，跳过。")
            continue

        logging.info(f"\n--- 检查批次 {i} ---")
        for key, tensor in batch.items():
            logging.info(f"  张量: {key}")
            logging.info(f"    形状: {tensor.shape}")
            
            if torch.isnan(tensor).any():
                logging.error(f"    错误: {key} 中检测到 NaN 值！")
            if torch.isinf(tensor).any():
                logging.error(f"    错误: {key} 中检测到 Inf 值！")

            min_val = torch.min(tensor).item()
            max_val = torch.max(tensor).item()
            mean_val = torch.mean(tensor).item()
            logging.info(f"    范围: [{min_val:.4f}, {max_val:.4f}], 均值: {mean_val:.4f}")

        sample_count += batch['history'].shape[0]
        if sample_count >= 10: # 检查足够多的样本
            break
    
    logging.info("--- LMDB数据样本内容检查完成 ---")

if __name__ == '__main__':
    check_lmdb_samples() 