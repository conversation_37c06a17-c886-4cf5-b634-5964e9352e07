"""
创建时间: 2025/07/15
功能: 项目主入口脚本，负责解析命令行参数，根据指定模式（训练、评估、预测与可视化）调度执行相应的任务。
输入:
  - `--mode`: 字符串，指定要执行的操作模式。可选值包括 "train"、"evaluate"、"predict_and_visualize"。
  - `--config`: 字符串，主配置文件路径，默认为 `configs/main_config.yaml`。
  - `--model_config_name`: 字符串，模型特定配置文件的名称（位于 `configs/models/` 目录下）。例如 `v1_grid_classifier_model.yaml`。
  - `--sample_idx` (仅对 `predict_and_visualize` 模式): 整数，要可视化的LMDB样本索引，默认为 -1（随机选择）。
  - `--checkpoint` (仅对 `evaluate` 和 `predict_and_visualize` 模式): 字符串，模型检查点文件路径。
输出:
  - 根据不同模式，输出训练日志、模型检查点、评估报告或可视化结果。
原理及处理方法:
  1. 使用 `argparse` 解析命令行参数，包括操作模式和各种配置路径。
  2. 根据 `--mode` 参数的值，构建并执行相应的Python脚本命令。
  3. 对于每个子脚本调用，确保在命令前激活 `wargame` conda 环境。
  4. 捕获并处理子进程执行过程中可能发生的错误。
"""
import argparse
import subprocess
import sys
import os
from pathlib import Path

# 确保项目根目录在sys.path中，尽管子脚本会自己处理，但为了run.py自身的路径解析也加上
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

def run_script(script_path, mode, main_config, model_config_name=None, sample_idx=None, checkpoint=None):
    """
    运行指定的Python脚本，并传递命令行参数。
    """
    print(f"--- 启动任务: {mode} ---")
    
    # 激活 conda 环境的命令前缀
    # conda_activate_cmd = "conda activate wargame && " # 移除此行，因为我们将在外部激活环境

    cmd = [
        "/home/<USER>/anaconda3/envs/wargame/bin/python", # 使用 wargame 环境中的 python 解释器
        str(script_path),
        "--config", str(main_config)
    ]

    if model_config_name:
        cmd.extend(["--model_config_name", str(model_config_name)])
    
    if mode == "predict_and_visualize":
        if sample_idx is not None:
            cmd.extend(["--sample_idx", str(sample_idx)])
        # For visualization, we always need a checkpoint
        if checkpoint:
             cmd.extend(["--checkpoint", str(checkpoint)])
        else:
            print("警告: 预测与可视化模式下未提供模型检查点路径。请在配置文件中指定或通过命令行提供。")

    elif mode == "evaluate":
        if checkpoint:
            cmd.extend(["--checkpoint", str(checkpoint)])
        else:
            print("错误: 评估模式下必须提供模型检查点路径。")
            sys.exit(1)
    
    # 将命令列表转换为字符串，以便与 conda_activate_cmd 拼接
    # full_cmd = conda_activate_cmd + " ".join([f"'{arg}'" if ' ' in arg else arg for arg in cmd]) # 移除此行
    full_cmd = " ".join([f"'{arg}'" if ' ' in arg else arg for arg in cmd]) # 直接使用命令列表

    print(f"执行命令: {full_cmd}")
    try:
        # 使用 shell=True 来执行 conda activate
        # 不再捕获输出，而是直接显示到控制台
        process = subprocess.run(full_cmd, shell=True, check=True, text=True, 
                                 stdout=sys.stdout, stderr=sys.stderr)
        print(f"--- 任务 {mode} 完成 ---")
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败，错误码: {e.returncode}")
        print("标准输出:")
        print(e.stdout) # 错误发生时仍然打印捕获的输出
        print("标准错误:")
        print(e.stderr) # 错误发生时仍然打印捕获的输出
        sys.exit(e.returncode)
    except FileNotFoundError:
        print(f"错误: 脚本或Python解释器未找到。请检查路径设置。")
        sys.exit(1)
    except Exception as e:
        print(f"执行过程中发生未知错误: {e}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="主入口脚本，调度训练、评估或预测任务。")
    parser.add_argument('--mode', type=str, required=True, 
                        choices=['train', 'evaluate', 'predict_and_visualize'],
                        help='选择操作模式: train (训练), evaluate (评估), predict_and_visualize (预测与可视化)。')
    parser.add_argument('--config', type=str, default='configs/main_config.yaml', 
                        help='主配置文件路径。')
    parser.add_argument('--model_config_name', type=str, default=None, 
                        help='模型特定配置文件的名称（位于configs/models/下）。例如 `v1_grid_classifier_model.yaml`。')
    parser.add_argument('--sample_idx', type=int, default=None, 
                        help='(仅对 predict_and_visualize 模式有效) 要可视化的LMDB样本索引。如果为-1则随机选择。')
    parser.add_argument('--checkpoint', type=str, default=None, 
                        help='(仅对 evaluate 和 predict_and_visualize 模式有效) 模型检查点文件路径。')
    
    args = parser.parse_args()

    # 验证主配置文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 主配置文件未找到: {args.config}")
        sys.exit(1)
    
    if args.model_config_name and not os.path.exists(os.path.join('configs', 'models', args.model_config_name)):
        print(f"错误: 模型特定配置文件未找到: configs/models/{args.model_config_name}")
        sys.exit(1)

    # 根据模式调度任务
    if args.mode == 'train':
        # 训练模式，调用相应的训练脚本
        if not args.model_config_name:
            print("错误: 训练模式下必须指定 --model_config_name。")
            sys.exit(1)
        
        # 暂时只支持 V1 模型训练脚本
        if args.model_config_name == 'v1_grid_classifier_model.yaml':
            script_to_run = Path("scripts/training/03_train_v1_model.py")
        # TODO: Add other training scripts here when they are implemented
        elif args.model_config_name == 'v4_mdn_model.yaml': # 添加对MDN预测器V5的支持
            script_to_run = Path("scripts/training/05_train_mdn_predictor_v5.py")
        elif args.model_config_name == 'pecnet_model.yaml': # 添加对PECNet模型的支持
            script_to_run = Path("scripts/training/06_train_pecnet_model.py")
        elif args.model_config_name == 'v2_intent_guided_model.yaml': # 添加对V2模型的支持
            script_to_run = Path("scripts/training/04_train_v2_intent_guided.py")
        elif args.model_config_name == 'v3_masked_autoregressive_model.yaml': # 添加对V3模型的支持
            script_to_run = Path("scripts/training/07_train_v3_masked_model.py")
        # elif args.model_config_name == 'cnn_rnn_fusion_model.yaml':
        #     script_to_run = Path("scripts/training/03_train_cnn_rnn_fusion_v1.py") # Example
        # elif args.model_config_name == 'v4_mdn_model.yaml':
        #     script_to_run = Path("scripts/training/05_train_mdn_predictor_v5.py") # Example
        else:
            print(f"错误: 不支持的模型配置名称 '{args.model_config_name}' 进行训练。")
            sys.exit(1)
        
        run_script(script_to_run, args.mode, args.config, args.model_config_name)

    elif args.mode == 'evaluate':
        # 评估模式，调用评估脚本
        script_to_run = Path("scripts/evaluation/evaluate_model.py")
        if not args.checkpoint:
            print("错误: 评估模式下必须提供 --checkpoint 参数。")
            sys.exit(1)
        run_script(script_to_run, args.mode, args.config, args.model_config_name, checkpoint=args.checkpoint)
    
    elif args.mode == 'predict_and_visualize':
        # 预测与可视化模式，调用可视化脚本
        script_to_run = Path("scripts/visualization/predict_and_visualize.py")
        if not args.checkpoint:
            print("错误: 预测与可视化模式下必须提供 --checkpoint 参数。")
            sys.exit(1)
        run_script(script_to_run, args.mode, args.config, args.model_config_name, sample_idx=args.sample_idx, checkpoint=args.checkpoint)

if __name__ == '__main__':
    main() 