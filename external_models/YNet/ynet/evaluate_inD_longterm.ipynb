{"cells": [{"cell_type": "code", "execution_count": 1, "id": "determined-township", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import yaml\n", "import argparse\n", "import torch\n", "from model import YNet"]}, {"cell_type": "code", "execution_count": 2, "id": "finished-potential", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "ec0c68d1", "metadata": {}, "source": ["#### Some hyperparameters and settings"]}, {"cell_type": "code", "execution_count": 3, "id": "arabic-thickness", "metadata": {}, "outputs": [], "source": ["CONFIG_FILE_PATH = 'config/inD_longterm.yaml'  # yaml config file containing all the hyperparameters\n", "DATASET_NAME = 'ind'\n", "\n", "TEST_DATA_PATH = 'data/inD/test.pkl'\n", "TEST_IMAGE_PATH = 'data/inD/test'\n", "OBS_LEN = 5  # in timesteps\n", "PRED_LEN = 30  # in timesteps\n", "NUM_GOALS = 20  # K_e\n", "NUM_TRAJ = 1  # K_a\n", "\n", "ROUNDS = 3  # Y-net is stochastic. How often to evaluate the whole dataset\n", "BATCH_SIZE = 8"]}, {"cell_type": "markdown", "id": "6de88cfa", "metadata": {}, "source": ["#### Load config file and print hyperparameters"]}, {"cell_type": "code", "execution_count": 4, "id": "dangerous-cutting", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'resize': 0.33,\n", " 'batch_size': 8,\n", " 'viz_epoch': 10,\n", " 'learning_rate': 0.0001,\n", " 'num_epochs': 300,\n", " 'encoder_channels': [32, 32, 64, 64, 64],\n", " 'decoder_channels': [64, 64, 64, 32, 32],\n", " 'waypoints': [14, 29],\n", " 'temperature': 1.8,\n", " 'segmentation_model_fp': 'segmentation_models/inD_segmentation.pth',\n", " 'semantic_classes': 6,\n", " 'loss_scale': 1000,\n", " 'kernlen': 31,\n", " 'nsig': 4,\n", " 'use_features_only': <PERSON><PERSON><PERSON>,\n", " 'unfreeze': 100,\n", " 'use_TTST': True,\n", " 'rel_threshold': 0.002,\n", " 'use_CWS': True,\n", " 'CWS_params': {'sigma_factor': 6, 'ratio': 2, 'rot': True}}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(CONFIG_FILE_PATH) as file:\n", "    params = yaml.load(file, Loader=yaml.FullLoader)\n", "experiment_name = CONFIG_FILE_PATH.split('.yaml')[0].split('config/')[1]\n", "params"]}, {"cell_type": "markdown", "id": "amber-pressure", "metadata": {}, "source": ["#### Load preprocessed Data"]}, {"cell_type": "code", "execution_count": 5, "id": "german-feature", "metadata": {}, "outputs": [], "source": ["df_test = pd.read_pickle(TEST_DATA_PATH)"]}, {"cell_type": "code", "execution_count": 6, "id": "corporate-pharmacy", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trackId</th>\n", "      <th>frame</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>sceneId</th>\n", "      <th>metaId</th>\n", "      <th>frame_diff</th>\n", "      <th>recId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>98</td>\n", "      <td>6852</td>\n", "      <td>854.241010</td>\n", "      <td>141.705840</td>\n", "      <td>scene1</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>98</td>\n", "      <td>6877</td>\n", "      <td>853.749934</td>\n", "      <td>149.903018</td>\n", "      <td>scene1</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>98</td>\n", "      <td>6902</td>\n", "      <td>852.634186</td>\n", "      <td>158.528806</td>\n", "      <td>scene1</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>98</td>\n", "      <td>6927</td>\n", "      <td>851.607283</td>\n", "      <td>167.000131</td>\n", "      <td>scene1</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>98</td>\n", "      <td>6952</td>\n", "      <td>850.816207</td>\n", "      <td>175.933858</td>\n", "      <td>scene1</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   trackId  frame           x           y sceneId  metaId  frame_diff recId\n", "0       98   6852  854.241010  141.705840  scene1       0         1.0    00\n", "1       98   6877  853.749934  149.903018  scene1       0         1.0    00\n", "2       98   6902  852.634186  158.528806  scene1       0         1.0    00\n", "3       98   6927  851.607283  167.000131  scene1       0         1.0    00\n", "4       98   6952  850.816207  175.933858  scene1       0         1.0    00"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test.head()"]}, {"cell_type": "markdown", "id": "82b42ada", "metadata": {}, "source": ["#### Initiate model and load pretrained weights"]}, {"cell_type": "code", "execution_count": 7, "id": "harmful-colleague", "metadata": {}, "outputs": [], "source": ["model = YNet(obs_len=OBS_LEN, pred_len=PRED_LEN, params=params)"]}, {"cell_type": "code", "execution_count": 8, "id": "indoor-communist", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<All keys matched successfully>\n"]}], "source": ["model.load(f'pretrained_models/{experiment_name}_weights.pt')"]}, {"cell_type": "markdown", "id": "85d1d16d", "metadata": {}, "source": ["#### Evaluate model"]}, {"cell_type": "code", "execution_count": 9, "id": "optional-colleague", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Prepare Dataset: 100%|██████████████████████████████████████████████████████████████████| 1/1 [00:00<00:00, 501.29it/s]\n", "Round:   0%|                                                                                     | 0/3 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Preprocess data\n", "Start testing\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "Round:  33%|█████████████████████████▎                                                  | 1/3 [03:09<06:18, 189.02s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Round 0: \n", "Test ADE: 14.850406646728516 \n", "Test FDE: 19.687257766723633\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "Round:  67%|██████████████████████████████████████████████████▋                         | 2/3 [06:16<03:08, 188.58s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Round 1: \n", "Test ADE: 14.665905952453613 \n", "Test FDE: 19.303884506225586\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Round: 100%|████████████████████████████████████████████████████████████████████████████| 3/3 [09:24<00:00, 188.20s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Round 2: \n", "Test ADE: 14.900250434875488 \n", "Test FDE: 19.748842239379883\n", "\n", "\n", "Average performance over 3 rounds: \n", "Test ADE: 14.805521011352539 \n", "Test FDE: 19.579994837443035\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["model.evaluate(df_test, params, image_path=TEST_IMAGE_PATH,\n", "               batch_size=BATCH_SIZE, rounds=ROUNDS, \n", "               num_goals=NUM_GOALS, num_traj=NUM_TRAJ, device=None, dataset_name=DATASET_NAME)"]}, {"cell_type": "code", "execution_count": null, "id": "eb60a554", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:PECNet]", "language": "python", "name": "conda-env-PECNet-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 5}