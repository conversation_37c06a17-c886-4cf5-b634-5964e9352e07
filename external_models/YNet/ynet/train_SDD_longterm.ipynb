{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import yaml\n", "import argparse\n", "import torch\n", "from model import YNet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python --version"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Some hyperparameters and settings"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["CONFIG_FILE_PATH = 'ynet_additional_files/config/sdd_longterm.yaml'  # yaml config file containing all the hyperparameters\n", "EXPERIMENT_NAME = 'sdd_longterm'  # arbitrary name for this experiment\n", "DATASET_NAME = 'sdd'\n", "\n", "TRAIN_DATA_PATH = 'ynet_additional_files/data/SDD/train_longterm.pkl'\n", "TRAIN_IMAGE_PATH = 'ynet_additional_files/data/SDD/train'\n", "VAL_DATA_PATH = 'ynet_additional_files/data/SDD/test_longterm.pkl'\n", "VAL_IMAGE_PATH = 'ynet_additional_files/data/SDD/test'\n", "OBS_LEN = 5  # in timesteps\n", "PRED_LEN = 30  # in timesteps\n", "NUM_GOALS = 20  # K_e\n", "NUM_TRAJ = 1  # K_a\n", "\n", "BATCH_SIZE = 4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Load config file and print hyperparameters"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'resize': 0.25,\n", " 'batch_size': 8,\n", " 'viz_epoch': 10,\n", " 'learning_rate': 0.0001,\n", " 'num_epochs': 300,\n", " 'encoder_channels': [32, 32, 64, 64, 64],\n", " 'decoder_channels': [64, 64, 64, 32, 32],\n", " 'waypoints': [14, 29],\n", " 'temperature': 1.8,\n", " 'segmentation_model_fp': 'segmentation_models/SDD_segmentation.pth',\n", " 'semantic_classes': 6,\n", " 'loss_scale': 1000,\n", " 'kernlen': 31,\n", " 'nsig': 4,\n", " 'use_features_only': <PERSON><PERSON><PERSON>,\n", " 'unfreeze': 100,\n", " 'use_TTST': True,\n", " 'rel_threshold': 0.002,\n", " 'use_CWS': True,\n", " 'CWS_params': {'sigma_factor': 6, 'ratio': 2, 'rot': True}}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(CONFIG_FILE_PATH) as file:\n", "    params = yaml.load(file, Loader=yaml.FullLoader)\n", "experiment_name = CONFIG_FILE_PATH.split('.yaml')[0].split('config/')[1]\n", "params"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Load preprocessed Data"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "unsupported pickle protocol: 5", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-14-f344ee0bc499>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mdf_train\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_pickle\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mTRAIN_DATA_PATH\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mdf_val\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_pickle\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mVAL_DATA_PATH\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.7/site-packages/pandas/io/pickle.py\u001b[0m in \u001b[0;36mread_pickle\u001b[0;34m(filepath_or_buffer, compression)\u001b[0m\n\u001b[1;32m    179\u001b[0m             \u001b[0;31m# We want to silence any warnings about, e.g. moved modules.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    180\u001b[0m             \u001b[0mwarnings\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msimplefilter\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"ignore\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mWarning\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 181\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mf\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    182\u001b[0m     \u001b[0;32mexcept\u001b[0m \u001b[0mexcs_to_catch\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    183\u001b[0m         \u001b[0;31m# e.g.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: unsupported pickle protocol: 5"]}], "source": ["df_train = pd.read_pickle(TRAIN_DATA_PATH)\n", "df_val = pd.read_pickle(VAL_DATA_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_train.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Initiate model and load pretrained weights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = YNet(obs_len=OBS_LEN, pred_len=PRED_LEN, params=params)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Start training\n", "Note, the Val ADE and FDE are without TTST and CWS to save time. Therefore, the numbers will be worse than the final values."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.train(df_train, df_val, params, train_image_path=TRAIN_IMAGE_PATH, val_image_path=VAL_IMAGE_PATH,\n", "            experiment_name=EXPERIMENT_NAME, batch_size=BATCH_SIZE, num_goals=NUM_GOALS, num_traj=NUM_TRAJ, \n", "            device=None, dataset_name=DATASET_NAME)"]}], "metadata": {"kernelspec": {"display_name": "shot", "language": "python", "name": "shot"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 5}