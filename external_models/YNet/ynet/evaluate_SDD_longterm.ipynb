{"cells": [{"cell_type": "code", "execution_count": 1, "id": "determined-township", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import yaml\n", "import argparse\n", "import torch\n", "from model import YNet"]}, {"cell_type": "code", "execution_count": 2, "id": "finished-potential", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "9ad94e97", "metadata": {}, "source": ["#### Some hyperparameters and settings"]}, {"cell_type": "code", "execution_count": 3, "id": "arabic-thickness", "metadata": {}, "outputs": [], "source": ["CONFIG_FILE_PATH = 'config/sdd_longterm.yaml'  # yaml config file containing all the hyperparameters\n", "DATASET_NAME = 'sdd'\n", "\n", "TEST_DATA_PATH = 'data/SDD/test_longterm.pkl'\n", "TEST_IMAGE_PATH = 'data/SDD/test'\n", "OBS_LEN = 5  # in timesteps\n", "PRED_LEN = 30  # in timesteps\n", "NUM_GOALS = 20  # K_e\n", "NUM_TRAJ = 1  # K_a\n", "\n", "ROUNDS = 3  # Y-net is stochastic. How often to evaluate the whole dataset\n", "BATCH_SIZE = 8"]}, {"cell_type": "markdown", "id": "8a2a9207", "metadata": {}, "source": ["#### Load config file and print hyperparameters"]}, {"cell_type": "code", "execution_count": 4, "id": "dangerous-cutting", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'resize': 0.25,\n", " 'batch_size': 8,\n", " 'viz_epoch': 10,\n", " 'learning_rate': 0.0001,\n", " 'num_epochs': 300,\n", " 'encoder_channels': [32, 32, 64, 64, 64],\n", " 'decoder_channels': [64, 64, 64, 32, 32],\n", " 'waypoints': [14, 29],\n", " 'temperature': 1.8,\n", " 'segmentation_model_fp': 'segmentation_models/SDD_segmentation.pth',\n", " 'semantic_classes': 6,\n", " 'loss_scale': 1000,\n", " 'kernlen': 31,\n", " 'nsig': 4,\n", " 'use_features_only': <PERSON><PERSON><PERSON>,\n", " 'unfreeze': 100,\n", " 'use_TTST': True,\n", " 'rel_threshold': 0.002,\n", " 'use_CWS': True,\n", " 'CWS_params': {'sigma_factor': 6, 'ratio': 2, 'rot': True}}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(CONFIG_FILE_PATH) as file:\n", "    params = yaml.load(file, Loader=yaml.FullLoader)\n", "experiment_name = CONFIG_FILE_PATH.split('.yaml')[0].split('config/')[1]\n", "params"]}, {"cell_type": "markdown", "id": "amber-pressure", "metadata": {}, "source": ["#### Load preprocessed Data"]}, {"cell_type": "code", "execution_count": 5, "id": "german-feature", "metadata": {}, "outputs": [], "source": ["df_test = pd.read_pickle(TEST_DATA_PATH)"]}, {"cell_type": "code", "execution_count": 6, "id": "corporate-pharmacy", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trackId</th>\n", "      <th>frame</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>sceneId</th>\n", "      <th>metaId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5</td>\n", "      <td>311</td>\n", "      <td>1608.0</td>\n", "      <td>326.5</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>341</td>\n", "      <td>1608.0</td>\n", "      <td>326.5</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>371</td>\n", "      <td>1608.0</td>\n", "      <td>326.5</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>401</td>\n", "      <td>1608.0</td>\n", "      <td>326.5</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>431</td>\n", "      <td>1608.0</td>\n", "      <td>326.5</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   trackId  frame       x      y  sceneId  metaId\n", "0        5    311  1608.0  326.5  coupa_0       0\n", "1        5    341  1608.0  326.5  coupa_0       0\n", "2        5    371  1608.0  326.5  coupa_0       0\n", "3        5    401  1608.0  326.5  coupa_0       0\n", "4        5    431  1608.0  326.5  coupa_0       0"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test.head()"]}, {"cell_type": "markdown", "id": "0efc7541", "metadata": {}, "source": ["#### Initiate model and load pretrained weights"]}, {"cell_type": "code", "execution_count": 7, "id": "harmful-colleague", "metadata": {}, "outputs": [], "source": ["model = YNet(obs_len=OBS_LEN, pred_len=PRED_LEN, params=params)"]}, {"cell_type": "code", "execution_count": 8, "id": "indoor-communist", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<All keys matched successfully>\n"]}], "source": ["model.load(f'pretrained_models/{experiment_name}_weights.pt')"]}, {"cell_type": "markdown", "id": "60033a1f", "metadata": {}, "source": ["#### Evaluate model"]}, {"cell_type": "code", "execution_count": 9, "id": "optional-colleague", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preprocess data\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Prepare Dataset: 100%|████████████████████████████████████████████████████████████████| 11/11 [00:00<00:00, 866.95it/s]\n", "Round:   0%|                                                                                     | 0/3 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Start testing\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "Round:  33%|█████████████████████████▎                                                  | 1/3 [09:19<18:39, 559.50s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Round 0: \n", "Test ADE: 46.825557708740234 \n", "Test FDE: 64.77117156982422\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\r", "Round:  67%|██████████████████████████████████████████████████▋                         | 2/3 [18:53<09:23, 563.81s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Round 1: \n", "Test ADE: 46.7724723815918 \n", "Test FDE: 65.5625991821289\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Round: 100%|████████████████████████████████████████████████████████████████████████████| 3/3 [28:27<00:00, 569.01s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Round 2: \n", "Test ADE: 47.00978469848633 \n", "Test FDE: 66.36463928222656\n", "\n", "\n", "Average performance over 3 rounds: \n", "Test ADE: 46.86927159627279 \n", "Test FDE: 65.56613667805989\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["model.evaluate(df_test, params, image_path=TEST_IMAGE_PATH,\n", "               batch_size=BATCH_SIZE, rounds=ROUNDS, \n", "               num_goals=NUM_GOALS, num_traj=NUM_TRAJ, device=None, dataset_name=DATASET_NAME)"]}, {"cell_type": "code", "execution_count": null, "id": "provincial-plymouth", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:PECNet]", "language": "python", "name": "conda-env-PECNet-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 5}