{"cells": [{"cell_type": "code", "execution_count": 1, "id": "necessary-coffee", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import yaml\n", "import argparse\n", "import torch\n", "from model import YNet"]}, {"cell_type": "code", "execution_count": 2, "id": "nominated-serve", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "85d6bf06", "metadata": {}, "source": ["#### Some hyperparameters and settings"]}, {"cell_type": "code", "execution_count": 3, "id": "external-stock", "metadata": {}, "outputs": [], "source": ["CONFIG_FILE_PATH = 'config/sdd_trajnet.yaml'  # yaml config file containing all the hyperparameters\n", "DATASET_NAME = 'sdd'\n", "\n", "TEST_DATA_PATH = 'data/SDD/test_trajnet.pkl'\n", "TEST_IMAGE_PATH = 'data/SDD/test'  # only needed for YNet, PECNet ignores this value\n", "OBS_LEN = 8  # in timesteps\n", "PRED_LEN = 12  # in timesteps\n", "NUM_GOALS = 20  # K_e\n", "NUM_TRAJ = 1  # K_a\n", "\n", "ROUNDS = 1  # Y-net is stochastic. How often to evaluate the whole dataset\n", "BATCH_SIZE = 8"]}, {"cell_type": "markdown", "id": "1d9dfa62", "metadata": {}, "source": ["#### Load config file and print hyperparameters"]}, {"cell_type": "code", "execution_count": 4, "id": "juvenile-factory", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'resize': 0.25,\n", " 'batch_size': 8,\n", " 'viz_epoch': 10,\n", " 'learning_rate': 0.0001,\n", " 'num_epochs': 300,\n", " 'encoder_channels': [32, 32, 64, 64, 64],\n", " 'decoder_channels': [64, 64, 64, 32, 32],\n", " 'waypoints': [11],\n", " 'temperature': 1.0,\n", " 'segmentation_model_fp': 'segmentation_models/SDD_segmentation.pth',\n", " 'semantic_classes': 6,\n", " 'loss_scale': 1000,\n", " 'kernlen': 31,\n", " 'nsig': 4,\n", " 'use_features_only': <PERSON><PERSON><PERSON>,\n", " 'unfreeze': 150,\n", " 'use_TTST': True,\n", " 'rel_threshold': 0.01,\n", " 'use_CWS': <PERSON><PERSON><PERSON>,\n", " 'CWS_params': 'None'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(CONFIG_FILE_PATH) as file:\n", "    params = yaml.load(file, Loader=yaml.FullLoader)\n", "experiment_name = CONFIG_FILE_PATH.split('.yaml')[0].split('config/')[1]\n", "params"]}, {"cell_type": "markdown", "id": "stylish-reserve", "metadata": {}, "source": ["#### Load preprocessed Data"]}, {"cell_type": "code", "execution_count": 5, "id": "isolated-advertiser", "metadata": {}, "outputs": [], "source": ["df_test = pd.read_pickle(TEST_DATA_PATH)"]}, {"cell_type": "code", "execution_count": 6, "id": "finished-lotus", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>trackId</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>sceneId</th>\n", "      <th>metaId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>28.0</td>\n", "      <td>1539.5</td>\n", "      <td>578.0</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>12</td>\n", "      <td>28.0</td>\n", "      <td>1484.5</td>\n", "      <td>576.0</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>24</td>\n", "      <td>28.0</td>\n", "      <td>1484.5</td>\n", "      <td>576.0</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>36</td>\n", "      <td>28.0</td>\n", "      <td>1459.5</td>\n", "      <td>571.0</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>48</td>\n", "      <td>28.0</td>\n", "      <td>1432.5</td>\n", "      <td>569.0</td>\n", "      <td>coupa_0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   frame  trackId       x      y  sceneId  metaId\n", "0      0     28.0  1539.5  578.0  coupa_0       0\n", "1     12     28.0  1484.5  576.0  coupa_0       0\n", "2     24     28.0  1484.5  576.0  coupa_0       0\n", "3     36     28.0  1459.5  571.0  coupa_0       0\n", "4     48     28.0  1432.5  569.0  coupa_0       0"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test.head()"]}, {"cell_type": "markdown", "id": "6b9046d1", "metadata": {}, "source": ["#### Initiate model and load pretrained weights"]}, {"cell_type": "code", "execution_count": 7, "id": "graphic-wales", "metadata": {}, "outputs": [], "source": ["model = YNet(obs_len=OBS_LEN, pred_len=PRED_LEN, params=params)"]}, {"cell_type": "code", "execution_count": 8, "id": "impaired-genre", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<All keys matched successfully>\n"]}], "source": ["model.load(f'pretrained_models/{experiment_name}_weights.pt')"]}, {"cell_type": "markdown", "id": "3de13533", "metadata": {}, "source": ["#### Evaluate model"]}, {"cell_type": "code", "execution_count": 9, "id": "labeled-permission", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preprocess data\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Prepare Dataset: 100%|████████████████████████████████████████████████████████████████| 17/17 [00:00<00:00, 897.17it/s]\n", "Round:   0%|                                                                                     | 0/1 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Start testing\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Round: 100%|███████████████████████████████████████████████████████████████████████████| 1/1 [27:40<00:00, 1660.86s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Round 0: \n", "Test ADE: 7.867360591888428 \n", "Test FDE: 11.998600006103516\n", "\n", "\n", "Average performance over 1 rounds: \n", "Test ADE: 7.867360591888428 \n", "Test FDE: 11.998600006103516\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["model.evaluate(df_test, params, image_path=TEST_IMAGE_PATH,\n", "               batch_size=BATCH_SIZE, rounds=ROUNDS, \n", "               num_goals=NUM_GOALS, num_traj=NUM_TRAJ, device=None, dataset_name=DATASET_NAME)"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:PECNet]", "language": "python", "name": "conda-env-PECNet-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 5}