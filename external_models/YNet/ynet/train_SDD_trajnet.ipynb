{"cells": [{"cell_type": "code", "execution_count": null, "id": "determined-township", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import yaml\n", "import argparse\n", "import torch\n", "from model import YNet"]}, {"cell_type": "code", "execution_count": null, "id": "finished-potential", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "949fa617", "metadata": {}, "source": ["#### Some hyperparameters and settings"]}, {"cell_type": "code", "execution_count": null, "id": "arabic-thickness", "metadata": {}, "outputs": [], "source": ["CONFIG_FILE_PATH = 'config/sdd_trajnet.yaml'  # yaml config file containing all the hyperparameters\n", "EXPERIMENT_NAME = 'sdd_trajnet'  # arbitrary name for this experiment\n", "DATASET_NAME = 'sdd'\n", "\n", "TRAIN_DATA_PATH = 'data/SDD/train_trajnet.pkl'\n", "TRAIN_IMAGE_PATH = 'data/SDD/train'\n", "VAL_DATA_PATH = 'data/SDD/test_trajnet.pkl'\n", "VAL_IMAGE_PATH = 'data/SDD/test'\n", "OBS_LEN = 8  # in timesteps\n", "PRED_LEN = 12  # in timesteps\n", "NUM_GOALS = 20  # K_e\n", "NUM_TRAJ = 1  # K_a\n", "\n", "BATCH_SIZE = 4"]}, {"cell_type": "markdown", "id": "5a0f01ec", "metadata": {}, "source": ["#### Load config file and print hyperparameters"]}, {"cell_type": "code", "execution_count": null, "id": "dangerous-cutting", "metadata": {}, "outputs": [], "source": ["with open(CONFIG_FILE_PATH) as file:\n", "    params = yaml.load(file, Loader=yaml.FullLoader)\n", "experiment_name = CONFIG_FILE_PATH.split('.yaml')[0].split('config/')[1]\n", "params"]}, {"cell_type": "markdown", "id": "amber-pressure", "metadata": {}, "source": ["#### Load preprocessed Data"]}, {"cell_type": "code", "execution_count": null, "id": "german-feature", "metadata": {}, "outputs": [], "source": ["df_train = pd.read_pickle(TRAIN_DATA_PATH)\n", "df_val = pd.read_pickle(VAL_DATA_PATH)"]}, {"cell_type": "code", "execution_count": null, "id": "corporate-pharmacy", "metadata": {}, "outputs": [], "source": ["df_train.head()"]}, {"cell_type": "markdown", "id": "e3b20057", "metadata": {}, "source": ["#### Initiate model and load pretrained weights"]}, {"cell_type": "code", "execution_count": null, "id": "harmful-colleague", "metadata": {}, "outputs": [], "source": ["model = YNet(obs_len=OBS_LEN, pred_len=PRED_LEN, params=params)"]}, {"cell_type": "markdown", "id": "cb4e880d", "metadata": {}, "source": ["#### Start training\n", "Note, the Val ADE and FDE are without TTST and CWS to save time. Therefore, the numbers will be worse than the final values."]}, {"cell_type": "code", "execution_count": null, "id": "optional-colleague", "metadata": {}, "outputs": [], "source": ["model.train(df_train, df_val, params, train_image_path=TRAIN_IMAGE_PATH, val_image_path=VAL_IMAGE_PATH,\n", "            experiment_name=EXPERIMENT_NAME, batch_size=BATCH_SIZE, num_goals=NUM_GOALS, num_traj=NUM_TRAJ, \n", "            device=None, dataset_name=DATASET_NAME)"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:PECNet]", "language": "python", "name": "conda-env-PECNet-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 5}